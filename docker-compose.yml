version: '3.8'

services:
  # Ứng dụng Express
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: thotran-chatbot-api
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
      # Weaviate
      - WEAVIATE_HOST=${WEAVIATE_HOST:-weaviate}
      - WEAVIATE_HTTP_PORT=${WEAVIATE_HTTP_PORT:-8080}
      - WEAVIATE_GRPC_PORT=${WEAVIATE_GRPC_PORT:-50051}
      - WEAVIATE_SECURE=${WEAVIATE_SECURE:-false}
      - WEAVIATE_API_KEY=${WEAVIATE_API_KEY}
      # OpenAI
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      # Google
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
      # OpenRouter
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      # Supabase
      - SUPABASE_URL=${SUPABASE_URL:-https://vhduizefoibsipsiraqf.supabase.co}
      - SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
      - SUPABASE_SERVICE_KEY=${SUPABASE_SERVICE_KEY}
      # PostgreSQL
      - PG_CONNECTION_STRING=${PG_CONNECTION_STRING}
      # Langfuse
      - LANGFUSE_PUBLIC_KEY=${LANGFUSE_PUBLIC_KEY}
      - LANGFUSE_SECRET_KEY=${LANGFUSE_SECRET_KEY}
      - LANGFUSE_BASEURL=${LANGFUSE_BASEURL:-https://cloud.langfuse.com}
      # Mastra
      - USE_MASTRA_CLIENT=${USE_MASTRA_CLIENT:-false}
      - MASTRA_SERVER_URL=${MASTRA_SERVER_URL:-http://localhost:4111}
      # Redis/Dragonfly
      - REDIS_HOST=dragonfly
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD:-tvqbjr0pnrnrluxtf9zdqkvgtoykpkb0}
    volumes:
      - ./logs:/app/logs
    depends_on:
      - dragonfly

  # Dragonfly (Redis alternative)
  dragonfly:
    image: 'docker.dragonflydb.io/dragonflydb/dragonfly'
    container_name: thotran-dragonfly
    restart: unless-stopped
    ulimits:
      memlock: -1
    ports:
      - "6379:6379"
    volumes:
      - dragonfly_data:/data
    command: --requirepass ${REDIS_PASSWORD:-tvqbjr0pnrnrluxtf9zdqkvgtoykpkb0}

volumes:
  dragonfly_data:
