# CẤU HÌNH TOOLS PRODUCTION - MOOLY.VN

## 📋 TỔNG QUAN

Hệ thống đã được tối ưu hóa để sử dụng **CHỈ CÁC TOOLS CẦN THIẾT** cho production, tránh trùng lặp và đảm bảo hiệu suất tối ưu.

## 🚀 TOOLS PRODUCTION - SỬ DỤNG DUY NHẤT

### 1. **ORDER TOOLS** (Quản lý đơn hàng)

#### ✅ `createOrderProductionTool` - TOOL DUY NHẤT TẠO ĐƠN HÀNG
- **File**: `src/mastra/tools/order/create-order-production.ts`
- **Chức năng**:
  - Tự động lấy giá chính xác từ database (sản phẩm có/không có biến thể)
  - Tự động tối ưu hóa khuyến mãi và vận chuyển
  - Kiểm tra tồn kho
  - Tạo đơn hàng với đầy đủ thông tin
  - Tích hợp chatbot info
- **Thay thế**: `createProductOrderTool`, `optimizeOrderTool`, `createOptimizedOrderTool`, `smartOrderCreationTool`

#### ✅ `getOrderByCodeTool` - Lấy thông tin đơn hàng
#### ✅ `updateOrderStatusTool` - Cập nhật trạng thái đơn hàng
#### ✅ `cancelOrderTool` - Hủy đơn hàng
#### ✅ `trackOrderTool` - Theo dõi đơn hàng

### 2. **PRODUCT TOOLS** (Quản lý sản phẩm)

#### ✅ `getProductDetailsProductionTool` - TOOL DUY NHẤT THÔNG TIN SẢN PHẨM
- **File**: `src/mastra/tools/product/get-product-details-production.ts`
- **Chức năng**:
  - Nhận vào product_id hoặc sku (hoặc cả hai)
  - Tự động xử lý logic tìm kiếm trong products và product_variants
  - Hiển thị thông tin giá chi tiết (giá gốc, giá sale, % giảm giá)
  - Kiểm tra tồn kho và trạng thái sản phẩm
  - Xử lý cả sản phẩm đơn giản và sản phẩm có biến thể
  - Tối ưu hóa để tiết kiệm token và cung cấp thông tin chính xác
- **Thay thế**: `getProductPricingProductionTool`, `getProductBySkuProductionTool`, `getProductDetailsTool`, `getProductAvailabilityTool`, `getAccuratePricingTool`

#### ✅ `searchProductsTool` - Tìm kiếm sản phẩm
#### ✅ `syncProductsTool` - Đồng bộ sản phẩm
#### ✅ `getProductCategoriesTool` - Lấy danh mục sản phẩm

### 3. **SUPPORT TOOLS** (Hỗ trợ khách hàng)
- `getFaqsTool`
- `getReturnPolicyTool`
- `getWarrantyInfoTool`
- `registerComplaintTool`
- `humanHandoffTool`
- `collectCustomerInfoTool`
- `detectSpamTool`

### 4. **PROMOTION TOOLS** (Khuyến mãi)
- `getPromotionsTool`

## ❌ DEPRECATED TOOLS - KHÔNG SỬ DỤNG

### Order Tools (Đã deprecated)
- ~~`optimizeOrderTool`~~ → Sử dụng `createOrderProductionTool`
- ~~`createOptimizedOrderTool`~~ → Sử dụng `createOrderProductionTool`
- ~~`smartOrderCreationTool`~~ → Sử dụng `createOrderProductionTool`

### Product Tools (Đã deprecated)
- ~~`getProductPricingProductionTool`~~ → Sử dụng `getProductDetailsProductionTool`
- ~~`getProductBySkuProductionTool`~~ → Sử dụng `getProductDetailsProductionTool`
- ~~`getProductDetailsTool`~~ → Sử dụng `getProductDetailsProductionTool`
- ~~`getProductAvailabilityTool`~~ → Sử dụng `getProductDetailsProductionTool`
- ~~`getAccuratePricingTool`~~ → Sử dụng `getProductDetailsProductionTool`

## 🔧 CẤU HÌNH AGENT

### E-commerce Agent (Production)
```typescript
export const ecommerceAgent = new Agent({
  name: "E-commerce Assistant Agent",
  tools: {
    // Product Tools
    searchProductsTool,
    getProductDetailsProductionTool,

    // Order Tools
    createProductOrderTool,
    createOrderProductionTool,
    trackOrderTool,
    getOrderByCodeTool,
    updateOrderStatusTool,
    cancelOrderTool,

    // Support Tools
    getReturnPolicyTool,
    getWarrantyInfoTool,
    registerComplaintTool,
    humanHandoffTool,
    collectCustomerInfoTool,
    getFaqsTool,
    detectSpamTool,
    getPromotionsTool,
  },
});
```

## 📊 WORKFLOW SẢN XUẤT

### 1. Quy trình tư vấn sản phẩm:
1. `searchProductsTool` - Tìm kiếm sản phẩm
2. `getProductPricingProductionTool` - Hiển thị thông tin chi tiết và giá chính xác

### 2. Quy trình tạo đơn hàng:
1. `createOrderProductionTool` - Tạo đơn hàng (tự động tối ưu hóa)
   - Tự động lấy giá từ database
   - Tự động áp dụng khuyến mãi tốt nhất
   - Tự động chọn vận chuyển tối ưu
   - Kiểm tra tồn kho
   - Tạo đơn hàng hoàn chỉnh

### 3. Quy trình theo dõi đơn hàng:
1. `trackOrderTool` hoặc `getOrderByCodeTool` - Lấy thông tin đơn hàng
2. `updateOrderStatusTool` - Cập nhật trạng thái (nếu cần)

## 🎯 LỢI ÍCH

### ✅ Tối ưu hóa hiệu suất:
- Giảm số lượng tools từ 15+ xuống 8 tools chính
- Loại bỏ trùng lặp chức năng
- Tăng tốc độ xử lý

### ✅ Đơn giản hóa maintenance:
- Chỉ cần maintain 2 tools chính: `createOrderProductionTool` và `getProductPricingProductionTool`
- Dễ dàng debug và fix lỗi
- Code base sạch hơn

### ✅ Đảm bảo tính nhất quán:
- Logic tính giá thống nhất
- Quy trình tạo đơn hàng chuẩn hóa
- Tự động áp dụng tối ưu hóa

## 🚨 LƯU Ý QUAN TRỌNG

1. **KHÔNG sử dụng các deprecated tools** trong production
2. **CHỈ sử dụng** `createOrderProductionTool` để tạo đơn hàng
3. **CHỈ sử dụng** `getProductDetailsProductionTool` để lấy thông tin sản phẩm
4. Các tools cũ được giữ lại chỉ để **tương thích ngược** trong quá trình migration

## 📝 MIGRATION CHECKLIST

- [x] Tạo `createOrderProductionTool` tích hợp đầy đủ tính năng
- [x] Tạo `getProductDetailsProductionTool` thay thế getProductPricingTool và getProductBySkuTool
- [x] Cập nhật agent sử dụng tools mới
- [x] Đánh dấu deprecated cho tools cũ
- [x] Tạo documentation hướng dẫn sử dụng

---

**Cập nhật lần cuối**: 2024-01-XX
**Phiên bản**: Production v1.0
**Trạng thái**: ✅ Sẵn sàng production
