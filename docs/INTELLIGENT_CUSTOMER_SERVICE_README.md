# 🤖 Intelligent Customer Service Agent - Hướng Dẫn Sử Dụng

## 🎯 Tổng Quan

**Intelligent Customer Service Agent** là một hệ thống AI chăm sóc khách hàng thông minh được xây dựng trên nền tảng <PERSON>, tận dụng tối đa các tool hiện có từ ecommerceAgent và bổ sung các tính năng thông minh:

### ✨ Tính Năng Nổi Bật

- 🧠 **Phân tích ý định và cảm xúc** khách hàng tự động
- 🎯 **Tư duy giải quyết vấn đề** như nhân viên có kinh nghiệm  
- 💬 **Giao tiếp thông minh** điều chỉnh theo ngữ cảnh
- 🛠️ **Tận dụng 14 tools** từ ecommerceAgent
- 🧠 **Memory system mạnh mẽ** với semantic search
- ⚡ **Workflow thông minh** với human handoff
- 📊 **<PERSON> dõi hiệu suất** và dự đoán mức độ hài lòng

## 🏗️ Kiến Trúc Hệ Thống

```mermaid
graph TB
    Customer[👤 Khách Hàng] --> API[🌐 API Endpoint]
    API --> Analysis[🧠 Intent & Emotion Analysis]
    Analysis --> Agent[🤖 Intelligent Agent]
    
    Agent --> Memory[🧠 Memory System]
    Agent --> Tools[🛠️ 14 Production Tools]
    Agent --> Workflow[⚡ Smart Workflow]
    
    Tools --> ProductTools[🛍️ Product Tools]
    Tools --> OrderTools[📦 Order Tools]
    Tools --> SupportTools[🎧 Support Tools]
    
    ProductTools --> Supabase[(🗄️ Supabase)]
    OrderTools --> Supabase
    SupportTools --> Supabase
    
    Memory --> PostgreSQL[(🐘 PostgreSQL)]
    Memory --> PgVector[(🔍 PgVector)]
    
    Workflow --> Response[📝 Intelligent Response]
    Workflow --> Escalation[👨‍💼 Human Handoff]
```

## 🚀 Cài Đặt và Cấu Hình

### 1. Dependencies (Đã có sẵn)
Tất cả dependencies đã được cài đặt trong dự án hiện tại.

### 2. Environment Variables (Đã cấu hình)
Sử dụng cấu hình hiện tại từ `.env`.

### 3. Database (Đã sẵn sàng)
Sử dụng PostgreSQL và Supabase hiện tại.

## 📡 API Endpoints

### 1. Generate Response (Khuyến nghị)
```http
POST /api/intelligent-customer-service/generate
```

**Request Body:**
```json
{
  "message": "Tôi muốn tìm áo thun nam size L màu đen",
  "threadId": "thread-123",
  "resourceId": "user-456", 
  "tenantId": "tenant-789",
  "botId": "bot-abc",
  "customerId": "customer-def",
  "channel": "facebook",
  "urgency": "medium",
  "conversationHistory": []
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "response": "Chào bạn! Tôi sẽ giúp bạn tìm áo thun nam size L màu đen...",
    "analysis": {
      "intent": "product_inquiry",
      "emotion": "neutral", 
      "complexity": "simple",
      "urgency": "medium"
    },
    "performance": {
      "processingTime": 1250,
      "confidence": 0.95,
      "predictedSatisfaction": 4.3,
      "needsEscalation": false
    },
    "toolsUsed": [
      {
        "name": "searchProductsTool",
        "args": {"query": "áo thun nam size L màu đen"}
      }
    ],
    "context": {
      "threadId": "thread-123",
      "tenantId": "tenant-789",
      "botId": "bot-abc"
    },
    "recommendations": {
      "nextSteps": ["continue_conversation"],
      "followUpSuggestions": []
    }
  }
}
```

### 2. Workflow Execution
```http
POST /api/intelligent-customer-service/workflow
```

**Request Body:** (Giống như endpoint generate)

**Response:**
```json
{
  "success": true,
  "data": {
    "response": "Phản hồi từ workflow...",
    "escalated": false,
    "confidence": 0.92,
    "actions": ["ai_response_generated"],
    "nextSteps": ["continue_conversation"],
    "customerSatisfaction": 4.2,
    "toolsUsed": ["searchProductsTool"],
    "processingTime": 1800,
    "workflowId": "workflow-run-123"
  }
}
```

### 3. Health Check
```http
GET /api/intelligent-customer-service/health
```

## 🛠️ Tools Được Sử Dụng

### 🔍 Product Tools
- `searchProductsTool` - Tìm kiếm sản phẩm thông minh
- `getProductDetailsProductionTool` - Chi tiết sản phẩm và giá

### 📦 Order Tools  
- `createOrderTool` - Tạo đơn hàng
- `trackOrderTool` - Theo dõi đơn hàng
- `getOrderByCodeTool` - Lấy thông tin đơn hàng
- `updateOrderStatusTool` - Cập nhật trạng thái
- `cancelOrderTool` - Hủy đơn hàng

### 🎁 Promotion Tools
- `getPromotionsTool` - Lấy khuyến mãi

### 🛡️ Support Tools
- `getReturnPolicyTool` - Chính sách đổi trả
- `registerComplaintTool` - Đăng ký khiếu nại
- `humanHandoffTool` - Chuyển nhân viên
- `collectCustomerInfoTool` - Thu thập thông tin
- `getFaqsTool` - Câu hỏi thường gặp
- `detectSpamTool` - Phát hiện spam

## 🧠 Phân Tích Thông Minh

### Intent Detection
- `product_inquiry` - Hỏi về sản phẩm
- `order_status` - Kiểm tra đơn hàng
- `complaint` - Khiếu nại
- `support_request` - Yêu cầu hỗ trợ
- `purchase` - Mua hàng
- `consultation` - Tư vấn
- `return` - Đổi trả

### Emotion Analysis
- `happy` - Vui vẻ, hài lòng
- `neutral` - Trung tính
- `frustrated` - Thất vọng
- `angry` - Tức giận
- `confused` - Bối rối
- `excited` - Hào hứng

### Complexity Assessment
- `simple` - Đơn giản, có thể xử lý bằng AI
- `medium` - Trung bình, cần logic phức tạp
- `complex` - Phức tạp, cần chuyển nhân viên

## 🔄 Workflow Thông Minh

### Step 1: Customer Analysis
- Phân tích ý định và cảm xúc
- Đánh giá độ phức tạp
- Phát hiện spam/quấy rối
- Xác định cần escalation không

### Step 2: AI Response Generation
- Tạo phản hồi với context đầy đủ
- Sử dụng tools phù hợp
- Điều chỉnh theo cảm xúc khách hàng
- Human handoff nếu cần

### Step 3: Evaluation
- Đánh giá chất lượng phản hồi
- Dự đoán mức độ hài lòng
- Đề xuất cải thiện

## 📊 Metrics và Monitoring

### Performance Metrics
- **Processing Time**: < 3 giây
- **Confidence Score**: 0.85 - 0.95
- **Customer Satisfaction**: > 4.0/5
- **Escalation Rate**: < 10%

### Quality Indicators
- **Intent Accuracy**: > 95%
- **Emotion Detection**: > 90%
- **Tool Usage**: Tối ưu theo context
- **Response Relevance**: > 95%

## 🎯 Ví Dụ Sử Dụng

### Tư vấn sản phẩm
```javascript
const response = await fetch('/api/intelligent-customer-service/generate', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    message: "Tôi muốn mua áo thun nam, bạn có gợi ý nào không?",
    tenantId: "tenant-123",
    customerId: "customer-456"
  })
});
```

### Theo dõi đơn hàng
```javascript
const response = await fetch('/api/intelligent-customer-service/generate', {
  method: 'POST', 
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    message: "Đơn hàng #DH12345 của tôi đến đâu rồi?",
    tenantId: "tenant-123",
    customerId: "customer-456",
    urgency: "high"
  })
});
```

### Khiếu nại
```javascript
const response = await fetch('/api/intelligent-customer-service/generate', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    message: "Sản phẩm tôi nhận được bị lỗi, tôi muốn đổi trả",
    tenantId: "tenant-123", 
    customerId: "customer-456",
    urgency: "high"
  })
});
```

## 🔧 Tùy Chỉnh và Mở Rộng

### Thêm Intent mới
1. Cập nhật `analyzeCustomerIntent()` trong `intelligent-customer-service.ts`
2. Thêm logic xử lý trong system prompt
3. Test và validate

### Thêm Tool mới
1. Tạo tool trong thư mục `tools/`
2. Import vào agent
3. Cập nhật system prompt với hướng dẫn sử dụng

### Tùy chỉnh Workflow
1. Chỉnh sửa `intelligent-customer-service.ts` trong `workflows/`
2. Thêm steps mới nếu cần
3. Cập nhật conditional logic

## 🚀 Triển Khai Production

### 1. Environment Setup
```bash
# Đảm bảo các biến môi trường được cấu hình
export PG_CONNECTION_STRING="your-postgres-url"
export SUPABASE_URL="your-supabase-url"
export SUPABASE_SERVICE_KEY="your-service-key"
```

### 2. Start Server
```bash
# Development
yarn dev:express

# Production  
yarn build
yarn start
```

### 3. Health Check
```bash
curl http://localhost:3000/api/intelligent-customer-service/health
```

## 🎉 Kết Luận

**Intelligent Customer Service Agent** mang đến một trải nghiệm chăm sóc khách hàng vượt trội với:

- ✅ **Tận dụng tối đa** các tool hiện có
- ✅ **Phân tích thông minh** ý định và cảm xúc
- ✅ **Memory system mạnh mẽ** cho cá nhân hóa
- ✅ **Workflow linh hoạt** với human handoff
- ✅ **Performance cao** và monitoring chi tiết
- ✅ **Dễ mở rộng** và tùy chỉnh

Hệ thống này không chỉ là một chatbot mà là một **nhân viên AI thông minh** được training với tư duy customer service chuyên nghiệp! 🚀
