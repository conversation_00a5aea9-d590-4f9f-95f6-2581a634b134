# ✅ Chatbot Configuration - Fixed

## 🔧 Đã Fix

1. **Sử dụng Supabase Admin Client** thay vì postgres trực tiếp
2. **Loại bỏ available_functions** không tồn tại trong DB
3. **Chỉ lấy instruction và delay_time** từ database
4. **Tối ưu hóa interface** cho đúng cấu trúc DB

## 📋 Cấu Trúc Database Thực Tế

### Bảng `chatbot_configurations`
- `instruction` (text) - Hướng dẫn cho AI
- `delay_time` (integer) - Thời gian trễ (giây), default: 10

### Bảng `chatbot_channels`  
- `inbox_id` (varchar) - ID inbox từ Chatwoot
- `bot_id` (uuid) - Link đến chatbot_configurations
- `tenant_id` (uuid) - ID tenant

## 🚀 API Endpoints

### Lấy cấu hình chatbot
```http
GET /api/system/chatbot/config/:inbox_id
```

### Cập nhật cấu hình
```http
PUT /api/system/message-batching/config
Content-Type: application/json

{
  "bot_id": "uuid",
  "delay_time": 15,
  "instruction": "Bạn là trợ lý AI..."
}
```

## ⚙️ Cách Hoạt Động

1. **Webhook** → `agent.controller.ts`
2. **Lấy cấu hình** từ `chatbot_configurations` qua `supabaseAdmin`
3. **Áp dụng delay_time** trong `message-buffer.service.ts`
4. **Sử dụng instruction** trong `agent.service.ts`

## 🔍 Test

```bash
# Test lấy cấu hình
curl http://localhost:3000/api/system/chatbot/config/INBOX_ID

# Test cập nhật
curl -X PUT http://localhost:3000/api/system/message-batching/config \
  -H "Content-Type: application/json" \
  -d '{"bot_id": "uuid", "delay_time": 15}'
```

## ✅ Đã Hoàn Thành

- [x] Fix lỗi column "available_functions" does not exist
- [x] Sử dụng supabaseAdmin thay vì postgres trực tiếp  
- [x] Cập nhật interface phù hợp với DB
- [x] Tối ưu hóa chỉ lấy instruction và delay_time
- [x] Đồng bộ hệ thống sử dụng Supabase package
