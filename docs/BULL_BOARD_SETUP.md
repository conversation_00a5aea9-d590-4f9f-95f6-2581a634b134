# Bull Board Dashboard Setup & Usage

## 📋 Tổng Quan

Bull Board là web dashboard để monitor và quản lý tất cả BullMQ queues trong hệ thống Mooly Chatbot AI. Dashboard cung cấp real-time monitoring, job management, và debugging tools.

## 🚀 Truy Cập Dashboard

### URL Dashboard
```
http://localhost:3000/admin/queues
```

### Authentication
- **Username**: `admin` (hoặc theo `BULL_BOARD_USERNAME`)
- **Password**: `mooly123` (hoặc theo `BULL_BOARD_PASSWORD`)
- **Type**: Basic Authentication

## 🔧 Cấu Hình Environment Variables

```env
# Bull Board Dashboard Configuration
BULL_BOARD_ENABLED=true
BULL_BOARD_USERNAME=admin
BULL_BOARD_PASSWORD=mooly123
BULL_BOARD_BYPASS_AUTH=true          # Chỉ cho development
BULL_BOARD_ALLOWED_IPS=127.0.0.1,::1 # Optional IP whitelist
```

## 📊 Queues Được Monitor

Bull Board monitor tất cả queues trong hệ thống:

1. **`{product-sync}`** - Đồng bộ sản phẩm
2. **`{email-notification}`** - Gửi email thông báo  
3. **`{data-cleanup}`** - Cleanup dữ liệu
4. **`{webhook-processing}`** - Xử lý webhooks
5. **`{message-batching}`** - Message batching optimization ⭐

## 🎛️ Tính Năng Dashboard

### 1. Real-time Queue Monitoring
- **Waiting Jobs**: Jobs đang chờ xử lý
- **Active Jobs**: Jobs đang được xử lý
- **Completed Jobs**: Jobs đã hoàn thành
- **Failed Jobs**: Jobs thất bại
- **Delayed Jobs**: Jobs được lên lịch

### 2. Job Management
- **View Job Details**: Xem chi tiết job data và logs
- **Retry Failed Jobs**: Retry jobs thất bại
- **Delete Jobs**: Xóa jobs không cần thiết
- **Job Timeline**: Theo dõi lifecycle của jobs

### 3. Queue Operations
- **Pause/Resume Queues**: Tạm dừng/tiếp tục queues
- **Clean Queues**: Cleanup completed/failed jobs
- **Queue Statistics**: Metrics và performance data

### 4. Message Batching Monitoring
Cho queue `{message-batching}`:
- **Batch Key**: Identifier của message batch
- **Message Count**: Số tin nhắn trong batch
- **Immediate Flag**: Có phải xử lý ngay lập tức không
- **Tenant ID**: ID của tenant

## 🔍 Sử Dụng Dashboard

### Truy Cập Dashboard
1. Mở browser và truy cập: `http://localhost:3000/admin/queues`
2. Nhập credentials:
   - Username: `admin`
   - Password: `mooly123`
3. Dashboard sẽ hiển thị tất cả queues

### Monitor Message Batching
1. Click vào queue **`{message-batching}`**
2. Xem các jobs đang xử lý:
   - **Delayed Jobs**: Message batches đang chờ timeout
   - **Active Jobs**: Batches đang được xử lý
   - **Completed Jobs**: Batches đã xử lý xong

### Debug Failed Jobs
1. Click vào tab **"Failed"**
2. Click vào job thất bại để xem details
3. Xem error message và stack trace
4. Có thể retry job nếu cần

### Clean Up Jobs
1. Click vào **"More"** menu
2. Chọn **"Clean"**
3. Chọn loại jobs muốn clean (completed/failed)
4. Xác nhận cleanup

## 📈 API Endpoints

Bull Board cũng cung cấp API endpoints để quản lý queues:

### GET `/api/system/queues/stats`
Lấy thống kê tất cả queues
```json
{
  "success": true,
  "data": {
    "queues": {
      "{message-batching}": {
        "waiting": 5,
        "active": 2,
        "completed": 100,
        "failed": 1,
        "delayed": 8
      }
    },
    "summary": {
      "totalQueues": 5,
      "totalJobs": 116,
      "healthyQueues": 5
    }
  }
}
```

### POST `/api/system/queues/cleanup`
Cleanup tất cả queues
```bash
curl -X POST http://localhost:3000/api/system/queues/cleanup \
  -H "Content-Type: application/json" \
  -d '{"keepJobs": 100}'
```

### POST `/api/system/queues/pause`
Pause tất cả queues (emergency stop)
```bash
curl -X POST http://localhost:3000/api/system/queues/pause
```

### POST `/api/system/queues/resume`
Resume tất cả queues
```bash
curl -X POST http://localhost:3000/api/system/queues/resume
```

### GET `/api/system/queues/health`
Health check cho Bull Board
```bash
curl http://localhost:3000/api/system/queues/health
```

## 🔐 Security

### Production Security
Trong production, đảm bảo:

1. **Strong Password**: Đặt password mạnh
```env
BULL_BOARD_PASSWORD=your_strong_password_here
```

2. **IP Whitelist**: Giới hạn IP có thể truy cập
```env
BULL_BOARD_ALLOWED_IPS=*************,*********
```

3. **Disable in Production**: Có thể tắt hoàn toàn nếu không cần
```env
BULL_BOARD_ENABLED=false
```

### Development Mode
Trong development, có thể bypass authentication:
```env
NODE_ENV=development
BULL_BOARD_BYPASS_AUTH=true
```

## 🚨 Troubleshooting

### Dashboard Không Load
1. Kiểm tra server có chạy không
2. Kiểm tra Redis/Dragonfly connection
3. Xem logs trong console

### Authentication Failed
1. Kiểm tra username/password trong `.env`
2. Clear browser cache
3. Thử incognito mode

### Queues Không Hiển Thị
1. Kiểm tra BullMQ workers có chạy không
2. Kiểm tra Redis connection
3. Restart server

### Performance Issues
1. Cleanup old jobs định kỳ
2. Giới hạn số jobs hiển thị
3. Monitor Redis memory usage

## 📝 Best Practices

### 1. Regular Cleanup
Chạy cleanup hàng ngày:
```bash
# Giữ 100 completed jobs, 50 failed jobs
curl -X POST http://localhost:3000/api/system/queues/cleanup \
  -d '{"keepJobs": 100}'
```

### 2. Monitor Failed Jobs
- Check failed jobs hàng ngày
- Investigate root cause
- Fix issues và retry nếu cần

### 3. Performance Monitoring
- Monitor queue depths
- Watch for stuck jobs
- Track processing times

### 4. Emergency Procedures
Nếu system overload:
```bash
# Pause tất cả queues
curl -X POST http://localhost:3000/api/system/queues/pause

# Cleanup jobs
curl -X POST http://localhost:3000/api/system/queues/cleanup

# Resume queues
curl -X POST http://localhost:3000/api/system/queues/resume
```

## 🔗 Links Hữu Ích

- **Dashboard**: http://localhost:3000/admin/queues
- **System Health**: http://localhost:3000/api/system/health
- **Message Batching Stats**: http://localhost:3000/api/system/message-batching/stats
- **Queue Stats API**: http://localhost:3000/api/system/queues/stats

---

**🎉 Bull Board Dashboard giúp bạn monitor và quản lý toàn bộ job queue system một cách trực quan và hiệu quả!** 