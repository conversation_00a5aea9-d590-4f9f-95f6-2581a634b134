# 🧪 Test Scenarios cho Message Aggregation Logic

## 📋 Mục Tiêu Test

Đảm bảo logic gom tin nhắn hoạt động đúng trong tất cả các trường hợp, đặc biệt khi `delay_time = 0`.

## 🎯 Test Cases

### Test Case 1: Tin nhắn text đơn lẻ với delay_time = 0
**Setup**: 
- Bot cấu hình `delay_time = 0`
- Không có batch hiện tại

**Input**: User gửi tin nhắn text "Xin chào"

**Expected Output**:
- ⚡ Immediate processing
- Không có delay
- <PERSON><PERSON>n hồi ngay lập tức

**Logs cần kiểm tra**:
```
⚡ Bot immediate processing (no delay) for bot xxx
⚡ Immediate mode (delay_time=0) and no existing batch - processing immediately
⚡ Tạo job xử lý ngay lập tức: job_xxx
```

### Test Case 2: Hình ảnh + Text với delay_time = 0 (QUAN TRỌNG)
**Setup**: 
- <PERSON><PERSON> cấu hình `delay_time = 0`
- User g<PERSON><PERSON> hình ảnh trước

**Input**: 
1. User gử<PERSON> hình ảnh
2. <PERSON>u 2-3 giây, user gửi text "Sản phẩm này giá bao nhiêu?"

**Expected Output**:
- Hình ảnh: Delay 10s
- Text: Được gom vào batch hiện tại với delay 2s
- Xử lý tổng hợp cả hình ảnh và text

**Logs cần kiểm tra**:
```
# Tin nhắn hình ảnh
🖼️ Image message detected - using default delay: 10s
🖼️ Image message - will be delayed for user input
⏰ Tạo delayed job (10000ms): job_xxx cho batch batch_xxx

# Tin nhắn text
📦 Existing batch found with 1 messages - will aggregate
📦 Added message to existing batch (2 messages total)
🔄 Existing batch detected with delay_time=0 - using short delay (2s) for message aggregation
⏰ Tạo delayed job (2000ms): job_yyy cho batch batch_xxx
```

### Test Case 3: Text + Hình ảnh với delay_time = 0
**Setup**: 
- Bot cấu hình `delay_time = 0`
- User gửi text trước

**Input**: 
1. User gửi text "Tôi muốn mua sản phẩm này"
2. Ngay sau đó gửi hình ảnh

**Expected Output**:
- Text: Xử lý ngay lập tức (không có batch)
- Hình ảnh: Tạo batch mới với delay 10s

**Logs cần kiểm tra**:
```
# Tin nhắn text đầu tiên
⚡ Immediate mode (delay_time=0) and no existing batch - processing immediately
⚡ Tạo job xử lý ngay lập tức: job_xxx

# Tin nhắn hình ảnh sau đó
🖼️ Image message detected - using default delay: 10s
⏰ Tạo delayed job (10000ms): job_yyy cho batch batch_yyy
```

### Test Case 4: Từ khóa khẩn cấp với batch hiện tại
**Setup**: 
- Bot cấu hình `delay_time = 0`
- Có batch hình ảnh đang chờ

**Input**: 
1. User gửi hình ảnh
2. User gửi text "KHẨN CẤP hủy đơn"

**Expected Output**:
- Hình ảnh: Delay 10s
- Text khẩn cấp: Xử lý ngay lập tức (bỏ qua batch)

**Logs cần kiểm tra**:
```
# Tin nhắn hình ảnh
🖼️ Image message detected - using default delay: 10s
⏰ Tạo delayed job (10000ms): job_xxx

# Tin nhắn khẩn cấp
🚨 Urgent keyword detected - processing immediately
⚡ Tạo job xử lý ngay lập tức: job_yyy
```

### Test Case 5: Nhiều tin nhắn text liên tiếp với delay_time = 0
**Setup**: 
- Bot cấu hình `delay_time = 0`
- User gửi nhiều tin nhắn text liên tiếp

**Input**: 
1. "Xin chào"
2. "Tôi muốn hỏi về sản phẩm"
3. "Có khuyến mãi không?"

**Expected Output**:
- Tin nhắn đầu tiên: Xử lý ngay lập tức
- Tin nhắn thứ 2, 3: Tạo batch mới và xử lý ngay lập tức

**Logs cần kiểm tra**:
```
# Mỗi tin nhắn đều được xử lý ngay lập tức
⚡ Immediate mode (delay_time=0) and no existing batch - processing immediately
```

## 🔍 Cách Test

### 1. Kiểm tra Logs
```bash
# Theo dõi logs real-time
tail -f logs/app.log | grep -E "(Image message|Existing batch|Immediate mode|delayed job)"
```

### 2. Test API
```bash
# Gửi tin nhắn hình ảnh
curl -X POST http://localhost:3000/api/agent/message \
  -H "Content-Type: application/json" \
  -d '{
    "message": "",
    "messageAttachments": [{"data_url": "https://example.com/image.jpg"}],
    "inboxId": "123",
    "accountId": "456",
    "conversationId": "789"
  }'

# Sau 2-3 giây, gửi tin nhắn text
curl -X POST http://localhost:3000/api/agent/message \
  -H "Content-Type: application/json" \
  -d '{
    "message": "Sản phẩm này giá bao nhiêu?",
    "inboxId": "123",
    "accountId": "456",
    "conversationId": "789"
  }'
```

### 3. Kiểm tra Redis
```bash
# Xem batch hiện tại
redis-cli keys "batch:*"
redis-cli get "batch:thread_xxx:resource_yyy"

# Xem job IDs
redis-cli keys "job:*"
```

## ✅ Kết Quả Mong Đợi

1. **Tin nhắn text đơn lẻ**: Xử lý ngay lập tức khi `delay_time = 0`
2. **Hình ảnh + Text**: Được gom lại và xử lý tổng hợp
3. **Từ khóa khẩn cấp**: Luôn xử lý ngay lập tức
4. **Logic gom tin nhắn**: Hoạt động đúng ngay cả với `delay_time = 0`

## 🚨 Red Flags

Nếu thấy các logs sau thì có vấn đề:

```bash
# BAD: Text message không được gom với hình ảnh
⚡ Immediate mode (delay_time=0) and no existing batch - processing immediately
# (khi thực tế có batch hình ảnh đang chờ)

# BAD: Hình ảnh được xử lý ngay lập tức
⚡ Tạo job xử lý ngay lập tức: job_xxx
# (khi tin nhắn có hình ảnh mà không có từ khóa khẩn cấp)

# BAD: Batch không được tạo đúng
# Không thấy logs "📦 Existing batch found" khi có batch hiện tại
```

## 🎯 Success Criteria

- ✅ Tất cả test cases pass
- ✅ Logs hiển thị đúng logic
- ✅ Message aggregation hoạt động với `delay_time = 0`
- ✅ Không có tin nhắn bị mất hoặc xử lý sai
- ✅ Performance không bị ảnh hưởng
