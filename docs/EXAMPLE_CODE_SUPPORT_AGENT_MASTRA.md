Tuyệt vời! Dựa trên yêu cầu của bạn về một chatbot AI siêu thông minh có khả năng tư vấn, chố<PERSON> đơn, tạo đơn và truy cập CRM, cùng với tham khảo code mẫu Mastra AI, tôi sẽ thiết kế một cấu trúc agent và workflow cho bạn.

Lưu ý rằng đây là một thiết kế cấu trúc và code mẫu. Việc tích hợp thực tế với CRM, hệ thống đặt hàng, và các API khác sẽ cần được thực hiện ở phần `execute` của mỗi `step` tương ứng.

**Ý tưởng cốt lõi:**
1.  **`SuperSalesCareAgent`**: <PERSON><PERSON><PERSON> là "bộ não" LLM, được huấn luyện với các chỉ dẫn chi tiết để thực hiện đa dạng các tác vụ bán hàng và chăm sóc khách hàng. Agent này sẽ quyết định khi nào cần trả lời trực tiếp, khi nào cần sử dụng công cụ (tool) để lấy thông tin hoặc thực hiện hành động.
2.  **Công cụ (Tools as Mastra Steps)**: Các hành động cụ thể như lấy thông tin khách hàng từ CRM, kiểm tra sản phẩm, tạo đơn hàng, ghi log... sẽ được định nghĩa dưới dạng các `createStep` của Mastra. Agent sẽ "gọi" các step này khi cần.
3.  **`chatTurnStep` (Bước Xử Lý Lượt Hội Thoại)**: Đây là một step trung tâm, điều phối một lượt tương tác giữa người dùng và AI. Nó nhận đầu vào từ người dùng, chuyển cho `SuperSalesCareAgent`, xử lý yêu cầu sử dụng công cụ (nếu có) bằng cách gọi các step công cụ khác, sau đó nhận phản hồi cuối cùng từ agent để trả lời người dùng.
4.  **`customerServiceWorkflow`**: Workflow chính sẽ bao gồm `chatTurnStep`. Trong một ứng dụng thực tế, bạn sẽ cần một vòng lặp bên ngoài để quản lý cuộc hội thoại nhiều lượt, truyền `conversationHistory` từ lượt này sang lượt kế tiếp.

Dưới đây là code mẫu chi tiết:

```typescript
import { Mastra } from "@mastra/core";
import { Agent } from "@mastra/core/agent"; // Giả sử Agent có thể được dùng hoặc chúng ta gọi model trực tiếp
import { createStep, createWorkflow } from "@mastra/core/workflows";
import { z } from "zod";
import { openai } from "@ai-sdk/openai"; // Sử dụng trực tiếp model OpenAI để có full control tool calling

// --- 1. Định Nghĩa Agent Chính ---
// Agent này chủ yếu cung cấp persona và model, phần xử lý logic phức tạp (tool calling)
// sẽ nằm trong chatTurnStep để có sự linh hoạt cao nhất.
const superSalesCareAgentPersona = new Agent({
  name: "Super Sales & Customer Care Agent",
  instructions: `
    Bạn là một chuyên viên tư vấn bán hàng và chăm sóc khách hàng AI siêu thông minh của AASTU Shop.
    Nhiệm vụ của bạn là:
    - Chào hỏi thân thiện, tìm hiểu kỹ nhu cầu của khách hàng. [cite: 19]
    - Tư vấn sản phẩm/dịch vụ một cách chuyên nghiệp, cá nhân hóa dựa trên thông tin khách hàng và lịch sử mua hàng (nếu có).
    - Sử dụng các kỹ thuật đặt câu hỏi theo mô hình SPIN (Tình huống, Vấn đề, Hệ quả, Nhu cầu) để hiểu sâu vấn đề. [cite: 3, 4, 5, 6]
    - Xử lý các tình huống từ chối, so sánh giá một cách khéo léo, tập trung vào giá trị sản phẩm. [cite: 9]
    - Chốt đơn hàng một cách tự nhiên, đưa ra lựa chọn thay vì câu hỏi đóng. [cite: 11, 12]
    - Hỗ trợ tạo đơn hàng khi khách đồng ý mua.
    - Truy cập và cập nhật thông tin khách hàng, lịch sử tương tác vào hệ thống CRM.
    - Giải đáp thắc mắc, xử lý khiếu nại với thái độ tích cực và tìm giải pháp.
    - Luôn duy trì giọng điệu thân thiện, chuyên nghiệp và đồng cảm. [cite: 7] Tránh gây áp lực cho khách hàng. [cite: 8]

    QUY TRÌNH TƯƠNG TÁC VÀ SỬ DỤNG CÔNG CỤ:
    1.  Khi nhận được tin nhắn từ người dùng, hãy phân tích ý định (INTENT), giai đoạn (STAGE), cảm xúc (EMOTION) và cơ hội (OPPORTUNITY). [cite: 22, 23, 24]
    2.  Nếu cần thông tin khách hàng (ví dụ: lịch sử mua hàng, thông tin liên hệ), hãy yêu cầu sử dụng công cụ 'fetchCustomerInfo'.
    3.  Nếu cần thông tin sản phẩm (ví dụ: giá, tồn kho, chi tiết), hãy yêu cầu sử dụng công cụ 'fetchProductInfo'.
    4.  Nếu khách hàng muốn đặt hàng và đã cung cấp đủ thông tin, hãy yêu cầu sử dụng công cụ 'createOrder'.
    5.  Sau mỗi tương tác quan trọng hoặc khi kết thúc cuộc trò chuyện, hãy yêu cầu sử dụng công cụ 'updateCRMLog' để ghi lại thông tin.
    6.  Nếu khách hàng hỏi về tình trạng đơn hàng, hãy yêu cầu sử dụng công cụ 'checkOrderStatus'.
    7.  Nếu bạn có thể trả lời trực tiếp mà không cần công cụ, hãy làm vậy.
    8.  Sau khi công cụ thực thi và trả về kết quả, hãy sử dụng thông tin đó để trả lời khách hàng một cách tự nhiên.
    9.  Luôn xác nhận lại các thông tin quan trọng với khách hàng trước khi thực hiện hành động (ví dụ: tạo đơn hàng).
    10. Nếu khách hàng yêu cầu một việc ngoài khả năng hoặc cần sự can thiệp của con người, hãy thông báo rõ và đề xuất chuyển cho nhân viên hỗ trợ.
    `,
  model: openai("gpt-4o-mini"), // Hoặc model mạnh hơn như gpt-4-turbo
});

// --- 2. Định Nghĩa Các Công Cụ (Tools as Mastra Steps) ---

// Schema chung cho thông tin khách hàng
const customerInfoSchema = z.object({
  customerId: z.string().optional(),
  name: z.string().optional(),
  email: z.string().email().optional(),
  phone: z.string().optional(),
  address: z.string().optional(),
  purchaseHistory: z.array(z.object({ orderId: z.string(), date: z.string(), items: z.string(), total: z.number() })).optional(),
  interactionLogs: z.array(z.string()).optional(),
  message: z.string().optional() // Thông báo nếu không tìm thấy hoặc có lỗi
});

const fetchCustomerInfoStep = createStep({
  id: "fetch-customer-info",
  description: "Lấy thông tin chi tiết của khách hàng từ CRM dựa trên email, SĐT hoặc ID.",
  inputSchema: z.object({
    identifier: z.string().describe("Email, SĐT hoặc ID của khách hàng"),
  }),
  outputSchema: customerInfoSchema,
  execute: async ({ inputData }) => {
    console.log(`[TOOL EXECUTED] fetchCustomerInfoStep with input: ${inputData?.identifier}`);
    // === Logic gọi API CRM thực tế để lấy thông tin khách hàng ===
    // Ví dụ mock:
    if (inputData?.identifier === "<EMAIL>" || inputData?.identifier === "KH001") {
      return {
        customerId: "KH001",
        name: "Nguyễn Văn A",
        email: "<EMAIL>",
        phone: "090xxxxxxx",
        address: "123 Đường ABC, Quận 1, TP. HCM",
        purchaseHistory: [{ orderId: "DH001", date: "2024-05-20", items: "Áo Thun XYZ (Màu Đen, Size L)", total: 350000 }],
        interactionLogs: ["2024-05-10: Hỏi về chính sách đổi trả."]
      };
    }
    return { customerId: inputData?.identifier, message: "Không tìm thấy thông tin khách hàng." };
  },
});

const productInfoSchema = z.object({
  productId: z.string(),
  name: z.string(),
  description: z.string(),
  price: z.number(),
  stock: z.number(),
  variants: z.array(z.object({ sku: z.string(), color: z.string(), size: z.string(), stock: z.number() })).optional(),
  message: z.string().optional()
});

const fetchProductInfoStep = createStep({
  id: "fetch-product-info",
  description: "Lấy thông tin chi tiết sản phẩm từ hệ thống (giá, tồn kho, mô tả).",
  inputSchema: z.object({
    productIdentifier: z.string().describe("ID, SKU, hoặc tên sản phẩm"),
  }),
  outputSchema: productInfoSchema,
  execute: async ({ inputData }) => {
    console.log(`[TOOL EXECUTED] fetchProductInfoStep with input: ${inputData?.productIdentifier}`);
    // === Logic gọi API sản phẩm thực tế ===
    // Ví dụ mock:
    if (inputData?.productIdentifier?.toLowerCase().includes("áo thun")) {
      return {
        productId: "AT001",
        name: "Áo Thun Cotton Cao Cấp",
        description: "Áo thun làm từ 100% cotton, thoáng mát, thấm hút mồ hôi.",
        price: 250000,
        stock: 100,
        variants: [
          { sku: "AT001-DEN-L", color: "Đen", size: "L", stock: 20 },
          { sku: "AT001-TRANG-M", color: "Trắng", size: "M", stock: 30 },
        ]
      };
    }
    return { productId: inputData?.productIdentifier || "", name: "", description: "", price: 0, stock: 0, message: "Không tìm thấy sản phẩm." };
  },
});

const orderSchema = z.object({
  orderId: z.string(),
  customerId: z.string(),
  items: z.array(z.object({ productId: z.string(), quantity: z.number(), price: z.number() })),
  totalAmount: z.number(),
  shippingAddress: z.string(),
  status: z.string().describe("Ví dụ: pending, confirmed, shipping, delivered"),
  message: z.string().optional()
});

const createOrderStep = createStep({
  id: "create-order",
  description: "Tạo đơn hàng mới trong hệ thống và CRM.",
  inputSchema: z.object({
    customerId: z.string(),
    items: z.array(z.object({ productId: z.string(), quantity: z.number(), price: z.number() })).min(1),
    shippingAddress: z.string(),
    paymentMethod: z.string().optional().describe("Phương thức thanh toán, ví dụ: COD, Bank Transfer"),
  }),
  outputSchema: orderSchema,
  execute: async ({ inputData }) => {
    console.log(`[TOOL EXECUTED] createOrderStep with input:`, JSON.stringify(inputData, null, 2));
    // === Logic gọi API tạo đơn hàng thực tế ===
    const totalAmount = inputData?.items.reduce((sum, item) => sum + item.price * item.quantity, 0) || 0;
    return {
      orderId: `DH${Date.now()}`,
      customerId: inputData?.customerId || "N/A",
      items: inputData?.items || [],
      totalAmount: totalAmount,
      shippingAddress: inputData?.shippingAddress || "N/A",
      status: "pending_confirmation",
      message: "Đơn hàng đã được tạo thành công và đang chờ xác nhận."
    };
  },
});

const crmLogSchema = z.object({
  logId: z.string(),
  status: z.string(),
  message: z.string().optional(),
});

const updateCRMLogStep = createStep({
  id: "update-crm-log",
  description: "Cập nhật log tương tác hoặc thông tin khách hàng vào CRM.",
  inputSchema: z.object({
    customerId: z.string(),
    interactionSummary: z.string().describe("Tóm tắt nội dung tương tác, quyết định, hoặc thông tin cần cập nhật."),
    tags: z.array(z.string()).optional().describe("Ví dụ: 'potential_lead', 'resolved_issue', 'order_placed'")
  }),
  outputSchema: crmLogSchema,
  execute: async ({ inputData }) => {
    console.log(`[TOOL EXECUTED] updateCRMLogStep for customer ${inputData?.customerId} with summary: ${inputData?.interactionSummary}`);
    // === Logic gọi API CRM để ghi log ===
    return {
      logId: `LOG${Date.now()}`,
      status: "success",
      message: "Log tương tác đã được cập nhật vào CRM."
    };
  },
});

const checkOrderStatusStep = createStep({
  id: "check-order-status",
  description: "Kiểm tra tình trạng đơn hàng hiện tại.",
  inputSchema: z.object({
    orderId: z.string(),
  }),
  outputSchema: orderSchema, // Trả về thông tin đơn hàng bao gồm status
  execute: async ({ inputData }) => {
    console.log(`[TOOL EXECUTED] checkOrderStatusStep for order: ${inputData?.orderId}`);
    // === Logic gọi API kiểm tra đơn hàng ===
    // Ví dụ mock
    if (inputData?.orderId === "DH001") {
        return {
            orderId: "DH001",
            customerId: "KH001",
            items: [{ productId: "AT001", quantity: 1, price: 350000 }],
            totalAmount: 350000,
            shippingAddress: "123 Đường ABC, Quận 1, TP. HCM",
            status: "delivered",
            message: "Đơn hàng đã được giao thành công."
        };
    }
    return {
        orderId: inputData?.orderId || "N/A",
        customerId: "N/A", items: [], totalAmount: 0, shippingAddress: "N/A",
        status: "not_found",
        message: "Không tìm thấy thông tin đơn hàng."
    };
  },
});


// --- 3. Định Nghĩa Bước Xử Lý Lượt Hội Thoại Chính ---
const AIAgentMessageSchema = z.object({
  role: z.enum(["user", "assistant", "tool"]),
  content: z.string(),
  tool_calls: z.array(z.object({
    id: z.string(),
    type: z.literal("function"),
    function: z.object({
      name: z.string(),
      arguments: z.string() // OpenAI trả về arguments dưới dạng JSON string
    })
  })).optional(),
  tool_call_id: z.string().optional(), // For tool response
});


const chatTurnStep = createStep({
  id: "chat-turn-step",
  description: "Xử lý một lượt hội thoại với khách hàng, bao gồm gọi tool nếu cần.",
  inputSchema: z.object({
    userInput: z.string(),
    conversationHistory: z.array(AIAgentMessageSchema).optional().default([]),
    customerId: z.string().optional().describe("ID khách hàng nếu đã được xác định."),
  }),
  outputSchema: z.object({
    agentResponse: z.string(),
    updatedHistory: z.array(AIAgentMessageSchema),
    updatedCustomerId: z.string().optional(),
  }),
  execute: async ({ inputData }) => {
    if (!inputData) throw new Error("Input data for chatTurnStep not found");

    let currentHistory = [...inputData.conversationHistory];
    currentHistory.push({ role: "user", content: inputData.userInput });

    const toolsForAgent = [
      { type: "function" as const, function: { name: "fetchCustomerInfo", description: fetchCustomerInfoStep.description, parameters: fetchCustomerInfoStep.inputSchema.openapi("Parameters") } },
      { type: "function" as const, function: { name: "fetchProductInfo", description: fetchProductInfoStep.description, parameters: fetchProductInfoStep.inputSchema.openapi("Parameters") } },
      { type: "function" as const, function: { name: "createOrder", description: createOrderStep.description, parameters: createOrderStep.inputSchema.openapi("Parameters") } },
      { type: "function" as const, function: { name: "updateCRMLog", description: updateCRMLogStep.description, parameters: updateCRMLogStep.inputSchema.openapi("Parameters") } },
      { type: "function" as const, function: { name: "checkOrderStatus", description: checkOrderStatusStep.description, parameters: checkOrderStatusStep.inputSchema.openapi("Parameters") } },
    ];

    // Vòng lặp xử lý tool_calls nếu có
    // eslint-disable-next-line no-constant-condition
    while (true) {
        const response = await openai("gpt-4o-mini").generate({ // Hoặc model đã định nghĩa trong superSalesCareAgentPersona
            model: "gpt-4o-mini", // Đảm bảo model này hỗ trợ tool calling
            system: superSalesCareAgentPersona.instructions, // Sử dụng instructions từ Agent
            messages: currentHistory.map(msg => ({ // Chuyển đổi message format cho AI SDK
                role: msg.role as "user" | "assistant" | "tool",
                content: msg.content,
                toolCalls: msg.tool_calls?.map(tc => ({ id: tc.id, type: tc.type, function: { name: tc.function.name, arguments: tc.function.arguments }})),
                toolCallId: msg.tool_call_id,
            })),
            tools: toolsForAgent,
            toolChoice: "auto",
        });

        const message = response.choices[0].message;
        currentHistory.push({ // Thêm phản hồi của assistant vào history (kể cả nếu là tool_calls)
            role: "assistant",
            content: message.content || "",
            tool_calls: message.toolCalls?.map(tc => ({ id: tc.toolCallId, type: "function", function: { name: tc.functionName, arguments: tc.functionArguments }})),
        });

        if (!message.toolCalls || message.toolCalls.length === 0) {
            // Không có tool call, trả về phản hồi dạng text của agent
            return {
                agentResponse: message.content || "Xin lỗi, tôi chưa thể xử lý yêu cầu này.",
                updatedHistory: currentHistory,
                updatedCustomerId: inputData.customerId, // Cần logic cập nhật customerId nếu tool fetchCustomerInfo được gọi
            };
        }

        // Có tool call, thực thi tool
        for (const toolCall of message.toolCalls) {
            const toolName = toolCall.functionName;
            const toolArgs = JSON.parse(toolCall.functionArguments);
            let toolResult: any;

            console.log(`Agent requests to call tool: ${toolName} with args:`, toolArgs);

            try {
                if (toolName === "fetchCustomerInfo") {
                    const result = await fetchCustomerInfoStep.executeSafe({ inputData: toolArgs as z.infer<typeof fetchCustomerInfoStep.inputSchema> });
                    if (result.ok) toolResult = result.value; else throw new Error(result.error.message);
                    // Cập nhật customerId nếu thành công và có customerId trả về
                    if (toolResult.customerId && inputData.customerId !== toolResult.customerId) {
                        inputData.customerId = toolResult.customerId; // Cập nhật cho output
                        console.log("Updated customerId to:", inputData.customerId);
                    }
                } else if (toolName === "fetchProductInfo") {
                    const result = await fetchProductInfoStep.executeSafe({ inputData: toolArgs as z.infer<typeof fetchProductInfoStep.inputSchema> });
                     if (result.ok) toolResult = result.value; else throw new Error(result.error.message);
                } else if (toolName === "createOrder") {
                    const result = await createOrderStep.executeSafe({ inputData: toolArgs as z.infer<typeof createOrderStep.inputSchema> });
                     if (result.ok) toolResult = result.value; else throw new Error(result.error.message);
                } else if (toolName === "updateCRMLog") {
                    const result = await updateCRMLogStep.executeSafe({ inputData: toolArgs as z.infer<typeof updateCRMLogStep.inputSchema> });
                    if (result.ok) toolResult = result.value; else throw new Error(result.error.message);
                } else if (toolName === "checkOrderStatus") {
                    const result = await checkOrderStatusStep.executeSafe({ inputData: toolArgs as z.infer<typeof checkOrderStatusStep.inputSchema> });
                    if (result.ok) toolResult = result.value; else throw new Error(result.error.message);
                } else {
                    console.error(`Unknown tool called: ${toolName}`);
                    toolResult = { error: `Tool ${toolName} not found.` };
                }
            } catch (error: any) {
                console.error(`Error executing tool ${toolName}:`, error);
                toolResult = { error: `Error executing tool ${toolName}: ${error.message}` };
            }

            currentHistory.push({
                role: "tool",
                tool_call_id: toolCall.toolCallId,
                content: JSON.stringify(toolResult),
            });
        }
        // Sau khi thực thi tool và thêm kết quả vào history, tiếp tục vòng lặp để agent xử lý kết quả tool
    }
  },
});


// --- 4. Định Nghĩa Workflow Chính ---
const customerServiceWorkflow = createWorkflow({
  id: "customer-service-workflow",
  inputSchema: z.object({
    userInput: z.string(),
    conversationHistory: z.array(AIAgentMessageSchema).optional().default([]),
    customerId: z.string().optional(),
  }),
  outputSchema: chatTurnStep.outputSchema, // Output của workflow là output của chatTurnStep
})
.then(chatTurnStep); // Workflow này chỉ có một bước chính là chatTurnStep

// --- 5. Commit Workflow và Khởi Tạo Mastra ---
customerServiceWorkflow.commit();

const mastra = new Mastra({
  workflows: {
    customerServiceWorkflow,
    // Các steps cũng có thể được đăng ký riêng lẻ nếu cần gọi trực tiếp
    fetchCustomerInfoStep,
    fetchProductInfoStep,
    createOrderStep,
    updateCRMLogStep,
    checkOrderStatusStep,
    chatTurnStep
  },
});

// --- 6. Workflow Execution (Ví dụ cho một lượt hội thoại) ---
async function main() {
  let conversationHistory: z.infer<typeof AIAgentMessageSchema>[] = [];
  let currentCustomerId: string | undefined = undefined;

  // Lượt 1: Khách chào hỏi
  let userInput = "Chào shop, mình muốn tư vấn về áo thun";
  console.log(`\n--- USER: ${userInput} ---`);

  let run = mastra.getWorkflow("customerServiceWorkflow").createRun();
  let result = await run.start({
    inputData: { userInput, conversationHistory, customerId: currentCustomerId },
  });

  if (result.ok) {
    console.log("--- AGENT:", result.value.agentResponse);
    conversationHistory = result.value.updatedHistory;
    currentCustomerId = result.value.updatedCustomerId;
  } else {
    console.error("Workflow run failed:", result.error);
    return;
  }

  // Lượt 2: Khách hỏi thông tin cụ thể (có thể trigger tool fetchProductInfo)
  userInput = "Áo thun cotton cao cấp bên bạn còn màu đen size L không?";
  console.log(`\n--- USER: ${userInput} ---`);

  run = mastra.getWorkflow("customerServiceWorkflow").createRun();
  result = await run.start({
    inputData: { userInput, conversationHistory, customerId: currentCustomerId },
  });

  if (result.ok) {
    console.log("--- AGENT:", result.value.agentResponse);
    conversationHistory = result.value.updatedHistory;
    currentCustomerId = result.value.updatedCustomerId;
  } else {
    console.error("Workflow run failed:", result.error);
  }

  // Lượt 3: Khách muốn đặt hàng (có thể trigger tool createOrder)
  // Giả sử agent đã tư vấn và khách đồng ý mua
  userInput = "Ok, mình lấy 1 áo thun cotton cao cấp màu đen size L. Giao đến 123 Đường ABC, Quận 1, TP. HCM nhé. SĐT của mình là 090xxxxxxx, email là <EMAIL>";
  // (Trong thực tế, agent sẽ cần hỏi và xác nhận customerId hoặc các thông tin cần thiết để tạo đơn.
  // Nếu chưa có customerId, agent có thể gọi fetchCustomerInfo hoặc yêu cầu thông tin để tạo mới)
  console.log(`\n--- USER: ${userInput} ---`);

  run = mastra.getWorkflow("customerServiceWorkflow").createRun();
  // Giả sử ở bước này, customerId đã được xác định là "KH001" từ một tool call trước đó
  if(!currentCustomerId) { // Giả sử agent cần xác định customerId trước khi tạo đơn
    // currentCustomerId = "KH001"; // Gán cứng để test, hoặc agent sẽ hỏi và gọi tool
    console.log("Agent sẽ cần hỏi thêm thông tin để xác định khách hàng hoặc tạo đơn.");
  }


  result = await run.start({
    inputData: { userInput, conversationHistory, customerId: currentCustomerId },
  });

  if (result.ok) {
    console.log("--- AGENT:", result.value.agentResponse);
    conversationHistory = result.value.updatedHistory;
    currentCustomerId = result.value.updatedCustomerId;

    // Giả sử sau khi tạo đơn, agent sẽ tự động ghi log CRM
    // (Logic này nằm trong prompt của agent để quyết định gọi updateCRMLogStep)
  } else {
    console.error("Workflow run failed:", result.error);
  }
}

main().catch(console.error);

```

**Giải thích chi tiết:**

1.  **`superSalesCareAgentPersona`**:
    * `instructions`: Phần này cực kỳ quan trọng. Nó chứa toàn bộ "kiến thức" và "quy trình" mà AI cần tuân theo, bao gồm cách tư vấn (SPIN [cite: 3, 4, 5, 6]), cách xử lý tình huống[cite: 9], chốt đơn[cite: 11, 12], và quan trọng nhất là **khi nào và làm thế nào để yêu cầu sử dụng các công cụ (tools)**. Tôi đã tích hợp các tư duy từ `stragi.txt` và cấu trúc prompt từ `promt-aastu.txt` vào đây. [cite: 2, 7, 8, 19, 22, 23, 24]
    * `model`: Chọn model OpenAI hỗ trợ tool calling tốt (ví dụ `gpt-4o-mini` hoặc `gpt-4-turbo`).

2.  **Các Steps Công Cụ (`fetchCustomerInfoStep`, `fetchProductInfoStep`, `createOrderStep`, `updateCRMLogStep`, `checkOrderStatusStep`)**:
    * Mỗi step đại diện cho một hành động cụ thể mà AI có thể yêu cầu.
    * `id`, `description`: Mô tả rõ ràng để AI hiểu chức năng của tool.
    * `inputSchema`, `outputSchema`: Định nghĩa dữ liệu vào/ra cho từng tool bằng `zod`. AI sẽ dựa vào đây để tạo đúng tham số khi gọi tool.
    * `execute`: Phần này chứa logic thực thi. Trong code mẫu, tôi dùng `console.log` và trả về dữ liệu giả lập (mock data). **Trong dự án thực tế, bạn sẽ thay thế bằng các lời gọi API đến CRM, hệ thống e-commerce, database sản phẩm, v.v.**
    * `executeSafe`: Mastra cung cấp `executeSafe` để xử lý lỗi một cách an toàn hơn, trả về `{ ok: true, value: ... }` hoặc `{ ok: false, error: ... }`.

3.  **`chatTurnStep`**:
    * Đây là "trái tim" của việc xử lý hội thoại tương tác.
    * `inputSchema`: Nhận đầu vào từ người dùng (`userInput`), toàn bộ lịch sử hội thoại trước đó (`conversationHistory`), và `customerId` (nếu đã biết).
    * `execute`:
        * Thêm tin nhắn mới của người dùng vào `conversationHistory`.
        * Định nghĩa `toolsForAgent`: Chuyển đổi các Mastra steps thành định dạng `tools` mà model OpenAI có thể hiểu (sử dụng `openapi()` của Zod schema để tạo `parameters`).
        * Gọi model OpenAI (`openai("gpt-4o-mini").generate`) với lịch sử hội thoại và danh sách các tools.
        * **Xử lý Tool Calling**:
            * Nếu model trả về `toolCalls`: Vòng lặp sẽ thực thi từng tool được yêu cầu. Nó xác định đúng `step` cần gọi, truyền tham số, và lấy kết quả.
            * Kết quả từ tool được thêm lại vào `conversationHistory` với `role: "tool"`.
            * Sau đó, vòng lặp `while(true)` sẽ lặp lại, gửi `conversationHistory` (đã bao gồm kết quả tool) trở lại cho model OpenAI để nó có thể sử dụng thông tin đó và tạo ra phản hồi cuối cùng cho người dùng.
            * Nếu `inputData.customerId` được cập nhật sau khi gọi `fetchCustomerInfoStep`, giá trị này sẽ được truyền ra ngoài.
        * Nếu model không yêu cầu `toolCalls` (tức là nó có thể trả lời trực tiếp): Phản hồi dạng text của model được trả về.
    * `outputSchema`: Trả về phản hồi của AI (`agentResponse`), lịch sử hội thoại đã được cập nhật (`updatedHistory`), và `updatedCustomerId`.

4.  **`customerServiceWorkflow`**:
    * Một workflow đơn giản chỉ chứa `chatTurnStep`.
    * Trong ứng dụng thực tế, bạn sẽ cần một lớp quản lý hội thoại (ví dụ: một session chat trên giao diện người dùng) để gọi workflow này lặp đi lặp lại, duy trì `conversationHistory` và `customerId` qua các lượt.

5.  **Thực thi (`main` function)**:
    * Minh họa cách gọi `customerServiceWorkflow` cho nhiều lượt hội thoại.
    * `conversationHistory` và `currentCustomerId` được duy trì và truyền vào cho mỗi lượt chạy workflow.

**Để chatbot AI này thực sự "toàn quyền như một nhân viên thực tế":**

* **Tích hợp API đầy đủ**: Phần `execute` của các `step` công cụ phải được kết nối thực sự với các hệ thống backend (CRM, E-commerce Platform, Database,...).
* **Xác thực và Phân quyền**: Các lời gọi API từ các `step` phải được xác thực cẩn thận và có đủ quyền hạn để đọc/ghi dữ liệu cần thiết.
* **Error Handling và Fallback**: Xử lý lỗi từ API, lỗi logic trong các `step` một cách chi tiết. Có kịch bản dự phòng khi một hành động không thành công.
* **Bảo mật**: Đảm bảo an toàn dữ liệu khách hàng và thông tin nhạy cảm.
* **Logging và Monitoring**: Ghi log chi tiết các hành động của AI, các tool được gọi, và kết quả để dễ dàng gỡ lỗi và theo dõi hiệu suất.
* **Continuous Improvement**: Thường xuyên review các cuộc hội thoại, tinh chỉnh `instructions` cho agent, cập nhật logic của tools để AI ngày càng thông minh và hiệu quả hơn. [cite: 14, 16]

Thiết kế này cung cấp một nền tảng vững chắc, kết hợp sức mạnh của LLM (thông qua `SuperSalesCareAgent` và `openai` SDK) với khả năng thực thi hành động có cấu trúc (thông qua Mastra `steps` đóng vai trò là tools).