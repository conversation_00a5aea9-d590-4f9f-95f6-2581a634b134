# Chatbot AI Chăm Sóc Khách Hàng Thông Minh - Hướng Dẫn Triển Khai

## 🎯 Tổng Quan

Dự án này xây dựng một chatbot AI thông minh chuyên biệt cho chăm sóc kh<PERSON>ch hàng, sử dụng Mastra framework để tạo ra một "nhân viên AI" có khả năng:

- **Phân tích và hiểu sâu** nhu cầu khách hàng như nhân viên có kinh nghiệm
- **Ra quyết định thông minh** dựa trên ngữ cảnh và tình huống cụ thể
- **Sử dụng tools chuyên biệt** để truy cập dữ liệu an toàn với tenant isolation
- **Áp dụng tư duy được training** từ các best practices customer service
- **Tương tác tự nhiên** với memory và context awareness
- **Workflow thông minh** với khả năng suspend/resume cho human handoff

## 📚 Tài Liệu

- [📋 Kế Hoạch Triển Khai Chi Tiết](./INTELLIGENT_CHATBOT_IMPLEMENTATION_PLAN.md)
- [🔍 Ngữ Cảnh Dự Án](./INTELLIGENT_CHATBOT_CONTEXT.md)
- [📊 Tiến Độ Triển Khai](./IMPLEMENTATION_PROGRESS.md)
- [🎨 Thiết Kế Support Agent](./SUPPORT_AGENT.md)

## 🏗️ Kiến Trúc Hệ Thống

```mermaid
graph TB
    Customer[👤 Customer] --> Agent[🧠 Intelligent Customer Service Agent]

    Agent --> Memory[🧠 Memory System]
    Agent --> Tools[�️ Specialized Tools]
    Agent --> Workflow[⚡ Smart Workflows]

    Tools --> ProductTool[🛍️ Product Search Tool]
    Tools --> OrderTool[📦 Order Management Tool]
    Tools --> SupportTool[🎧 Support Request Tool]
    Tools --> EscalationTool[👨‍💼 Human Handoff Tool]

    ProductTool --> Supabase[(🗄️ Supabase)]
    OrderTool --> Supabase
    SupportTool --> Supabase

    Memory --> PostgreSQL[(� PostgreSQL)]
    Memory --> Weaviate[(🔍 Weaviate)]

    Workflow --> Response[📝 Intelligent Response]
```

## 🚀 Bắt Đầu Nhanh

### 1. Cài Đặt Dependencies: Đã xong
```

### 2. Cấu Hình Environment: Đã xong
```

### 3. Tạo Intelligent Customer Service Agent

```typescript
// src/mastra/agents/intelligent-customer-service.ts
import { Agent } from "@mastra/core/agent";
import { Memory } from "@mastra/memory";
import { google } from "@ai-sdk/google";
import { openai } from "@ai-sdk/openai";
import {
  productSearchTool,
  orderManagementTool,
  customerSupportTool,
  escalationTool,
  faqSearchTool
} from "../tools/customer-service";

// Memory system với PostgreSQL và vector search
const memory = new Memory({
  storage: postgresStore,
  vector: pgVectorClient,
  embedder: openai.embedding("text-embedding-3-small"),
  options: {
    lastMessages: 10,
    semanticRecall: {
      topK: 5,
      messageRange: 3,
    },
    workingMemory: {
      enabled: true,
      template: `
      Bạn đang hỗ trợ khách hàng với thông tin sau:
      - Lịch sử hội thoại: {lastMessages}
      - Thông tin liên quan: {semanticRecall}
      - Ngữ cảnh hiện tại: {currentContext}

      Hãy sử dụng thông tin này để đưa ra phản hồi phù hợp và cá nhân hóa.
      `,
    },
  },
});

export const intelligentCustomerServiceAgent = new Agent({
  name: "Intelligent Customer Service Agent",
  instructions: `
  Bạn là một nhân viên chăm sóc khách hàng AI thông minh và có kinh nghiệm.

  TƯ DUY VÀ PHƯƠNG PHÁP LÀM VIỆC:

  1. PHÂN TÍCH TÌNH HUỐNG (Situation Analysis):
     - Đọc hiểu kỹ tin nhắn của khách hàng
     - Xác định cảm xúc và mức độ khẩn cấp
     - Nhận diện loại yêu cầu (thông tin, hỗ trợ, khiếu nại)
     - Kiểm tra lịch sử tương tác trước đó

  2. TƯ DUY GIẢI QUYẾT VẤN ĐỀ (Problem-Solving Mindset):
     - Tìm hiểu nguyên nhân gốc rễ của vấn đề
     - Đánh giá các giải pháp có thể
     - Ưu tiên giải pháp nhanh chóng và hiệu quả
     - Dự đoán các câu hỏi tiếp theo của khách hàng

  3. GIAO TIẾP THÔNG MINH (Intelligent Communication):
     - Sử dụng ngôn ngữ phù hợp với cảm xúc khách hàng
     - Thể hiện sự đồng cảm và hiểu biết
     - Giải thích rõ ràng, dễ hiểu
     - Đưa ra các bước hành động cụ thể

  4. SỬ DỤNG TOOLS HIỆU QUẢ:
     - productSearchTool: Khi khách hàng hỏi về sản phẩm
     - orderManagementTool: Khi cần kiểm tra/xử lý đơn hàng
     - customerSupportTool: Khi cần tạo ticket hỗ trợ
     - escalationTool: Khi vấn đề phức tạp cần nhân viên
     - faqSearchTool: Khi cần tìm thông tin từ knowledge base

  5. THEO DÕI VÀ CẢI THIỆN:
     - Kiểm tra mức độ hài lòng của khách hàng
     - Đề xuất các bước tiếp theo nếu cần
     - Ghi nhớ thông tin quan trọng cho lần tương tác sau

  QUY TẮC VÀNG:
  - Luôn đặt khách hàng làm trung tâm
  - Lắng nghe trước khi đưa ra giải pháp
  - Không bao giờ nói "không thể" mà đưa ra lựa chọn thay thế
  - Sử dụng memory để cá nhân hóa trải nghiệm
  - Đảm bảo tenant_id trong mọi tool call
  `,

  model: google("gemini-2.0-flash-exp"),

  tools: {
    productSearchTool,
    orderManagementTool,
    customerSupportTool,
    escalationTool,
    faqSearchTool,
  },

  memory,
});
```

### 4. Tạo Specialized Tools

```typescript
// src/mastra/tools/customer-service/product-search.ts
import { createTool } from "@mastra/core/tools";
import { z } from "zod";
import { supabaseAdmin } from "../../../config/supabase";

export const productSearchTool = createTool({
  id: "product-search",
  description: "Tìm kiếm sản phẩm thông minh với semantic search và filtering",
  inputSchema: z.object({
    query: z.string().describe("Từ khóa tìm kiếm sản phẩm"),
    tenantId: z.string().describe("Tenant ID (bắt buộc)"),
    category: z.string().optional().describe("Danh mục sản phẩm"),
    priceRange: z.object({
      min: z.number().optional(),
      max: z.number().optional(),
    }).optional().describe("Khoảng giá"),
    limit: z.number().default(5).describe("Số lượng kết quả"),
  }),
  execute: async ({ context }) => {
    const { query, tenantId, category, priceRange, limit } = context;

    if (!tenantId) {
      throw new Error("tenant_id is mandatory for all operations");
    }

    try {
      // Build query với tenant isolation
      let dbQuery = supabaseAdmin
        .from('products')
        .select(`
          id,
          name,
          description,
          price,
          sale_price,
          images,
          is_active,
          stock_quantity,
          category:categories(name),
          variants:product_variants(
            id,
            name,
            price,
            stock_quantity
          )
        `)
        .eq('tenant_id', tenantId)
        .eq('is_active', true);

      // Semantic search
      if (query) {
        dbQuery = dbQuery.or(`name.ilike.%${query}%,description.ilike.%${query}%`);
      }

      // Category filter
      if (category) {
        dbQuery = dbQuery.eq('categories.name', category);
      }

      // Price range filter
      if (priceRange) {
        if (priceRange.min) {
          dbQuery = dbQuery.gte('price', priceRange.min);
        }
        if (priceRange.max) {
          dbQuery = dbQuery.lte('price', priceRange.max);
        }
      }

      const { data, error } = await dbQuery.limit(limit);

      if (error) {
        return {
          success: false,
          message: "Xin lỗi, hiện tại không thể tìm kiếm sản phẩm. Vui lòng thử lại sau.",
          products: []
        };
      }

      // Format customer-friendly data
      const products = data?.map(product => ({
        name: product.name,
        description: product.description,
        price: formatPrice(product.sale_price || product.price),
        originalPrice: product.sale_price ? formatPrice(product.price) : null,
        category: product.category?.name || "Chưa phân loại",
        images: product.images || [],
        inStock: product.stock_quantity > 0,
        variants: product.variants?.map(v => ({
          name: v.name,
          price: formatPrice(v.price),
          inStock: v.stock_quantity > 0
        })) || []
      })) || [];

      return {
        success: true,
        message: `Tìm thấy ${products.length} sản phẩm phù hợp`,
        products,
        searchQuery: query,
        totalFound: products.length
      };

    } catch (error) {
      console.error('Product search error:', error);
      return {
        success: false,
        message: "Đã có lỗi xảy ra khi tìm kiếm. Chúng tôi sẽ khắc phục sớm nhất.",
        products: []
      };
    }
  },
});

function formatPrice(price: number): string {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND'
  }).format(price);
}
```

### 5. Tạo Intelligent Workflow với Human Handoff

```typescript
// src/mastra/workflows/intelligent-customer-service.ts
import { createWorkflow, createStep } from "@mastra/core/workflows";
import { z } from "zod";

export const intelligentCustomerServiceWorkflow = createWorkflow({
  id: "intelligent-customer-service",
  inputSchema: z.object({
    customerMessage: z.string(),
    tenantId: z.string(),
    customerId: z.string().optional(),
    conversationHistory: z.array(z.any()).optional(),
    urgency: z.enum(["low", "medium", "high"]).default("medium"),
  }),
  outputSchema: z.object({
    response: z.string(),
    actions: z.array(z.string()).optional(),
    escalated: z.boolean().default(false),
    confidence: z.number(),
    nextSteps: z.array(z.string()).optional(),
  }),
});

// Step 1: Customer Intent Analysis với AI
const customerAnalysisStep = createStep({
  id: "customer-analysis",
  description: "Phân tích ý định và cảm xúc khách hàng bằng AI",
  inputSchema: intelligentCustomerServiceWorkflow.inputSchema,
  outputSchema: z.object({
    intent: z.string(),
    emotion: z.string(),
    complexity: z.enum(["simple", "medium", "complex"]),
    requiresHuman: z.boolean(),
  }),
  execute: async ({ inputData, mastra }) => {
    const agent = mastra.getAgent("intelligentCustomerServiceAgent");

    const analysis = await agent.generate([
      {
        role: "system",
        content: `Phân tích tin nhắn khách hàng và xác định:
        1. Intent (ý định): product_inquiry, order_status, complaint, support_request, etc.
        2. Emotion (cảm xúc): happy, neutral, frustrated, angry, confused
        3. Complexity (độ phức tạp): simple, medium, complex
        4. RequiresHuman (cần nhân viên): true/false

        Tin nhắn: "${inputData.customerMessage}"
        Lịch sử: ${JSON.stringify(inputData.conversationHistory || [])}
        `
      }
    ], {
      output: z.object({
        intent: z.string(),
        emotion: z.string(),
        complexity: z.enum(["simple", "medium", "complex"]),
        requiresHuman: z.boolean(),
      })
    });

    return analysis.object;
  },
});

// Step 2: AI Response Generation
const aiResponseStep = createStep({
  id: "ai-response",
  description: "Tạo phản hồi thông minh bằng AI",
  inputSchema: z.object({
    customerMessage: z.string(),
    tenantId: z.string(),
    analysis: z.object({
      intent: z.string(),
      emotion: z.string(),
      complexity: z.enum(["simple", "medium", "complex"]),
      requiresHuman: z.boolean(),
    }),
  }),
  outputSchema: z.object({
    response: z.string(),
    toolsUsed: z.array(z.string()),
    confidence: z.number(),
  }),
  execute: async ({ inputData, mastra }) => {
    const agent = mastra.getAgent("intelligentCustomerServiceAgent");

    // Nếu cần nhân viên, suspend workflow
    if (inputData.analysis.requiresHuman) {
      return {
        response: "Tôi hiểu vấn đề của bạn cần được xử lý cẩn thận. Để đảm bảo hỗ trợ tốt nhất, tôi sẽ chuyển bạn đến nhân viên chuyên môn. Vui lòng chờ trong giây lát.",
        toolsUsed: ["escalationTool"],
        confidence: 0.9,
      };
    }

    // Tạo phản hồi với context đầy đủ
    const response = await agent.generate([
      {
        role: "user",
        content: inputData.customerMessage,
      }
    ], {
      runtimeContext: {
        "tenant-id": inputData.tenantId,
        "customer-intent": inputData.analysis.intent,
        "customer-emotion": inputData.analysis.emotion,
      }
    });

    return {
      response: response.text,
      toolsUsed: response.toolCalls?.map(tc => tc.toolName) || [],
      confidence: 0.85,
    };
  },
});

// Step 3: Human Handoff (conditional)
const humanHandoffStep = createStep({
  id: "human-handoff",
  description: "Chuyển giao cho nhân viên khi cần thiết",
  inputSchema: z.object({
    customerMessage: z.string(),
    tenantId: z.string(),
    analysis: z.any(),
    aiResponse: z.any(),
  }),
  outputSchema: z.object({
    escalated: z.boolean(),
    handoffReason: z.string().optional(),
    ticketId: z.string().optional(),
  }),
  execute: async ({ inputData, mastra }) => {
    if (inputData.analysis.requiresHuman || inputData.analysis.complexity === "complex") {
      const agent = mastra.getAgent("intelligentCustomerServiceAgent");

      // Sử dụng escalation tool
      const escalation = await agent.generate([
        {
          role: "user",
          content: `Tạo ticket escalation cho: ${inputData.customerMessage}`
        }
      ], {
        tools: ["escalationTool"],
        runtimeContext: {
          "tenant-id": inputData.tenantId,
        }
      });

      return {
        escalated: true,
        handoffReason: `Complex issue requiring human expertise: ${inputData.analysis.intent}`,
        ticketId: "TICKET_" + Date.now(),
      };
    }

    return {
      escalated: false,
    };
  },
});

// Commit workflow với conditional branching
intelligentCustomerServiceWorkflow
  .then(customerAnalysisStep)
  .then(aiResponseStep)
  .then(humanHandoffStep, {
    condition: ({ getStepResult }) => {
      const analysis = getStepResult("customer-analysis");
      return analysis.requiresHuman || analysis.complexity === "complex";
    }
  })
  .commit();
```

## 🔧 Cấu Hình Mastra

```typescript
// src/mastra/index.ts
import { Mastra } from "@mastra/core";
import { Memory } from "@mastra/memory";
import { PgStore } from "@mastra/pg";
import { intelligentCustomerServiceAgent } from "./agents/intelligent-customer-service";
import { intelligentCustomerServiceWorkflow } from "./workflows/intelligent-customer-service";

// Define Customer Service Context Type (simplified)
type CustomerServiceContext = {
  "customer-intent": string;
  "customer-emotion": string;
  "conversation-stage": string;
  "tenant-id": string; // MANDATORY
  "customer-id": string;
  "language": "vi" | "en";
};

// Memory configuration
const memory = new Memory({
  storage: new PgStore({
    connectionString: process.env.PG_CONNECTION_STRING!,
  }),
  embedder: openai.embedding("text-embedding-3-small"),
  options: {
    lastMessages: 10,
    semanticRecall: {
      topK: 5,
      messageRange: 3,
    },
    workingMemory: {
      enabled: true,
      template: `
      Ngữ cảnh hỗ trợ khách hàng:
      - Lịch sử hội thoại gần đây: {lastMessages}
      - Thông tin liên quan: {semanticRecall}
      - Tình huống hiện tại: {currentContext}

      Sử dụng thông tin này để:
      1. Hiểu rõ nhu cầu khách hàng
      2. Cung cấp phản hồi cá nhân hóa
      3. Tránh lặp lại thông tin đã biết
      4. Đưa ra giải pháp phù hợp
      `,
    },
  },
});

export const mastra = new Mastra({
  agents: {
    intelligentCustomerServiceAgent,
  },
  workflows: {
    intelligentCustomerServiceWorkflow,
  },
  memory,
  server: {
    middleware: [
      // Tenant ID validation middleware
      async (c, next) => {
        const tenantId = c.req.header("x-tenant-id") || c.req.query("tenant_id");

        if (!tenantId) {
          return c.json({ error: "tenant_id is required" }, 400);
        }

        const runtimeContext = c.get<CustomerServiceContext>("runtimeContext");
        runtimeContext.set("tenant-id", tenantId);

        await next();
      },

      // Customer context middleware
      async (c, next) => {
        const runtimeContext = c.get<CustomerServiceContext>("runtimeContext");

        // Set defaults
        runtimeContext.set("customer-emotion", "neutral");
        runtimeContext.set("conversation-stage", "inquiry");
        runtimeContext.set("language", "vi");

        // Override with headers if provided
        const customerId = c.req.header("x-customer-id");
        const language = c.req.header("x-language");

        if (customerId) runtimeContext.set("customer-id", customerId);
        if (language) runtimeContext.set("language", language as "vi" | "en");

        await next();
      },
    ],
  },
});
```

## 🧪 Testing

### 1. Test Intelligent Customer Service Agent

```typescript
// test/intelligent-agent.test.ts
import { mastra } from "../src/mastra";

async function testIntelligentAgent() {
  const agent = mastra.getAgent("intelligentCustomerServiceAgent");

  // Test với context đầy đủ
  const response = await agent.generate([
    {
      role: "user",
      content: "Tôi muốn tìm áo thun nam size L màu đen giá dưới 500k",
    },
  ], {
    runtimeContext: {
      "tenant-id": "tenant-123",
      "customer-id": "customer-456",
      "customer-intent": "product-inquiry",
      "customer-emotion": "neutral",
      "language": "vi",
    }
  });

  console.log("Intelligent Agent Response:", response.text);
  console.log("Tools Used:", response.toolCalls?.map(tc => tc.toolName));
}

testIntelligentAgent();
```

### 2. Test Customer Service Workflow

```typescript
// test/customer-workflow.test.ts
import { mastra } from "../src/mastra";

async function testCustomerWorkflow() {
  const workflow = mastra.getWorkflow("intelligentCustomerServiceWorkflow");
  const run = workflow.createRun();

  const result = await run.start({
    inputData: {
      customerMessage: "Đơn hàng #12345 của tôi chưa được giao, đã 5 ngày rồi!",
      tenantId: "tenant-123",
      customerId: "customer-456",
      urgency: "high",
    },
  });

  console.log("Workflow Result:", result);
  console.log("Escalated:", result.escalated);
  console.log("Confidence:", result.confidence);
}

testCustomerWorkflow();
```

### 3. Test Memory System

```typescript
// test/memory.test.ts
import { mastra } from "../src/mastra";

async function testMemorySystem() {
  const agent = mastra.getAgent("intelligentCustomerServiceAgent");

  // Conversation 1
  await agent.generate([
    {
      role: "user",
      content: "Tôi tên là Nguyễn Văn A, tôi muốn mua giày thể thao",
    },
  ], {
    runtimeContext: {
      "tenant-id": "tenant-123",
      "customer-id": "customer-456",
    }
  });

  // Conversation 2 - Agent should remember customer name
  const response = await agent.generate([
    {
      role: "user",
      content: "Bạn có thể gợi ý giày nào phù hợp với tôi không?",
    },
  ], {
    runtimeContext: {
      "tenant-id": "tenant-123",
      "customer-id": "customer-456",
    }
  });

  console.log("Memory-aware response:", response.text);
  // Should include customer name and previous context
}

testMemorySystem();
```

## 📊 Monitoring & Analytics

### Performance Metrics
- Response time < 2 seconds
- Accuracy rate > 95%
- Customer satisfaction > 4.5/5

### Security Metrics
- Zero security incidents
- 100% audit trail coverage
- SQL injection prevention: 100%

## 🔒 Bảo Mật

### Row Level Security (RLS)
```sql
-- Enable RLS on products table
ALTER TABLE products ENABLE ROW LEVEL SECURITY;

-- Create tenant isolation policy
CREATE POLICY "tenant_isolation" ON products
  FOR ALL USING (tenant_id = current_setting('app.current_tenant_id'));
```

### Permission Matrix
| Role | Products | Orders | Customers | System |
|------|----------|--------|-----------|--------|
| Customer | Read | Own | Own | - |
| Staff | Read/Write | Manage | Read/Write | - |
| Admin | Full | Full | Full | Full |

## 🚀 Deployment

### Development
```bash
# Start Mastra dev server
yarn dev:mastra

# Start Express server
yarn dev:express
```

### Production
```bash
# Build project
yarn build

# Start production server
yarn start
```

## 📞 Hỗ Trợ

- **Documentation**: Xem các file trong thư mục `docs/`
- **Issues**: Tạo issue trong repository
- **Team**: Liên hệ development team

## 🎉 Kết Luận

Hệ thống Intelligent Customer Service Agent này tận dụng tối đa Mastra stack để tạo ra một "nhân viên AI" thông minh với:

### 🧠 **Trí Tuệ Nhân Tạo**
- **Phân tích sâu**: Hiểu ý định, cảm xúc và ngữ cảnh khách hàng
- **Ra quyết định**: Chọn tools và strategies phù hợp
- **Học hỏi**: Sử dụng memory để cải thiện trải nghiệm

### 🛠️ **Tools Chuyên Biệt**
- **Pre-defined tools** thay vì SQL agent để đảm bảo bảo mật
- **Tenant isolation** bắt buộc trong mọi operation
- **Customer-friendly responses** với data masking

### ⚡ **Workflows Thông Minh**
- **Conditional branching** dựa trên complexity
- **Human handoff** khi cần thiết
- **Suspend/resume** capabilities

### 🧠 **Memory System**
- **Context awareness** qua các cuộc hội thoại
- **Personalization** dựa trên lịch sử
- **Semantic recall** cho thông tin liên quan

### 🔒 **Bảo Mật & Tuân Thủ**
- **Mandatory tenant_id** validation
- **Customer data protection**
- **No SQL injection risks**
- **Audit trails** đầy đủ

Hệ thống này không chỉ là chatbot mà là một **nhân viên AI được training** với tư duy customer service chuyên nghiệp, sử dụng công nghệ Mastra tiên tiến để mang lại trải nghiệm khách hàng vượt trội.

Bắt đầu triển khai ngay với Phase 1 trong `IMPLEMENTATION_PROGRESS.md`! 🚀
