# Customer Service Agent - Code Example

## Tổng Quan

Đây là code example cho Customer Service Agent chuyên bi<PERSON>, tập trung vào:
- Giao tiếp thân thiện với khách hàng
- Truy cập database an toàn với mandatory tenant_id
- Bảo vệ thông tin nhạy cảm
- Phản hồi customer-friendly

## 1. Customer Service Agent

```typescript
import { Agent } from "@mastra/core/agent";
import { google } from "@ai-sdk/google";
import { 
  customerProductSearchTool,
  customerOrderLookupTool,
  customerSupportTool,
  escalateToHumanTool
} from "../tools/customer-service";

export const customerServiceAgent = new Agent({
  name: "Customer Service Agent",
  instructions: async ({ runtimeContext }) => {
    const tenantId = runtimeContext.get("tenant-id");
    const customerEmotion = runtimeContext.get("customer-emotion");
    const customerIntent = runtimeContext.get("customer-intent");
    
    if (!tenantId) {
      throw new Error("tenant_id is mandatory for customer service operations");
    }
    
    return `
    Bạn là Customer Service Agent chuyên nghiệp và thân thiện.
    
    THÔNG TIN NGỮ CẢNH:
    - Tenant ID: ${tenantId} (LUÔN sử dụng trong mọi truy vấn)
    - Cảm xúc khách hàng: ${customerEmotion}
    - Ý định khách hàng: ${customerIntent}
    
    NHIỆM VỤ CHÍNH:
    1. Giao tiếp thân thiện, lịch sự và chuyên nghiệp
    2. Hiểu rõ nhu cầu và cảm xúc của khách hàng
    3. Cung cấp thông tin chính xác và hữu ích
    4. Bảo vệ thông tin nhạy cảm (không tiết lộ internal IDs, technical details)
    5. Đảm bảo tenant isolation trong mọi truy vấn database
    
    QUY TẮC GIAO TIẾP:
    - Luôn sử dụng ngôn ngữ thân thiện, dễ hiểu
    - Tránh thuật ngữ kỹ thuật phức tạp
    - Thể hiện sự đồng cảm khi khách hàng gặp vấn đề
    - Cung cấp giải pháp cụ thể và khả thi
    - Xin lỗi chân thành khi có sự cố
    
    QUY TẮC BẢO MẬT:
    - KHÔNG BAO GIỜ tiết lộ internal IDs, database structure
    - KHÔNG hiển thị technical error messages cho khách hàng
    - CHỈ truy cập dữ liệu của tenant hiện tại
    - LUÔN mask thông tin nhạy cảm (email, phone một phần)
    
    CÁCH XỬ LÝ CÁC TÌNH HUỐNG:
    - Khách hàng hỏi về sản phẩm: Sử dụng customerProductSearchTool
    - Khách hàng hỏi về đơn hàng: Sử dụng customerOrderLookupTool  
    - Khách hàng cần hỗ trợ: Sử dụng customerSupportTool
    - Vấn đề phức tạp: Sử dụng escalateToHumanTool
    `;
  },
  
  model: google("gemini-2.0-flash-exp"),
  
  tools: {
    customerProductSearchTool,
    customerOrderLookupTool,
    customerSupportTool,
    escalateToHumanTool,
  },
});
```

## 2. Customer-Safe SQL Agent

```typescript
import { Agent } from "@mastra/core/agent";
import { openai } from "@ai-sdk/openai";
import { 
  tenantSafeSQLTool,
  customerDataMaskingTool,
  customerErrorHandlingTool
} from "../tools/sql-security";

export const customerSafeSQLAgent = new Agent({
  name: "Customer Safe SQL Agent",
  instructions: `
  Bạn là SQL Security Agent chuyên tạo queries an toàn cho customer service.
  
  QUY TẮC TUYỆT ĐỐI:
  1. CHỈ tạo SELECT queries (không INSERT/UPDATE/DELETE)
  2. LUÔN bao gồm tenant_id trong WHERE clause
  3. CHỈ truy cập tables: products, orders, customers
  4. MASK thông tin nhạy cảm trước khi trả về
  5. Tạo error messages thân thiện với khách hàng
  
  TEMPLATE SQL AN TOÀN:
  SELECT [customer_safe_fields] 
  FROM [allowed_table] 
  WHERE tenant_id = $1 
  AND [additional_conditions]
  
  THÔNG TIN CẦN MASK:
  - Internal IDs (chỉ hiển thị order number, product code)
  - Email (hiển thị m***@example.com)
  - Phone (hiển thị 090***1234)
  - Technical details (created_at, updated_at, etc.)
  
  KHÔNG BAO GIỜ:
  - Tạo queries không có tenant_id
  - Truy cập tables không được phép
  - Hiển thị raw technical data
  - Tạo queries có thể modify data
  `,
  
  model: openai("gpt-4o"), // Sử dụng model mạnh nhất cho security
  
  tools: {
    tenantSafeSQLTool,
    customerDataMaskingTool,
    customerErrorHandlingTool,
  },
});
```

## 3. Customer Service Tools

```typescript
import { createTool } from "@mastra/core/tools";
import { z } from "zod";
import { supabaseAdmin } from "../../../config/supabase";

// Tool tìm kiếm sản phẩm an toàn cho khách hàng
export const customerProductSearchTool = createTool({
  id: "customer-product-search",
  description: "Tìm kiếm sản phẩm an toàn cho customer service",
  inputSchema: z.object({
    query: z.string().describe("Từ khóa tìm kiếm sản phẩm"),
    tenantId: z.string().describe("Tenant ID (bắt buộc)"),
    limit: z.number().default(5).describe("Số lượng sản phẩm trả về"),
  }),
  execute: async ({ context }) => {
    const { query, tenantId, limit } = context;
    
    if (!tenantId) {
      throw new Error("tenant_id is mandatory for all operations");
    }
    
    try {
      // Query an toàn với tenant isolation
      const { data, error } = await supabaseAdmin
        .from('products')
        .select(`
          name,
          description,
          price,
          is_active,
          category:categories(name)
        `)
        .eq('tenant_id', tenantId)
        .eq('is_active', true)
        .ilike('name', `%${query}%`)
        .limit(limit);
      
      if (error) {
        return {
          success: false,
          message: "Xin lỗi, hiện tại không thể tìm kiếm sản phẩm. Vui lòng thử lại sau.",
          products: []
        };
      }
      
      // Format dữ liệu customer-friendly
      const customerFriendlyProducts = data?.map(product => ({
        name: product.name,
        description: product.description,
        price: new Intl.NumberFormat('vi-VN', {
          style: 'currency',
          currency: 'VND'
        }).format(product.price),
        category: product.category?.name || "Chưa phân loại",
        available: product.is_active
      })) || [];
      
      return {
        success: true,
        message: `Tìm thấy ${customerFriendlyProducts.length} sản phẩm phù hợp`,
        products: customerFriendlyProducts
      };
      
    } catch (error) {
      console.error('Customer product search error:', error);
      return {
        success: false,
        message: "Đã có lỗi xảy ra khi tìm kiếm. Chúng tôi sẽ khắc phục sớm nhất.",
        products: []
      };
    }
  },
});

// Tool tra cứu đơn hàng an toàn
export const customerOrderLookupTool = createTool({
  id: "customer-order-lookup",
  description: "Tra cứu thông tin đơn hàng cho khách hàng",
  inputSchema: z.object({
    orderCode: z.string().describe("Mã đơn hàng"),
    tenantId: z.string().describe("Tenant ID (bắt buộc)"),
    customerPhone: z.string().optional().describe("Số điện thoại khách hàng để xác thực"),
  }),
  execute: async ({ context }) => {
    const { orderCode, tenantId, customerPhone } = context;
    
    if (!tenantId) {
      throw new Error("tenant_id is mandatory for all operations");
    }
    
    try {
      let query = supabaseAdmin
        .from('orders')
        .select(`
          order_code,
          status,
          total_amount,
          created_at,
          shipping_address,
          customer:customers(name, phone)
        `)
        .eq('tenant_id', tenantId)
        .eq('order_code', orderCode);
      
      // Thêm xác thực số điện thoại nếu có
      if (customerPhone) {
        query = query.eq('customers.phone', customerPhone);
      }
      
      const { data, error } = await query.single();
      
      if (error || !data) {
        return {
          success: false,
          message: "Không tìm thấy đơn hàng với mã này. Vui lòng kiểm tra lại mã đơn hàng.",
          order: null
        };
      }
      
      // Format thông tin customer-friendly
      const customerFriendlyOrder = {
        orderCode: data.order_code,
        status: getCustomerFriendlyStatus(data.status),
        totalAmount: new Intl.NumberFormat('vi-VN', {
          style: 'currency',
          currency: 'VND'
        }).format(data.total_amount),
        orderDate: new Date(data.created_at).toLocaleDateString('vi-VN'),
        shippingAddress: data.shipping_address,
        customerName: data.customer?.name || "Khách hàng",
        // Mask số điện thoại
        customerPhone: maskPhoneNumber(data.customer?.phone || "")
      };
      
      return {
        success: true,
        message: "Đã tìm thấy thông tin đơn hàng",
        order: customerFriendlyOrder
      };
      
    } catch (error) {
      console.error('Customer order lookup error:', error);
      return {
        success: false,
        message: "Đã có lỗi xảy ra khi tra cứu đơn hàng. Vui lòng liên hệ hỗ trợ.",
        order: null
      };
    }
  },
});

// Helper functions
function getCustomerFriendlyStatus(status: string): string {
  const statusMap: Record<string, string> = {
    'pending': 'Đang xử lý',
    'confirmed': 'Đã xác nhận',
    'shipping': 'Đang giao hàng',
    'delivered': 'Đã giao hàng',
    'cancelled': 'Đã hủy',
    'returned': 'Đã trả hàng'
  };
  
  return statusMap[status] || 'Không xác định';
}

function maskPhoneNumber(phone: string): string {
  if (!phone || phone.length < 4) return phone;
  
  const start = phone.substring(0, 3);
  const end = phone.substring(phone.length - 4);
  const middle = '*'.repeat(phone.length - 7);
  
  return `${start}${middle}${end}`;
}
```

## 4. Customer Service Workflow

```typescript
import { createWorkflow, createStep } from "@mastra/core/workflows";
import { z } from "zod";

export const customerServiceWorkflow = createWorkflow({
  id: "customer-service-workflow",
  inputSchema: z.object({
    customerMessage: z.string(),
    tenantId: z.string(),
    customerId: z.string().optional(),
    conversationHistory: z.array(z.any()).optional(),
  }),
  outputSchema: z.object({
    response: z.string(),
    actions: z.array(z.string()).optional(),
    escalated: z.boolean().default(false),
  }),
});

// Step 1: Validate tenant và customer context
const validateContextStep = createStep({
  id: "validate-context",
  description: "Validate tenant ID và customer context",
  inputSchema: customerServiceWorkflow.inputSchema,
  outputSchema: z.object({
    isValid: z.boolean(),
    tenantId: z.string(),
    errorMessage: z.string().optional(),
  }),
  execute: async ({ inputData }) => {
    if (!inputData.tenantId) {
      return {
        isValid: false,
        tenantId: "",
        errorMessage: "Tenant ID is required for customer service"
      };
    }
    
    return {
      isValid: true,
      tenantId: inputData.tenantId
    };
  },
});

// Step 2: Customer intent analysis
const customerIntentStep = createStep({
  id: "customer-intent-analysis",
  description: "Phân tích ý định và cảm xúc khách hàng",
  inputSchema: z.object({
    customerMessage: z.string(),
    tenantId: z.string(),
  }),
  outputSchema: z.object({
    intent: z.string(),
    emotion: z.string(),
    urgency: z.enum(["low", "medium", "high"]),
  }),
  execute: async ({ inputData, mastra }) => {
    const agent = mastra.getAgent("customerServiceAgent");
    
    const analysis = await agent.generate([
      {
        role: "user",
        content: `Phân tích ý định và cảm xúc trong tin nhắn: "${inputData.customerMessage}"`
      }
    ], {
      output: z.object({
        intent: z.string(),
        emotion: z.string(),
        urgency: z.enum(["low", "medium", "high"]),
      })
    });
    
    return analysis.object;
  },
});

// Commit workflow
customerServiceWorkflow
  .then(validateContextStep)
  .then(customerIntentStep)
  .commit();
```

## Kết Luận

Code example này tập trung vào:

1. **Customer-Centric Design**: Mọi thứ đều tối ưu cho trải nghiệm khách hàng
2. **Mandatory Tenant Isolation**: Luôn đảm bảo tenant_id trong mọi operation
3. **Data Protection**: Mask thông tin nhạy cảm và chỉ hiển thị thông tin cần thiết
4. **Customer-Friendly Responses**: Error messages và data format thân thiện
5. **Security First**: Chỉ SELECT operations và strict validation

Hệ thống này đảm bảo chatbot hoạt động an toàn, hiệu quả và tập trung vào customer service.
