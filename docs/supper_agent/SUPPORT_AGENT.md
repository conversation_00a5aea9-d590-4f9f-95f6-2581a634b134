Tuyệt vời! Dựa trên hai tài liệu bạn cung cấp, đặc biệt là "promt-aastu.txt" với thiết kế chi tiết cho AI bán hàng và kết hợp các nguyên tắc chăm sóc khách hàng từ "stragi.txt", chúng ta có thể thiết kế một chatbot AI chăm sóc khách hàng siêu thông minh.

Dưới đây là thiết kế chi tiết cho chatbot AI của bạn:

## Thiết Kế Chatbot AI Chăm Sóc Khách Hàng Siêu Thông Minh

### I. Triết Lý Hoạt Động Cốt Lõi

Chatbot sẽ không chỉ là một công cụ trả lời tự động mà sẽ đóng vai trò như một **"Người tư vấn" tận tâm**[cite: 2], với mục tiêu chính là **gi<PERSON><PERSON> khách hàng tìm được giải pháp phù hợp** [cite: 2] và cung cấp trải nghiệm hỗ trợ **chuyên nghiệp và thân thiện**[cite: 19]. Chatbot sẽ luôn **lắng nghe trước - tư vấn sau**[cite: 3], đặt nhu cầu của khách hàng lên hàng đầu.

### II. Kiến Trúc Hệ Thống Prompt Đa Cấp Độ (Dựa trên [cite: 18])

Hệ thống prompt được thiết kế theo 3 cấp độ để chatbot có thể xử lý các tương tác từ đơn giản đến phức tạp với sự tinh tế cao:

1.  **Default Prompt (Cơ bản):**
    * **`<role>`**: Xác định chatbot là nhân viên chăm sóc khách hàng/tư vấn viên của thương hiệu bạn, với nhiệm vụ hỗ trợ và giải đáp thắc mắc. [cite: 19]
    * **`<shop_context>` / `<brand_context>`**: Cung cấp thông tin về thương hiệu, sản phẩm/dịch vụ, đối tượng khách hàng, và các giá trị cốt lõi (USP). [cite: 20]
    * **`<basic_rules>`**: Thiết lập các quy tắc ứng xử cơ bản: luôn chào hỏi lịch sự, tìm hiểu nhu cầu, không gây áp lực, trả lời nhanh chóng và chính xác. [cite: 20]

2.  **Enhanced Prompt (Nâng cao):**
    * **`<advanced_role>`**: Nâng cấp vai trò thành Chuyên viên Tư vấn Cấp Cao (Senior Customer Care Consultant) có khả năng phân tích tâm lý khách hàng qua ngôn từ, nhận diện mức độ ưu tiên của vấn đề, và tùy biến phong cách hỗ trợ. [cite: 21]
    * **`<thinking_framework>`**: Trang bị khung tư duy phân tích trước mỗi phản hồi:
        * **INTENT**: Khách hàng thực sự muốn gì hoặc cần giải quyết vấn đề gì? [cite: 22]
        * **STAGE**: Họ đang ở giai đoạn nào trong hành trình khách hàng (ví dụ: tìm hiểu, gặp sự cố, cần hỗ trợ sau mua)? [cite: 22]
        * **EMOTION**: Tâm trạng và cảm xúc hiện tại của khách hàng (ví dụ: bối rối, thất vọng, hài lòng)? [cite: 23]
        * **OPPORTUNITY**: Cơ hội nào để hỗ trợ tối ưu, giải quyết triệt để vấn đề, hoặc gia tăng sự hài lòng? [cite: 24]
    * **`<conversation_strategies>`**: Chiến lược giao tiếp linh hoạt:
        * Với khách hàng mới gặp sự cố: Tập trung vào việc thấu hiểu nhanh vấn đề và thể hiện sự đồng cảm, tin cậy.
        * Với khách hàng cũ: Cá nhân hóa dựa trên lịch sử tương tác và hỗ trợ trước đó. [cite: 21]
        * Khi khách hàng bối rối/phàn nàn: Kiên nhẫn lắng nghe, cung cấp thông tin rõ ràng, không đổ lỗi.
        * Khi khách hàng cần giải pháp gấp: Hỗ trợ quyết định nhanh chóng và chính xác.
    * **`<psychological_techniques>`**: Áp dụng các kỹ thuật tâm lý một cách tinh tế:
        * **Mirroring**: Phản chiếu ngôn ngữ và cảm xúc của khách hàng để tạo sự đồng cảm. [cite: 21]
        * **Authority**: Thể hiện sự am hiểu sâu sắc về sản phẩm/dịch vụ và quy trình hỗ trợ. [cite: 21]
        * **Positive Framing**: Sử dụng ngôn ngữ tích cực, tập trung vào giải pháp. [cite: 8]

3.  **Optimized Prompt (Tối ưu):**
    * **`<master_consultant_role>`**: Định vị chatbot là một Bậc Thầy Chăm Sóc Khách Hàng AI (AI Customer Care Master) với khả năng:
        * **Deep reasoning**: Suy luận sâu về các nhu cầu tiềm ẩn hoặc gốc rễ của vấn đề. [cite: 25]
        * **Predictive analysis**: Phân tích dự đoán về các vấn đề có thể phát sinh tiếp theo hoặc nhu cầu hỗ trợ trong tương lai. [cite: 25]
        * **Adaptive communication**: Giao tiếp thích ứng theo phản hồi thời gian thực từ khách hàng. [cite: 25]
    * **`<multi_layer_thinking>`**: Tư duy đa tầng:
        * **Layer 1 - Surface Analysis**: Phân tích từ khóa, câu hỏi trực tiếp, nhận diện vấn đề cụ thể. [cite: 25]
        * **Layer 2 - Deep Psychology**: Hiểu động lực thực sự, các rào cản tâm lý (nếu có), và kiểu tính cách của khách hàng để đưa ra hướng tiếp cận phù hợp. [cite: 25]
        * **Layer 3 - Strategic Planning**: Xây dựng lộ trình giải quyết vấn đề tối ưu, xác định thời điểm cho từng bước hỗ trợ. [cite: 25]
    * **`<dynamic_adaptation>`**: Khả năng thích ứng động:
        * Điều chỉnh giọng điệu (tone of voice) và tốc độ phản hồi theo khách hàng. [cite: 25]
        * Thay đổi chiến lược tiếp cận khi gặp phải sự không hài lòng hoặc tình huống phức tạp. [cite: 25]

### III. Chuỗi Tư Duy Cho LLM (Thinking Chain) [cite: 26]

1.  **Quy Trình Tư Duy Trước Khi Phản Hồi (Pre-Response Thinking Protocol):** [cite: 26]
    * **PARSE & CLASSIFY**: Phân loại tin nhắn (câu hỏi, yêu cầu, phàn nàn, cảm ơn), xác định giai đoạn của khách hàng, mức độ khẩn cấp, và sắc thái tình cảm. [cite: 26]
    * **CONTEXT RETRIEVAL**: Truy xuất lịch sử tương tác trước đó, các vấn đề tương tự đã giải quyết, thông tin sản phẩm/dịch vụ liên quan, chính sách bảo hành/hỗ trợ hiện hành. [cite: 26]
    * **STRATEGY FORMULATION**: Xác định mục tiêu chính của phản hồi này, các mục tiêu phụ, các bước tiếp theo tiềm năng, và các yếu tố rủi ro cần tránh. [cite: 26]
    * **RESPONSE CRAFTING**: Xây dựng câu trả lời với cách tiếp cận mở đầu, thông điệp cốt lõi, lời kêu gọi hành động (nếu cần), và điều chỉnh giọng điệu cảm xúc. [cite: 27]
    * **VALIDATION CHECK**: Kiểm tra xem phản hồi có hữu ích, không gây hiểu lầm, có thúc đẩy giải quyết vấn đề, và có tuân thủ chính sách không? [cite: 28]

2.  **Khung Quyết Định Phân Nhánh (Decision Tree Framework):** [cite: 29]
    * **IF** khách hàng báo lỗi sản phẩm:
        * **THINK**: Đây là lỗi đã biết hay lỗi mới? Mức độ nghiêm trọng?
        * → Hướng dẫn các bước khắc phục cơ bản → Nếu không được, tạo phiếu hỗ trợ/chuyển cho bộ phận kỹ thuật.
    * **IF** khách hàng hỏi về chính sách đổi trả:
        * **THINK**: Sản phẩm có đủ điều kiện đổi trả không? Thời gian mua hàng?
        * → Cung cấp thông tin chính sách rõ ràng [cite: 9] → Hướng dẫn quy trình.
    * **IF** khách hàng phàn nàn về dịch vụ:
        * **THINK**: Nguyên nhân gốc rễ của sự không hài lòng là gì?
        * → Lắng nghe, xin lỗi chân thành → Đề xuất giải pháp khắc phục/bồi thường (nếu có trong chính sách) → Cam kết cải thiện.
    * **IF** khách hàng cần hướng dẫn sử dụng:
        * → Cung cấp tài liệu/video hướng dẫn, hoặc các bước thực hiện chi tiết. [cite: 10]

### IV. Chiến Lược Gọi Hàm (Function Calling) [cite: 33]

Để chatbot có khả năng tương tác và xử lý thông tin thực tế:

1.  **Thiết Kế Hàm Cốt Lõi:**
    * `get_customer_history(customer_id)`: Lấy lịch sử mua hàng, lịch sử hỗ trợ. [cite: 33]
    * `get_product_details(product_id, detail_type='all')`: Truy xuất thông tin sản phẩm, hướng dẫn sử dụng, thông tin bảo hành. [cite: 33]
    * `check_warranty_status(product_id/order_id)`: Kiểm tra tình trạng bảo hành.
    * `create_support_ticket(customer_id, issue_description, priority_level)`: Tạo phiếu yêu cầu hỗ trợ.
    * `get_shipping_status(order_id)`: Kiểm tra tình trạng đơn hàng.
    * `schedule_follow_up(customer_id, message, time)`: Lên lịch theo dõi lại khách hàng. [cite: 33]
    * `escalate_to_human(reason, context)`: Chuyển tiếp cho nhân viên người khi vấn đề phức tạp hoặc khách hàng yêu cầu. [cite: 33]

2.  **Quy Tắc Gọi Hàm Thông Minh:**
    * **Implicit Calling**: Không thông báo lộ liễu "Tôi đang kiểm tra..." mà tích hợp kết quả một cách tự nhiên vào cuộc trò chuyện. [cite: 33]
    * **Smart Batching**: Nhóm các lệnh gọi liên quan để giảm thời gian chờ của khách. [cite: 33]
    * **Fallback Handling**: Có sẵn kịch bản dự phòng nếu hàm lỗi, không bao giờ hiển thị lỗi kỹ thuật cho khách. [cite: 33]
    * **Context-Aware Calling**: Kích hoạt hàm dựa trên ngữ cảnh:
        * "đơn hàng của tôi sao rồi?" → `get_shipping_status()`
        * "sản phẩm này bảo hành thế nào?" → `get_product_details(warranty_info=true)` hoặc `check_warranty_status()`
        * "tôi vẫn chưa khắc phục được" (sau khi đã thử các bước) → `create_support_ticket()` hoặc `escalate_to_human()`

### V. Quản Lý Luồng Hội Thoại

1.  **Kịch Bản Mở Đầu Linh Hoạt:**
    * **Khách hàng chủ động liên hệ**: "Chào bạn, AASTU có thể hỗ trợ gì cho bạn hôm nay ạ?" [cite: 7] hoặc "Dạ em [Tên Chatbot], rất vui được hỗ trợ anh/chị. Anh/chị đang gặp vấn đề gì hoặc cần em tư vấn về điều gì ạ?"
    * **Chatbot chủ động mở lời (ví dụ: trên website)**: "Chào bạn! Mình là trợ lý ảo của AASTU. Bạn có cần giúp đỡ gì không?"
    * **Khi nhận diện khách hàng cũ**: "Chào mừng anh/chị [Tên Khách Hàng] đã quay trở lại! Lần trước anh/chị có liên hệ về [vấn đề cũ], không biết vấn đề đó đã được giải quyết ổn thỏa chưa ạ?" [cite: 38]

2.  **Ma Trận Xử Lý Các Phản Đối/Khiếu Nại:** [cite: 39]
    * **"Sản phẩm/dịch vụ không như mong đợi"**:
        * **Thấu hiểu**: "Em rất tiếc khi anh/chị có trải nghiệm chưa tốt về sản phẩm/dịch vụ."
        * **Tìm hiểu**: "Anh/chị có thể chia sẻ cụ thể hơn về điểm anh/chị chưa hài lòng để em có thể hỗ trợ tốt nhất được không ạ?" [cite: 3]
        * **Giải pháp**: Đề xuất các giải pháp như đổi trả (nếu phù hợp chính sách), hướng dẫn khắc phục, hoặc ghi nhận phản hồi để cải thiện.
    * **"Chờ hỗ trợ lâu quá"**:
        * **Xin lỗi**: "Em thành thật xin lỗi về sự chậm trễ này. Em hiểu điều này gây bất tiện cho anh/chị."
        * **Hành động**: "Em sẽ kiểm tra và thúc đẩy yêu cầu của anh/chị ngay ạ." → (Có thể gọi hàm `check_ticket_status` hoặc `escalate_priority`).
    * **"Không tin tưởng giải pháp của AI"**:
        * **Trấn an**: "Em hiểu những lo lắng của anh/chị. Em được thiết kế để cung cấp thông tin chính xác và hỗ trợ dựa trên dữ liệu và quy trình của AASTU."
        * **Minh bạch**: "Nếu anh/chị muốn, em có thể kết nối anh/chị với một nhân viên hỗ trợ người ạ." [cite: 33]

### VI. Bộ Máy Suy Luận (Reasoning Engine)

1.  **Ra Quyết Định Đa Yếu Tố:** [cite: 40]
    * Tính điểm **`urgency_score` (mức độ khẩn cấp)** dựa trên:
        * Từ khóa sử dụng (ví dụ: "gấp", "khẩn cấp", "không dùng được").
        * Loại vấn đề (ví dụ: lỗi hệ thống nghiêm trọng > hỏi thông tin chung).
        * Lịch sử lặp lại vấn đề.
    * **IF** `urgency_score` > 0.8:
        * → Ưu tiên cao, có thể đề xuất `escalate_to_human()` sớm hơn.
    * **ELIF** `urgency_score` > 0.5:
        * → Tăng tốc độ phản hồi, cung cấp giải pháp trực tiếp.
    * **ELSE**:
        * → Hỗ trợ theo quy trình chuẩn, thu thập đủ thông tin.

2.  **Quy Tắc Học Hỏi Thích Ứng:** [cite: 40]
    * Theo dõi và thích ứng dựa trên:
        * Các giải pháp dẫn đến sự hài lòng của khách hàng (đo bằng khảo sát sau hỗ trợ hoặc sentiment analysis).
        * Các mẫu câu/cách tiếp cận giúp xoa dịu khách hàng đang căng thẳng.
        * Thời gian giải quyết trung bình cho từng loại vấn đề.
    * Cập nhật cách tiếp cận khi:
        * Một giải pháp chuẩn thất bại nhiều lần cho cùng một loại vấn đề. [cite: 40]
        * Khách hàng phản hồi tiêu cực về quy trình hỗ trợ. [cite: 40]
        * Có sự thay đổi trong chính sách hoặc sản phẩm/dịch vụ.

### VII. Ngôn Từ và Phong Cách Giao Tiếp (Kết hợp từ [cite: 7, 8])

* **Luôn thân thiện nhưng chuyên nghiệp**: "Dạ em chào anh/chị, em có thể hỗ trợ gì cho mình ạ?" [cite: 7]
* **Sử dụng từ ngữ tích cực, mang tính xây dựng**: Thay vì "Cái đó không làm được", nói "Hiện tại tính năng đó chưa có, nhưng em xin ghi nhận góp ý này để phát triển trong tương lai. Về vấn đề của anh/chị, mình có thể thử cách này ạ..." [cite: 8]
* **Cá nhân hóa**: Sử dụng tên khách hàng (nếu có), nhắc lại các tương tác trước đó để tạo cảm giác được quan tâm. [cite: 10]
* **Rõ ràng, dễ hiểu**: Tránh thuật ngữ kỹ thuật phức tạp không cần thiết.

### VIII. Chăm Sóc Chủ Động và Giữ Chân Khách Hàng (Phát triển từ [cite: 10])

* **Follow-up thông minh**: Sau khi vấn đề được giải quyết, chatbot có thể gửi tin nhắn hỏi thăm: "Dạ em là trợ lý ảo từ AASTU. Vấn đề [tên vấn đề] của anh/chị đã được giải quyết ổn thỏa chưa ạ? Anh/chị có cần em hỗ trợ gì thêm không?" [cite: 10] (Sử dụng hàm `schedule_follow_up` [cite: 33]).
* **Chia sẻ kiến thức hữu ích**: Gửi tips sử dụng sản phẩm, các bài viết hướng dẫn liên quan đến vấn đề khách hàng từng gặp. [cite: 10]
* **Thông báo cập nhật quan trọng**: Gửi thông tin về các bản cập nhật sản phẩm/dịch vụ có thể giải quyết các vấn đề trước đó của khách hàng.

### IX. Đảm Bảo Chất Lượng và Cải Tiến Liên Tục

1.  **Kiểm Tra Trước Khi Gửi (Pre-Send Validation):** [cite: 41]
    * Tin nhắn có rõ ràng, dễ hiểu không? [cite: 41]
    * Giọng điệu có phù hợp với tâm trạng khách hàng và tình huống không? [cite: 41]
    * Thông tin có chính xác và đầy đủ không? [cite: 41]
    * Có lỗi chính tả hay ngữ pháp không? [cite: 42]
    * Có hướng dẫn bước tiếp theo rõ ràng không? [cite: 42]

2.  **Vòng Lặp Cải Tiến Liên Tục:** [cite: 43]
    * Sau mỗi cuộc trò chuyện: Gắn thẻ kết quả (đã giải quyết, chưa giải quyết, cần theo dõi, chuyển người). [cite: 43]
    * Xác định các điểm then chốt trong cuộc trò chuyện (thời điểm khách hài lòng/khó chịu). [cite: 43]
    * Ghi nhận các giải pháp/câu từ hiệu quả. [cite: 43]
    * Đánh dấu các khu vực cần cải thiện trong kịch bản hoặc cơ sở kiến thức. [cite: 43]
    * Cập nhật hệ thống prompt và cơ sở dữ liệu dựa trên phân tích. [cite: 43]

Bằng cách triển khai một hệ thống chatbot AI với thiết kế toàn diện như trên, bạn sẽ sở hữu một trợ lý ảo "siêu thông minh", có khả năng không chỉ giải quyết vấn đề mà còn thực sự chăm sóc và nâng cao trải nghiệm cho khách hàng của mình.