# Message Batching Quick Start Guide

## 🚀 Khởi Động Nhanh

### 1. Build & Deploy
```bash
# Build project
yarn build

# Start integrated server (API + Workers)
yarn start:integrated

# Hoặc start riêng biệt:
yarn start                    # API Server
yarn start:message-batching   # Message Batching Worker
```

### 2. <PERSON><PERSON><PERSON> Tra Hệ Thống
```bash
# Kiểm tra queue stats
curl http://localhost:3000/api/system/message-batching/stats

# Response mẫu:
{
  "success": true,
  "data": {
    "buffer": {
      "totalBatches": 0,
      "totalMessages": 0,
      "avgMessagesPerBatch": 0
    },
    "queue": {
      "waiting": 0,
      "active": 0,
      "completed": 0,
      "failed": 0,
      "delayed": 0
    },
    "health": {
      "isHealthy": true
    }
  }
}
```

## 📊 Cách Hoạt Động

### Before (Cũ)
```
User Message → API → Immediate Processing → Response (2-3s)
```

### After (Mới - Message Batching)
```
User Message 1 → Buffer (wait 10s)
User Message 2 → Buffer (same conversation)
User Message 3 → <PERSON>uffer (same conversation)
                ↓ (after 10s delay)
               Batch Processing → Single Response (covers all 3 messages)
```

## ⚙️ Cấu Hình

### Default Settings
- **Delay Time**: 10 giây (configurable per bot)
- **Max Messages per Batch**: Không giới hạn
- **Immediate Processing**: Messages có keywords urgent hoặc attachments

### Cập Nhật Cấu Hình Bot
```bash
curl -X PUT http://localhost:3000/api/system/message-batching/config \
  -H "Content-Type: application/json" \
  -d '{
    "bot_id": "your_bot_id",
    "delay_time": 8
  }'
```

## 🔍 Monitoring

### Real-time Stats
```bash
curl http://localhost:3000/api/system/message-batching/stats
```

### Manual Cleanup
```bash
curl -X POST http://localhost:3000/api/system/message-batching/cleanup
```

### Debug Redis Keys
```bash
redis-cli KEYS "msg_buffer:*"
redis-cli KEYS "batch_job:*"
```

## 🎯 Expected Results

- **60-80% reduction** in API calls
- **50% reduction** in server resources
- **70% reduction** in processing costs
- Better conversation context understanding
- Improved response quality

## ⚠️ Important Notes

1. **Backward Compatibility**: Hệ thống cũ vẫn hoạt động như fallback
2. **Urgent Messages**: Vẫn được xử lý ngay lập tức
3. **Image Messages**: Được xử lý ngay lập tức
4. **Credit System**: Được tích hợp sẵn

## 🐛 Troubleshooting

### High Memory Usage
```bash
# Check Redis memory
redis-cli INFO memory

# Manual cleanup
curl -X POST http://localhost:3000/api/system/message-batching/cleanup
```

### Worker Not Processing
```bash
# Check worker logs
tail -f logs/message-batching-worker.log

# Restart worker
pm2 restart message-batching-worker
```

### Queue Stuck
```bash
# Check queue health
curl http://localhost:3000/api/system/message-batching/stats

# Look for high failed/stalled job counts
```

---

**🎉 Congratulations!** Message Batching system đã sẵn sàng và sẽ tự động tối ưu hóa performance của chatbot system của bạn! 