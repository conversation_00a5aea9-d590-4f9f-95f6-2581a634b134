# Hướng dẫn sử dụng các tùy chọn đồng bộ sản phẩm

## Tổng quan

Hệ thống đồng bộ sản phẩm hỗ trợ nhiều tùy chọn để kiểm soát cách thức đồng bộ dữ liệu từ các nền tảng bên ngo<PERSON> (Haravan, Sapo) vào hệ thống Supabase.

## Các tùy chọn đồng bộ

### 1. `full_sync` (boolean, mặc định: false)
- **M<PERSON><PERSON> đích**: Đồng bộ toàn bộ sản phẩm từ nền tảng
- **Khi true**: Tự động bật `force_update=true`
- **Khi false**: Chỉ đồng bộ sản phẩm mới hoặc đã thay đổi

### 2. `force_update` (boolean, mặc định: false)
- **<PERSON><PERSON><PERSON> đích**: <PERSON><PERSON><PERSON> nhật lại tất cả sản phẩm, kể cả đã tồn tại
- **Khi true**: <PERSON><PERSON><PERSON> nhật tất cả sản phẩm (bỏ qua kiểm tra đã tồn tại)
- **Khi false**: Chỉ đồng bộ sản phẩm mới (bỏ qua sản phẩm đã tồn tại)

### 3. `limit` (number, mặc định: 50)
- **Mục đích**: Số lượng sản phẩm tối đa mỗi trang
- **Khuyến nghị**: 50-100 để tránh timeout

### 4. `updated_at_min` (string, tùy chọn)
- **Mục đích**: Chỉ đồng bộ sản phẩm được cập nhật sau thời điểm này
- **Định dạng**: ISO 8601 (ví dụ: "2024-01-01T00:00:00Z")

## Logic đồng bộ

### Chế độ đồng bộ sản phẩm mới (mặc định)
```
full_sync=false, force_update=false
```
- ✅ Kiểm tra sản phẩm đã tồn tại
- ✅ Chỉ đồng bộ sản phẩm mới
- ⏭️ Bỏ qua sản phẩm đã tồn tại
- 🚀 Nhanh và tiết kiệm tài nguyên

### Chế độ cập nhật toàn bộ
```
full_sync=true hoặc force_update=true
```
- 🔄 Cập nhật tất cả sản phẩm
- 📝 Ghi đè dữ liệu đã tồn tại
- 🕐 Chậm hơn nhưng đảm bảo dữ liệu mới nhất

## Ví dụ sử dụng

### 1. Đồng bộ sản phẩm mới (khuyến nghị cho sử dụng hàng ngày)
```json
{
  "token": "your_haravan_token",
  "tenant_id": "your_tenant_id",
  "limit": 50,
  "full_sync": false,
  "force_update": false
}
```

### 2. Đồng bộ toàn bộ (khuyến nghị cho lần đầu hoặc sau khi có thay đổi lớn)
```json
{
  "token": "your_haravan_token",
  "tenant_id": "your_tenant_id",
  "limit": 50,
  "full_sync": true
}
```

### 3. Đồng bộ sản phẩm được cập nhật trong 7 ngày qua
```json
{
  "token": "your_haravan_token",
  "tenant_id": "your_tenant_id",
  "limit": 50,
  "updated_at_min": "2024-01-20T00:00:00Z",
  "force_update": true
}
```

## Hiệu suất và tối ưu hóa

### Tối ưu hóa đã áp dụng:
1. **Kiểm tra hàng loạt**: Sử dụng `checkMultipleProductsExist()` thay vì kiểm tra từng sản phẩm
2. **Phân trang thông minh**: Chia nhỏ dữ liệu để tránh timeout
3. **Batch processing**: Xử lý theo batch để tránh quá tải
4. **Logging chi tiết**: Theo dõi tiến trình và kết quả

### Khuyến nghị sử dụng:
- **Đồng bộ hàng ngày**: `full_sync=false, force_update=false`
- **Đồng bộ lần đầu**: `full_sync=true`
- **Sau khi sửa lỗi dữ liệu**: `force_update=true`

## Monitoring và Debug

### Log messages quan trọng:
- `🚀 Bắt đầu đồng bộ`: Thông tin cấu hình
- `📊 Kết quả kiểm tra`: Số lượng sản phẩm mới/đã tồn tại
- `⏭️ Bỏ qua sản phẩm`: Sản phẩm đã tồn tại (khi force_update=false)
- `➕ Sản phẩm mới`: Sản phẩm sẽ được đồng bộ
- `✅ Đã lưu sản phẩm`: Kết quả lưu thành công

### Troubleshooting:
1. **Không có sản phẩm mới**: Kiểm tra `updated_at_min` và `force_update`
2. **Đồng bộ chậm**: Giảm `limit` hoặc kiểm tra kết nối mạng
3. **Lỗi timeout**: Kiểm tra kết nối mạng và giảm batch size
