# Cập nhật cấu trúc giá sản phẩm - Logic chuẩn

## Giải pháp cuối cùng

Sau khi phân tích, chúng ta đã quyết định sử dụng **logic chuẩn** để đồng nhất giữa Platform API và Database:

### Logic chuẩn được áp dụng:
- **`price`**: <PERSON><PERSON><PERSON> bán hiện tại (selling price)
- **`compare_at_price`**: Giá gốc để so sánh (original price)
- **Logic giảm giá**: `compare_at_price` > `price` = có giảm giá

### Lợi ích của logic chuẩn:
- ✅ Đồng nhất giữa Platform API và Database
- ✅ Dễ hiểu và maintain
- ✅ Mapping trực tiếp 1:1, không cần logic phức tạp
- ✅ Tuân theo chuẩn của các platform thương mại điện tử

## Thực hiện cập nhật

### 1. Cập nhật cấu trúc Database

Đã thực hiện migration để áp dụng logic chuẩn:

```sql
-- Cấu trúc cuối cùng (Logic chuẩn)
ALTER TABLE products
  ADD COLUMN price NUMERIC DEFAULT 0 NOT NULL,           -- Giá bán hiện tại (selling price)
  ADD COLUMN compare_at_price NUMERIC DEFAULT 0;         -- Giá gốc để so sánh (original price)

ALTER TABLE product_variants
  ADD COLUMN price NUMERIC DEFAULT 0 NOT NULL,           -- Giá bán hiện tại (selling price)
  ADD COLUMN compare_at_price NUMERIC DEFAULT 0;         -- Giá gốc để so sánh (original price)
```

### 2. Cập nhật Adapter Logic

**Haravan Adapter** (`src/services/sync/adapters/haravan.adapter.ts`):
```typescript
// Mapping trực tiếp 1:1 (Logic chuẩn)
// Sản phẩm chính
price: product.variants?.[0]?.price || product.price || 0,
compare_at_price: product.variants?.[0]?.compare_at_price || product.compare_at_price || null,

// Variants
price: variant.price || 0,
compare_at_price: variant.compare_at_price || null,
```

**Sapo Adapter** (`src/services/sync/adapters/sapo.adapter.ts`):
```typescript
// Mapping trực tiếp 1:1 (Logic chuẩn)
price: product.variants?.[0]?.price || product.price || 0,
compare_at_price: product.variants?.[0]?.compare_at_price || product.compare_at_price || null,
```

### 3. Cập nhật Tool Logic

Các tool trong `src/mastra/tools/product/` đã được cập nhật để sử dụng logic chuẩn:

```typescript
// Logic mới (chuẩn)
const finalPrice = parseFloat(product.price);                    // Giá bán hiện tại
const originalPrice = product.compare_at_price ?
  parseFloat(product.compare_at_price) : finalPrice;             // Giá gốc để so sánh
const isOnSale = product.compare_at_price &&
  parseFloat(product.price) < parseFloat(product.compare_at_price);
```

## Kết quả

### Logic chuẩn được áp dụng:
```
Platform API: price=18990000, compare_at_price=20990000
Database: price=18990000, compare_at_price=20990000  ✅ ĐÚNG (1:1 mapping)
Tool hiển thị: 18.990.000 VNĐ (Giảm 10% từ 20.990.000 VNĐ)  ✅ ĐÚNG
```

### Ví dụ cụ thể:
**Samsung Galaxy S21:**
- Platform: price=18,990,000 VNĐ, compare_at_price=20,990,000 VNĐ
- Database: price=18,990,000 VNĐ, compare_at_price=20,990,000 VNĐ
- Hiển thị: "18.990.000 VNĐ (Giảm 10% từ 20.990.000 VNĐ)"

## Ý nghĩa các trường (Logic chuẩn)

### Database Schema:
- **`price`**: Giá bán hiện tại (selling price)
- **`compare_at_price`**: Giá gốc để so sánh (original price, có thể null)

### Platform API:
- **`price`**: Giá bán hiện tại (selling price)
- **`compare_at_price`**: Giá gốc để so sánh (original price)

### Tool Logic:
- **`final_price`**: Lấy từ `price` database (giá bán)
- **`original_price`**: Lấy từ `compare_at_price` database, fallback về `price` nếu null
- **`is_on_sale`**: `price` < `compare_at_price`

## Files đã cập nhật

### 1. Database Migration (7 bước)
- Cập nhật schema `products` và `product_variants`
- Migrate dữ liệu hiện có với logic chuẩn

### 2. Adapters (Mapping 1:1)
- **Haravan Adapter**: `src/services/sync/adapters/haravan.adapter.ts`
- **Sapo Adapter**: `src/services/sync/adapters/sapo.adapter.ts`

### 3. Product Processor (Core Logic)
- **Product Processor**: `src/services/sync/product-processor.ts`
  - ✅ Sửa tất cả `sale_price` → `compare_at_price`
  - ✅ Cập nhật minimal insert/update để bao gồm avatar + images
  - ✅ Đảm bảo hình ảnh được lưu ngay cả khi có lỗi

### 4. Tools (Display Logic)
- **Main Tool**: `src/mastra/tools/product/get-product-details-production.ts`
- **Search Tool**: `src/mastra/tools/product/search.ts`
- **Interface**: `src/services/sync/types.ts`

## Vấn đề đã sửa

### 1. ❌ Lỗi Database Schema
**Trước**: Code sử dụng `sale_price` nhưng database có `compare_at_price`
**Sau**: ✅ Tất cả code sử dụng `compare_at_price` đồng nhất

### 2. ❌ Lỗi Hình ảnh không lưu
**Trước**: Minimal insert/update không có avatar + images
**Sau**: ✅ Luôn lưu avatar + images ngay cả khi có lỗi

### 3. ❌ Lỗi Logic giá
**Trước**: Logic phức tạp, dễ nhầm lẫn
**Sau**: ✅ Logic chuẩn, mapping trực tiếp 1:1

## Lưu ý quan trọng

- ✅ **Logic chuẩn**: Database và Platform sử dụng cùng logic
- ✅ **Mapping đơn giản**: 1:1 mapping, không cần logic phức tạp
- ✅ **Dễ maintain**: Logic rõ ràng, dễ hiểu
- ✅ **Tương thích**: Tuân theo chuẩn thương mại điện tử
- ✅ **Tất cả sản phẩm**: Đã được migrate với logic chuẩn
- ✅ **Hiển thị chính xác**: Giá bán (Giảm % từ giá gốc)
- ✅ **Hình ảnh đầy đủ**: Luôn lưu avatar + images
- ✅ **Xử lý SKU**: Đúng logic cho trường hợp không có SKU
