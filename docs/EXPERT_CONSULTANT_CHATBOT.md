# Expert Consultant Chatbot - Hướng dẫn sử dụng

## Tổng quan

Expert Consultant Chatbot là một hệ thống AI chatbot thông minh được xây dựng trên nền tả<PERSON>, c<PERSON> khả năng tư vấn chuyên nghiệp như một chuyên gia thực sự. Chatbot này đư<PERSON>c thiết kế với quy trình bài bản để phân tích, tìm kiếm thông tin và đưa ra phản hồi chính xác nhất.

## Kiến trúc hệ thống

### 1. Các Agent chuyên biệt

- **Expert Consultant Agent**: Agent ch<PERSON>h điều phối tổng thể
- **Conversation Analyzer Agent**: <PERSON><PERSON>ên phân tích hội thoại và ngữ cảnh
- **Information Retrieval Agent**: <PERSON><PERSON><PERSON><PERSON> tì<PERSON> kiếm thông tin từ nhiều nguồn
- **Decision Making Agent**: <PERSON>y<PERSON><PERSON> đ<PERSON>h giá và ra quyết định
- **Response Generation Agent**: <PERSON><PERSON><PERSON><PERSON> tạ<PERSON> phản hồi thông minh

### 2. Các Tools hỗ trợ

- **Conversation Analyzer Tool**: Phân tích ý định và ngữ cảnh
- **Knowledge Base Search Tool**: Tìm kiếm thông tin từ Weaviate và Supabase
- **Information Evaluator Tool**: Đánh giá độ đầy đủ thông tin
- **Response Generator Tool**: Tạo phản hồi cá nhân hóa
- **Clarifying Questions Tool**: Tạo câu hỏi làm rõ thông minh

### 3. Quy trình xử lý

```
Tin nhắn người dùng
        ↓
1. Phân tích hội thoại (Conversation Analysis)
        ↓
2. Tìm kiếm thông tin (Information Retrieval)
        ↓
3. Đánh giá độ đầy đủ (Information Evaluation)
        ↓
4. Ra quyết định (Decision Making)
        ↓
5. Tạo phản hồi (Response Generation)
        ↓
Phản hồi cho người dùng
```

## API Endpoints

### 1. Chat với Expert Consultant

**POST** `/api/expert-consultant/chat`

```json
{
  "message": "Tôi muốn tìm áo thun màu đen size M",
  "conversation_history": [
    {
      "role": "user",
      "content": "Xin chào"
    },
    {
      "role": "assistant", 
      "content": "Chào bạn! Tôi có thể giúp gì cho bạn?"
    }
  ],
  "customer_context": {
    "name": "Nguyễn Văn A",
    "preferences": ["áo thun", "màu đen"],
    "purchase_history": ["áo polo", "quần jean"]
  },
  "use_simple_mode": true
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "response": "Tôi sẽ giúp bạn tìm áo thun màu đen size M phù hợp...",
    "thread_id": "thread_123",
    "type": "simple_response"
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### 2. Chat trực tiếp

**POST** `/api/expert-consultant/direct`

```json
{
  "message": "Tôi cần hỗ trợ về đơn hàng",
  "thread_id": "thread_123"
}
```

### 3. Phân tích hội thoại

**POST** `/api/expert-consultant/analyze`

```json
{
  "message": "Tôi muốn đổi sản phẩm",
  "conversation_history": []
}
```

### 4. Tìm kiếm thông tin

**POST** `/api/expert-consultant/search`

```json
{
  "query": "áo thun nam",
  "search_type": "products",
  "limit": 5
}
```

### 5. Thông tin hệ thống

**GET** `/api/expert-consultant/health`

**GET** `/api/expert-consultant/agents`

## Tính năng nổi bật

### 1. Phân tích thông minh
- Hiểu ý định người dùng
- Trích xuất thực thể quan trọng
- Phân tích ngữ cảnh hội thoại

### 2. Tìm kiếm đa nguồn
- Semantic search với Weaviate
- Database search với Supabase
- Kết hợp nhiều nguồn thông tin

### 3. Quyết định thông minh
- Đánh giá độ đầy đủ thông tin
- Quyết định hành động phù hợp
- Tạo câu hỏi làm rõ khi cần

### 4. Phản hồi cá nhân hóa
- Dựa trên lịch sử hội thoại
- Tùy chỉnh theo sở thích khách hàng
- Gợi ý thông minh

### 5. Nguyên tắc cốt lõi
- **Không bịa thông tin**: Chỉ sử dụng dữ liệu có sẵn
- **Phân tích trước hành động**: Hiểu rõ trước khi phản hồi
- **Tìm kiếm đầy đủ**: Thu thập thông tin cần thiết
- **Tổng hợp thông minh**: Kết hợp từ nhiều nguồn

## Cấu hình

### Environment Variables

```env
# Mastra Core
LLM_MODEL=gpt-4o
OPENAI_API_KEY=your_openai_key

# Database
PG_CONNECTION_STRING=postgresql://...
SUPABASE_URL=https://...
SUPABASE_ANON_KEY=your_supabase_key

# Weaviate
WEAVIATE_URL=http://localhost:8080
WEAVIATE_API_KEY=your_weaviate_key
```

### Models sử dụng

- **Expert Consultant Agent**: GPT-4o (model mạnh nhất)
- **Conversation Analyzer**: GPT-4o-mini (tối ưu chi phí)
- **Information Retrieval**: GPT-4o-mini
- **Decision Making**: GPT-4o
- **Response Generation**: GPT-4o

## Ví dụ sử dụng

### 1. Tìm kiếm sản phẩm

```bash
curl -X POST http://localhost:3000/api/expert-consultant/chat \
  -H "Content-Type: application/json" \
  -d '{
    "message": "Tôi cần tìm áo sơ mi trắng size L",
    "use_simple_mode": true
  }'
```

### 2. Hỗ trợ đơn hàng

```bash
curl -X POST http://localhost:3000/api/expert-consultant/chat \
  -H "Content-Type: application/json" \
  -d '{
    "message": "Đơn hàng ORD123456 của tôi đang ở trạng thái gì?",
    "use_simple_mode": true
  }'
```

### 3. Tư vấn chung

```bash
curl -X POST http://localhost:3000/api/expert-consultant/chat \
  -H "Content-Type: application/json" \
  -d '{
    "message": "Tôi nên chọn áo gì cho buổi hẹn hò?",
    "customer_context": {
      "preferences": ["phong cách trẻ trung", "màu sáng"]
    }
  }'
```

## Monitoring và Debug

### Logs

Hệ thống ghi log chi tiết cho từng bước:

```
=== EXPERT CONSULTANT REQUEST ===
Message: Tôi muốn tìm áo thun
History length: 2
Simple mode: true

=== CONVERSATION ANALYSIS ===
Intent: product_search
Entities: ["áo thun"]
Confidence: 0.8

=== INFORMATION RETRIEVAL ===
Search type: products
Results found: 5
Strategy: Weaviate + Supabase

=== EXPERT CONSULTANT RESPONSE ===
Response type: complete_answer
Confidence: 0.9
```

### Health Check

```bash
curl http://localhost:3000/api/expert-consultant/health
```

## Mở rộng

### Thêm Agent mới

1. Tạo agent trong `src/mastra/agents/index.ts`
2. Đăng ký trong `src/mastra/index.ts`
3. Tạo tools tương ứng trong `src/mastra/tools/expert-consultant/`

### Thêm nguồn dữ liệu

1. Cập nhật `searchKnowledgeBaseTool`
2. Thêm function tìm kiếm mới
3. Cập nhật logic kết hợp kết quả

### Tùy chỉnh prompts

Chỉnh sửa các prompts trong `src/mastra/prompts/system.ts`:
- `expertConsultantPrompt`
- `conversationAnalyzerPrompt`
- `informationRetrievalPrompt`
- `decisionMakingPrompt`
- `responseGenerationPrompt`
