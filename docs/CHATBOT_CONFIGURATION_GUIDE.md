# 🤖 Hướng Dẫn C<PERSON><PERSON>ình Chatbot Linh Hoạt

## 📋 Tổng Quan

Hệ thống chatbot hiện đã được cấu hình để lấy `delay_time` và `instruction` từ database một cách linh hoạt theo từng chatbot configuration. Điều này cho phép:

- **Delay Time**: Tùy chỉnh thời gian trễ (1-60 giây) cho từng bot
- **Instruction**: <PERSON><PERSON><PERSON> hình hướng dẫn AI riêng biệt cho từng bot
- **Available Functions**: Bật/tắt các chức năng theo nhu cầu (tương lai)

## 🗄️ Cấu Trúc Database

### Bảng `chatbot_configurations`
```sql
CREATE TABLE chatbot_configurations (
  id UUID PRIMARY KEY,
  name VARCHAR(255),
  description TEXT,
  instruction TEXT,
  delay_time INTEGER DEFAULT 10, -- Thờ<PERSON> gian trễ (giây)
  available_functions TEXT[], -- Danh sách functions có thể sử dụng
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

### Bảng `chatbot_channels`
```sql
CREATE TABLE chatbot_channels (
  id UUID PRIMARY KEY,
  tenant_id UUID,
  bot_id UUID REFERENCES chatbot_configurations(id),
  inbox_id VARCHAR(255) UNIQUE,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

## 🔧 API Endpoints

### 1. Lấy Cấu Hình Chatbot
```http
GET /api/system/chatbot/config/:inbox_id
```

**Response:**
```json
{
  "success": true,
  "data": {
    "channel": {
      "bot_id": "uuid",
      "tenant_id": "uuid",
      "is_active": true
    },
    "configuration": {
      "id": "uuid",
      "name": "Customer Service Bot",
      "instruction": "Bạn là trợ lý AI...",
      "delay_time": 15,
      "available_functions": ["searchProducts", "createOrder"],
      "is_active": true
    }
  }
}
```

### 2. Cập Nhật Cấu Hình Chatbot
```http
PUT /api/system/message-batching/config
```

**Request Body:**
```json
{
  "bot_id": "uuid",
  "delay_time": 15,
  "instruction": "Bạn là trợ lý AI chuyên nghiệp..."
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "name": "Customer Service Bot",
    "instruction": "Bạn là trợ lý AI chuyên nghiệp...",
    "delay_time": 15,
    "updated_at": "2024-01-01T00:00:00Z"
  },
  "message": "Cập nhật cấu hình chatbot thành công"
}
```

## ⚙️ Cách Hoạt Động

### 1. Luồng Lấy Cấu Hình
```
Webhook → Agent Controller → getChatbotByInboxId() → Database Query
                                ↓
                    Lấy instruction + delay_time + available_functions
                                ↓
                    Message Buffer Service → getBotTimeout()
                                ↓
                    Sử dụng delay_time từ DB (fallback: 10s)
```

### 2. Luồng Xử Lý Tin Nhắn
```
Tin nhắn → Buffer → Kiểm tra delay_time → Tạo delayed job → Agent Service
                                                              ↓
                                                    Sử dụng instruction từ DB
                                                              ↓
                                                    Mastra Agent → Response
```

## 🎯 Ví Dụ Sử Dụng

### Cấu Hình Bot Bán Hàng
```bash
curl -X PUT http://localhost:3000/api/system/message-batching/config \
  -H "Content-Type: application/json" \
  -d '{
    "bot_id": "sales-bot-uuid",
    "delay_time": 5,
    "instruction": "Bạn là chuyên gia bán hàng. Hãy tư vấn sản phẩm nhiệt tình và chuyên nghiệp."
  }'
```

### Cấu Hình Bot Hỗ Trợ Kỹ Thuật
```bash
curl -X PUT http://localhost:3000/api/system/message-batching/config \
  -H "Content-Type: application/json" \
  -d '{
    "bot_id": "support-bot-uuid", 
    "delay_time": 20,
    "instruction": "Bạn là chuyên gia kỹ thuật. Hãy giải quyết vấn đề một cách chi tiết và chính xác."
  }'
```

## 🔍 Monitoring & Debug

### Kiểm tra cấu hình hiện tại
```bash
curl http://localhost:3000/api/system/chatbot/config/INBOX_ID
```

### Xem logs delay time
```bash
# Logs sẽ hiển thị:
# 🕐 Bot timeout configuration: 15s (15000ms) for bot uuid
```

### Thống kê Message Batching
```bash
curl http://localhost:3000/api/system/message-batching/stats
```

## ⚡ Immediate Processing vs Delayed Processing

### Xử lý ngay lập tức (Immediate)
Tin nhắn sẽ được xử lý ngay lập tức (bỏ qua delay_time) khi:
- Chứa từ khóa khẩn cấp: `urgent`, `gấp`, `khẩn cấp`, `emergency`, `hủy đơn`, `cancel order`, `hủy`, `cancel`
- Cấu hình `delay_time = 0` (immediate mode)

### Xử lý có delay (Delayed) - MẶC ĐỊNH CHO HÌNH ẢNH
Tin nhắn hình ảnh sẽ được delay **10 giây mặc định** để:
- Chờ user nhập thêm câu hỏi tư vấn chi tiết
- Tạo cuộc trò chuyện tự nhiên và chính xác hơn
- Cho phép AI hiểu rõ ý định của khách hàng

**Lưu ý**: Hình ảnh không còn được xử lý ngay lập tức như trước, mà sẽ delay theo cấu hình để tối ưu hóa trải nghiệm tư vấn.

## 🛠️ Troubleshooting

### Lỗi thường gặp:

1. **delay_time không hoạt động**
   - Kiểm tra bot_id có đúng không
   - Xem logs: `🕐 Bot timeout configuration`

2. **instruction không được áp dụng**
   - Kiểm tra chatbot_configurations có instruction không
   - Xem logs agent service

3. **Cấu hình không được lưu**
   - Kiểm tra bot_id có tồn tại trong database
   - Validate delay_time (1-60 giây)

### Debug commands:
```bash
# Kiểm tra cấu hình
curl http://localhost:3000/api/system/chatbot/config/INBOX_ID

# Xem thống kê
curl http://localhost:3000/api/system/message-batching/stats

# Kiểm tra kết nối
curl http://localhost:3000/api/system/status
```

## 🚀 Tính Năng Tương Lai

- [ ] Available Functions Configuration
- [ ] Multi-language Instructions
- [ ] Dynamic Delay Time based on conversation context
- [ ] A/B Testing for different configurations
- [ ] Analytics dashboard for configuration performance
