# 🔧 Tối Ưu Message Aggregation cho delay_time = 0

## 🎯 Vấn Đề Đã Giải Quyết

**Vấn đề ban đầu**: Khi user cấu hình `delay_time = 0`, tin nhắn text được xử lý ngay lập tức mà không kiểm tra xem có job hình ảnh đang chờ hay không. Điều này khiến việc gom tin nhắn không hoạt động.

**Kịch bản lỗi**:
1. User g<PERSON><PERSON> hình ảnh → <PERSON><PERSON> thống tạo job delay 10s
2. User gửi text ngay sau đó → <PERSON><PERSON> thống xử lý ngay lập tức (do `delay_time = 0`)
3. Kết quả: 2 phản hồi riêng biệt thay vì 1 phản hồi tổng hợp

## ✅ Giải Pháp Đã Triển Khai

### 1. <PERSON><PERSON><PERSON> nhật Logic `shouldProcessImmediately`

**Tr<PERSON>ớc**:
```typescript
private shouldProcessImmediately(message: BufferedMessage, chatbotInfo: any): boolean {
  const delayTimeSeconds = chatbotInfo?.instruction?.delay_time ?? 0;
  const isImmediateMode = delayTimeSeconds === 0 && !hasImages;
  return hasUrgentKeyword || isImmediateMode;
}
```

**Sau**:
```typescript
private async shouldProcessImmediately(
  message: BufferedMessage, 
  chatbotInfo: any, 
  existingBatch: MessageBatch | null
): Promise<boolean> {
  // Kiểm tra từ khóa khẩn cấp - luôn xử lý ngay
  if (hasUrgentKeyword) return true;
  
  // Kiểm tra có hình ảnh - luôn delay
  if (hasImages) return false;
  
  // Kiểm tra có batch hiện tại - ưu tiên gom tin nhắn
  if (existingBatch && existingBatch.messages.length > 0) return false;
  
  // Chỉ xử lý ngay khi delay_time=0 và không có batch
  const delayTimeSeconds = chatbotInfo?.instruction?.delay_time ?? 0;
  return delayTimeSeconds === 0;
}
```

### 2. Thêm Logic Delay Ngắn cho Message Aggregation

**Vấn đề**: Khi có existing batch và `delay_time = 0`, cần delay ngắn để gom tin nhắn.

**Giải pháp**:
```typescript
// Nếu có existing batch và delay_time = 0, sử dụng delay ngắn để gom tin nhắn
let actualDelay = timeout;
if (existingBatch && timeout === 0) {
  actualDelay = 2000; // 2 giây để gom tin nhắn
  console.log(`🔄 Existing batch detected with delay_time=0 - using short delay (2s) for message aggregation`);
}
```

### 3. Cải Thiện Logging và Debugging

**Thêm logs chi tiết**:
- `📦 Existing batch found with X messages - will aggregate`
- `📦 Added message to existing batch (X messages total)`
- `🔄 Existing batch detected with delay_time=0 - using short delay (2s) for message aggregation`
- `⚡ Immediate mode (delay_time=0) and no existing batch - processing immediately`

## 🔄 Luồng Xử Lý Mới

### Scenario 1: Tin nhắn text đơn lẻ (delay_time = 0)
```
User gửi text → Không có batch hiện tại → Xử lý ngay lập tức
```

### Scenario 2: Hình ảnh + Text (delay_time = 0) - QUAN TRỌNG
```
User gửi hình ảnh → Tạo batch với delay 10s
User gửi text → Phát hiện existing batch → Gom vào batch → Delay 2s → Xử lý tổng hợp
```

### Scenario 3: Từ khóa khẩn cấp
```
User gửi tin nhắn có từ khóa khẩn cấp → Xử lý ngay lập tức (bỏ qua batch)
```

## 📊 Bảng Quyết Định Logic

| Loại tin nhắn | delay_time | Có batch hiện tại | Từ khóa khẩn cấp | Kết quả |
|---------------|------------|-------------------|------------------|---------|
| Hình ảnh | Bất kỳ | Không | Không | Delay 10s |
| Hình ảnh | Bất kỳ | Có | Không | Delay 10s |
| Hình ảnh | Bất kỳ | Bất kỳ | **Có** | **Ngay lập tức** |
| Text | 0 | Không | Không | **Ngay lập tức** |
| Text | 0 | **Có** | Không | **Delay 2s (gom)** |
| Text | 0 | Bất kỳ | **Có** | **Ngay lập tức** |
| Text | >0 | Bất kỳ | Không | Delay theo config |
| Text | >0 | Bất kỳ | **Có** | **Ngay lập tức** |

## 🎯 Lợi Ích

### 1. Message Aggregation Hoạt Động Đúng
- ✅ Tin nhắn hình ảnh + text được gom lại ngay cả với `delay_time = 0`
- ✅ Phản hồi tổng hợp thay vì nhiều phản hồi riêng lẻ
- ✅ Trải nghiệm người dùng tự nhiên hơn

### 2. Tối Ưu Performance
- ✅ Giảm số lần gọi API agent
- ✅ Giảm tải cho hệ thống
- ✅ Tiết kiệm credit

### 3. Flexibility
- ✅ Vẫn hỗ trợ xử lý ngay lập tức khi cần
- ✅ Từ khóa khẩn cấp luôn được ưu tiên
- ✅ Cấu hình linh hoạt theo nhu cầu

## 🔍 Monitoring và Debug

### Logs Quan Trọng
```bash
# Kiểm tra batch detection
📦 Existing batch found with 1 messages - will aggregate

# Kiểm tra logic immediate
⚡ Immediate mode (delay_time=0) and no existing batch - processing immediately

# Kiểm tra message aggregation
🔄 Existing batch detected with delay_time=0 - using short delay (2s) for message aggregation

# Kiểm tra job creation
⏰ Tạo delayed job (2000ms): job_123 cho batch batch_456
```

### Redis Keys để Monitor
```bash
# Batch hiện tại
redis-cli keys "batch:*"

# Job IDs
redis-cli keys "job:*"

# Xem nội dung batch
redis-cli get "batch:thread_xxx:resource_yyy"
```

## 🧪 Test Cases

Xem chi tiết trong file `MESSAGE_AGGREGATION_TEST_SCENARIOS.md`

### Quick Test
```bash
# 1. Gửi hình ảnh
curl -X POST /api/agent/message -d '{"message":"", "messageAttachments":[...]}'

# 2. Sau 2-3 giây, gửi text
curl -X POST /api/agent/message -d '{"message":"Sản phẩm này giá bao nhiêu?"}'

# 3. Kiểm tra logs
tail -f logs/app.log | grep -E "(Existing batch|delayed job)"
```

## 🚨 Breaking Changes

**Không có breaking changes** - tất cả cấu hình hiện tại vẫn hoạt động như cũ.

**Chỉ cải thiện**: Logic gom tin nhắn khi `delay_time = 0`.

## 📝 Files Đã Thay Đổi

1. **`src/services/queue/message-buffer.service.ts`**:
   - Cập nhật `shouldProcessImmediately()` method
   - Thêm logic delay ngắn cho message aggregation
   - Cải thiện logging

2. **`docs/IMAGE_MESSAGE_DELAY_CONFIG.md`**:
   - Cập nhật documentation
   - Thêm bảng quyết định logic
   - Cải thiện troubleshooting guide

3. **`docs/MESSAGE_AGGREGATION_TEST_SCENARIOS.md`** (mới):
   - Test cases chi tiết
   - Hướng dẫn test và debug

4. **`docs/MESSAGE_AGGREGATION_OPTIMIZATION.md`** (mới):
   - Summary về optimization
   - Technical details

## 🎉 Kết Luận

Optimization này giải quyết hoàn toàn vấn đề gom tin nhắn khi `delay_time = 0`, đảm bảo:

- ✅ Message aggregation hoạt động đúng trong mọi trường hợp
- ✅ Performance được tối ưu
- ✅ User experience được cải thiện
- ✅ Backward compatibility được đảm bảo
- ✅ Debugging và monitoring được cải thiện
