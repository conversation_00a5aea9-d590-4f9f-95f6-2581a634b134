# Product Tools - Production Ready

Thư mục này chứa các tools đã được tối ưu hóa cho production, chỉ trả về các field cần thiết để tiết kiệm token cho AI.

## Cấu trúc Tools

### 1. `searchProductsTool` (search.ts)
**<PERSON><PERSON><PERSON> đích**: Tìm kiếm sản phẩm thông qua Weaviate và Supabase
**Tối ưu hóa**:
- Chỉ trả về các field cần thiết: id, name, price, sale_price, stock_quantity, short_description, avatar, images (tối đa 3), type, sku, is_active
- Tối ưu hóa variants: chỉ bao gồm các field quan trọng
- Sử dụng hàm `optimizeProductData()` để chuẩn hóa dữ liệu

**Input**:
- `query`: Từ khóa tìm kiếm hoặc mã SKU

### 2. `getProductDetailsProductionTool` (get-product-details-production.ts)
**<PERSON><PERSON><PERSON> đích**: Tool duy nhất để lấy thông tin chi tiết sản phẩm - thay thế getProductPricingTool và getProductBySkuTool
**Tính năng**:
- Nhận vào product_id hoặc sku (hoặc cả hai)
- Tự động xử lý logic tìm kiếm trong products và product_variants
- Hiển thị thông tin giá chi tiết (giá gốc, giá sale, % giảm giá)
- Kiểm tra tồn kho và trạng thái sản phẩm
- Xử lý cả sản phẩm đơn giản và sản phẩm có biến thể
- Tối ưu hóa để tiết kiệm token và cung cấp thông tin chính xác

**Input**:
- `product_id`: Mã sản phẩm (tùy chọn)
- `sku`: Mã SKU của sản phẩm hoặc biến thể (tùy chọn)
- `variant_id`: Mã biến thể cụ thể (tùy chọn)

**Output**:
- Thông tin sản phẩm/biến thể chi tiết với giá và tồn kho
- Nếu là sản phẩm có biến thể: hiển thị tất cả biến thể
- Nếu tìm thấy biến thể cụ thể: hiển thị thông tin biến thể đó

**Output**:
```json
{
  "products": [
    {
      "id": "uuid",
      "name": "Tên sản phẩm",
      "price": 100000,
      "sale_price": 80000,
      "stock_quantity": 10,
      "short_description": "Mô tả ngắn...",
      "avatar": "url_hình_ảnh",
      "images": ["url1", "url2", "url3"],
      "type": "simple|variable",
      "sku": "SKU123",
      "is_active": true,
      "variants": [...]
    }
  ],
  "total_results": 5,
  "page": 1
}
```

### 3. `getProductCategoriesTool` (categories.ts)
**Mục đích**: Lấy danh sách danh mục sản phẩm
**Input**: Không có (lấy từ runtime context)

### 4. `syncProductsTool` (sync.ts)
**Mục đích**: Đồng bộ sản phẩm từ Supabase sang Weaviate
**Input**: Không có (lấy từ runtime context)

## Tối ưu hóa đã thực hiện

### 1. Loại bỏ các tools deprecated
- ❌ `details.ts` - Thay thế bằng `getProductPricingProductionTool`
- ❌ `availability.ts` - Tích hợp vào `getProductPricingProductionTool`
- ❌ `accurate-pricing.ts` - Tích hợp vào `getProductPricingProductionTool`
- ❌ `warranty.ts` - Không cần thiết cho production
- ❌ `mock-data.ts` - Chỉ dùng cho development

### 2. Tối ưu hóa dữ liệu trả về
- Chỉ lấy các field cần thiết từ database
- Giới hạn số lượng hình ảnh (tối đa 3)
- Rút gọn mô tả sản phẩm (tối đa 200 ký tự)
- Tối ưu hóa thông tin variants

### 3. Cải thiện performance
- Sử dụng query SQL tối ưu
- Giảm thiểu số lần truy vấn database
- Sử dụng hàm utility chung `optimizeProductData()`

## Cách sử dụng

### Import tools
```typescript
import {
  searchProductsTool,
  getProductDetailsProductionTool,
  getProductCategoriesTool,
  syncProductsTool
} from '../tools/product';
```

### Sử dụng trong Agent
```typescript
const agent = new Agent({
  tools: {
    searchProductsTool,
    getProductDetailsProductionTool,
    getProductCategoriesTool,
    syncProductsTool
  }
});
```

## Runtime Context Requirements

Tất cả tools đều yêu cầu:
- `tenant_id`: ID của tenant
- `bot_id`: ID của bot (cho một số tools)

## Lưu ý quan trọng

1. **Chỉ sử dụng `getProductPricingProductionTool`** cho việc lấy thông tin chi tiết sản phẩm và giá
2. **Không sử dụng các tools deprecated** đã bị xóa
3. **Tools đã được tối ưu hóa** để tiết kiệm token cho AI
4. **Luôn kiểm tra `success`** trong response trước khi sử dụng dữ liệu
