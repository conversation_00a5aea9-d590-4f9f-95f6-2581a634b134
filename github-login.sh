#!/bin/bash

echo "===== GitHub Login Helper ====="
echo "Script này sẽ giúp bạn đăng nhập lại vào GitHub"
echo ""

# Xóa thông tin đăng nhập hiện tại
echo "Đang xóa thông tin đăng nhập GitHub hiện tại..."
git credential reject <<EOF
protocol=https
host=github.com
EOF

# Yêu cầu thông tin đăng nhập mới
echo ""
echo "Vui lòng nhập thông tin đăng nhập GitHub của bạn:"
read -p "Username: " username
read -sp "Password/Token: " password
echo ""

# Lưu thông tin đăng nhập mới
echo "Đang lưu thông tin đăng nhập mới..."
git config --global credential.helper store
git credential approve <<EOF
protocol=https
host=github.com
username=$username
password=$password
EOF

echo ""
echo "Đ<PERSON> lưu thông tin đăng nhập GitHub!"
echo "Bạn có thể thử push code lên GitHub bằng lệnh: git push origin main"
echo "===== Hoàn tất ====="
