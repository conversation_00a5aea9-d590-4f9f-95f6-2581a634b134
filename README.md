# Chatbot AI Backend cho Thọ Trần Shop

Backend cho chatbot AI của Thọ Trần Shop, sử dụng Mastra để tạo agent thông minh với khả năng tư vấn sản phẩm, xử lý đơn hàng và hỗ trợ khách hàng.

## Tính năng

- Agent thông minh với khả năng tư vấn sản phẩm, gi<PERSON>i đáp thắc mắc
- Hỗ trợ tạo đơn hàng và hoàn tất quá trình mua sắm
- Xử lý khiếu nại ban đầu và chuyển tiếp đến nhân viên thực khi cần thiết
- Tr<PERSON> về danh sách tin nhắn để tạo trải nghiệm tự nhiên cho người dùng
- Tích hợp với Weaviate và Supabase để quản lý dữ liệu sản phẩm

## Cài đặt

```bash
# Cài đặt dependencies
npm install
```

## Ph<PERSON><PERSON> triển (Development)

```bash
# Khởi động Mastra dev server
npm run dev:mastra

# Khởi động Express server trong chế độ development
npm run dev:express

# Khởi động cả Mastra và Express cùng lúc
npm run dev

# Chạy ứng dụng test
npm run test:app
```

## Build và Triển khai

```bash
# Xóa thư mục dist cũ
npm run clean

# Build dự án (bao gồm cả Mastra và Express)
npm run build

# Build chỉ phần Mastra
npm run build:mastra

# Build chỉ phần Express
npm run build:express

# Chạy Express server trong chế độ development
npm run start:express

# Chạy Express server trong chế độ production (từ thư mục dist)
npm run start:express:prod

# Chạy Express server (alias cho start:express)
npm run start

# Chạy Express server trong chế độ production (alias cho start:express:prod)
npm run start:prod
```

## Kiểm tra và Linting

```bash
# Chạy kiểm tra TypeScript (không build)
npm run lint

# Chạy tests
npm test

# Chạy tests với chế độ watch
npm run test:watch

# Chạy tests với báo cáo coverage
npm run test:coverage
```

## Cấu trúc dự án

- `src/`: Mã nguồn chính của ứng dụng
  - `config/`: Cấu hình cho Weaviate, Supabase và các dịch vụ khác
  - `mastra/`: Mã nguồn liên quan đến Mastra (agents, tools, workflows)
  - `routes/`: Các API routes của Express
  - `services/`: Các dịch vụ tương tác với Weaviate, Supabase
  - `validators/`: Các schema xác thực dữ liệu
  - `index.ts`: Điểm khởi đầu của ứng dụng Express

## API Endpoints

### Tạo phản hồi từ agent

```
POST /api/agent/generate
```

Body:
```json
{
  "message": "Tôi muốn mua áo thun nam",
  "threadId": "thread-123",
  "resourceId": "user-456"
}
```

Response:
```json
{
  "messages": [
    {
      "content": "Chào anh/chị! Em rất vui được hỗ trợ anh/chị tìm áo thun nam ạ."
    },
    {
      "content": "Thọ Trần Shop có nhiều mẫu áo thun nam đa dạng về kiểu dáng và màu sắc. Anh/chị có yêu cầu cụ thể về màu sắc, kích cỡ hoặc phong cách không ạ?"
    },
    {
      "content": "Em có thể giới thiệu cho anh/chị một số mẫu áo thun nam basic rất được ưa chuộng, hoặc nếu anh/chị thích phong cách nào đặc biệt, em sẽ tìm giúp anh/chị ạ."
    }
  ],
  "thinking": "Khách hàng đang quan tâm đến áo thun nam nhưng chưa cung cấp thông tin cụ thể về màu sắc, kích cỡ hoặc phong cách. Tôi sẽ chào hỏi, xác nhận yêu cầu và hỏi thêm thông tin để có thể tư vấn tốt hơn."
}
```

### Stream phản hồi từ agent

```
POST /api/agent/stream
```

Body:
```json
{
  "message": "Tôi muốn mua áo thun nam",
  "threadId": "thread-123",
  "resourceId": "user-456"
}
```

Response: Server-Sent Events (SSE) stream

## Cấu trúc dự án

- `src/mastra/agents`: Cấu hình agent
- `src/mastra/tools`: Các công cụ cho agent
- `src/mastra/prompts`: Các prompt hệ thống
- `src/mastra/schemas`: Các schema định nghĩa cấu trúc dữ liệu
- `src/api`: API endpoints và Express server

## Telemetry và Langfuse

Dự án này sử dụng Langfuse để theo dõi và phân tích các cuộc hội thoại. Có hai cách để sử dụng Mastra với telemetry:

### Cách 1: Sử dụng Mastra Server (Khuyến nghị)

Khi sử dụng Mastra thông qua server (npm run dev:mastra), telemetry sẽ tự động được gửi đến Langfuse. Đây là cách được khuyến nghị để có đầy đủ thông tin telemetry.

```bash
# Khởi động Mastra server
npm run dev:mastra

# Trong file .env, đặt
USE_MASTRA_CLIENT=true
```

### Cách 2: Sử dụng Mastra trực tiếp

Khi sử dụng Mastra trực tiếp trong Node.js (không thông qua server), bạn cần đặt biến môi trường `USE_MASTRA_CLIENT=false` để sử dụng instance Mastra trực tiếp.

```bash
# Trong file .env, đặt
USE_MASTRA_CLIENT=false
```

### Kiểm tra cả hai chế độ

Bạn có thể chạy script test để kiểm tra cả hai chế độ:

```bash
# Biên dịch dự án trước
npm run build

# Chạy script test
node test-both-modes.js
```

## BullMQ và Đồng bộ hóa sản phẩm

Dự án sử dụng BullMQ với Dragonfly để quản lý các tác vụ đồng bộ hóa sản phẩm. Để khởi động worker xử lý đồng bộ sản phẩm:

```bash
# Khởi động worker đồng bộ sản phẩm
node start-product-sync-worker.js
```

## Môi trường

Dự án sử dụng các biến môi trường được định nghĩa trong file `.env`. Bạn có thể sao chép từ file `.env.example` để bắt đầu:

```bash
# Sao chép file .env.example thành .env
cp .env.example .env

# Chỉnh sửa file .env với thông tin cấu hình của bạn
```
