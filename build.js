import { exec } from 'child_process';
import fs from 'fs';
import path from 'path';

// T<PERSON><PERSON> thư mục dist nếu chưa tồn tại
if (!fs.existsSync('dist')) {
  fs.mkdirSync('dist');
}

// Chạy TypeScript compiler
console.log('🔨 Đang biên dịch TypeScript...');
exec('tsc', (error, stdout, stderr) => {
  if (error) {
    console.error(`❌ Lỗi khi biên dịch TypeScript: ${error.message}`);
    return;
  }
  if (stderr) {
    console.error(`⚠️ TypeScript stderr: ${stderr}`);
    return;
  }
  console.log('✅ Biên dịch TypeScript thành công!');
  
  // Sao chép file .env vào thư mục dist
  console.log('📄 Đang sao chép file .env...');
  try {
    if (fs.existsSync('.env')) {
      fs.copyFileSync('.env', path.join('dist', '.env'));
      console.log('✅ Sao chép file .env thành công!');
    } else {
      console.warn('⚠️ Không tìm thấy file .env để sao chép');
    }
  } catch (err) {
    console.error(`❌ Lỗi khi sao chép file .env: ${err.message}`);
  }
  
  console.log('🎉 Quá trình build hoàn tất!');
});
