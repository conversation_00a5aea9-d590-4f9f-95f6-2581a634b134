# ===== STAGE 1: Dependencies =====
FROM node:20-alpine AS deps
WORKDIR /app

# Cài đặt yarn
RUN corepack enable

# Copy package files
COPY package.json yarn.lock* ./
COPY .yarnrc.yml ./ 

# Install dependencies
RUN yarn install

# ===== STAGE 2: Builder =====
FROM node:20-alpine AS builder
WORKDIR /app

# Enable yarn
RUN corepack enable

# Copy dependencies từ stage trước
COPY --from=deps /app/node_modules ./node_modules
COPY package.json yarn.lock* .yarnrc.yml ./

# Copy source code
COPY . .

# Build application
RUN yarn build

# ===== STAGE 3: Production =====
FROM node:20-alpine AS production
WORKDIR /app

# Enable yarn
RUN corepack enable

# Set production environment
ENV NODE_ENV=production
ENV PORT=3000

# Copy package files
COPY package.json yarn.lock* ./

# Copy built application
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/node_modules ./node_modules

# Expose port
EXPOSE 3000

# Start application
CMD ["yarn", "start"]