# SKU System Optimization Summary

## Vấn đề ban đầu
- Lỗi `relation "public.sku_counters" does not exist` khi đồng bộ sản phẩm
- <PERSON><PERSON> thống cố gắng tạo SKU tự động thông qua database counter phức tạp
- Logic tạo SKU phức tạp và dễ gây lỗi

## Giải pháp đã triển khai

### 1. Đơn giản hóa SKU Manager (`src/services/sync/sku-manager.ts`)
- **Loại bỏ**: `getCurrentSkuCounter()`, `updateSkuCounter()` - không cần database counter
- **Cập nhật**: `generateNextSku()` - chỉ tạo SKU từ platform + source_id
- **Cập nhật**: `validateAndNormalizeSku()` - ưu tiên SKU có sẵn từ source
- **Deprecated**: `initializeSkuCounters()` - không cần khởi tạo counter nữa

### 2. Cập nhật Product Processor (`src/services/sync/product-processor.ts`)
- **Thay đổi logic**: Chỉ sử dụng SKU có sẵn từ sản phẩm hoặc để trống
- **Loại bỏ**: Tạo SKU tự động phức tạp
- **Cập nhật**: `sanitizeVariantSku()` - chỉ sử dụng SKU có sẵn từ variant

### 3. Cập nhật Adapters
- **Haravan Adapter** (`src/services/sync/adapters/haravan.adapter.ts`):
  - Sử dụng SKU có sẵn từ variant hoặc để trống
  - Loại bỏ logic tạo SKU từ product.id
  
- **Sapo Adapter** (`src/services/sync/adapters/sapo.adapter.ts`):
  - Sử dụng SKU có sẵn từ variant hoặc để trống
  - Loại bỏ logic tạo SKU từ product.id

### 4. Database Schema Update
- **Migration**: `make_sku_nullable`
- **Thay đổi**: `products.sku` từ NOT NULL thành NULLABLE
- **Lý do**: Cho phép sản phẩm không có SKU nếu source không cung cấp

## Logic mới

### SKU cho sản phẩm chính:
1. Nếu có SKU từ source → sử dụng SKU đó
2. Nếu không có SKU từ source → để trống (`''`)
3. Nếu cần tạo SKU (có source_id) → `PLATFORM + source_id` (VD: `HARA12345`, `SAPO67890`)

### SKU cho variants:
1. Nếu variant có SKU từ source → sử dụng SKU đó
2. Nếu variant không có SKU → để trống (`''`)
3. Không tự động tạo SKU cho variant nữa

## Lợi ích

### 1. Đơn giản hóa hệ thống
- Loại bỏ dependency vào `sku_counters` table
- Giảm complexity trong code
- Dễ maintain và debug

### 2. Tăng độ tin cậy
- Không còn lỗi database table không tồn tại
- Logic rõ ràng và dễ hiểu
- Ít điểm lỗi tiềm ẩn

### 3. Linh hoạt hơn
- Sản phẩm có thể không có SKU (phù hợp với thực tế)
- Sử dụng SKU gốc từ platform (chính xác hơn)
- Dễ mở rộng cho các platform khác

## Test
- Tạo file `test-sku-logic.js` để test logic mới
- Chạy: `node test-sku-logic.js`

## Kết quả
- ✅ Loại bỏ lỗi `sku_counters does not exist`
- ✅ Hệ thống đồng bộ hoạt động ổn định
- ✅ Code đơn giản và dễ maintain
- ✅ Phù hợp với yêu cầu thực tế của business
