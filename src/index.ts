import express, { Request, Response } from "express";
import cors from "cors";
import bodyParser from "body-parser";
import dotenv from "dotenv";
import routes from "./routes/index";
import { errorMiddleware } from "./middlewares/index";
import { getWeaviateClientInstance } from "./config/weaviate";
import { initializeBullMQSystem, shutdownBullMQSystem, createHealthCheckEndpoint, createMetricsEndpoint, productSyncQueue } from "./services/queue";
import { getBullBoardRouter } from "./services/queue/bull-board.service";
import { bullBoardDevMiddleware } from "./middlewares/bull-board-auth.middleware";
import { exec } from "child_process";
import { promisify } from "util";

// Khởi tạo biến môi trường
dotenv.config();

// Promisify exec để sử dụng async/await
const execAsync = promisify(exec);

/**
 * Kiểm tra và kill process đang sử dụng port
 */
const killPortIfInUse = async (port: number): Promise<void> => {
  try {
    // Tìm process đang sử dụng port
    const { stdout } = await execAsync(`lsof -ti:${port}`);

    if (stdout.trim()) {
      const pids = stdout.trim().split('\n');

      // Kill tất cả process đang sử dụng port
      for (const pid of pids) {
        if (pid.trim()) {
          try {
            await execAsync(`kill -9 ${pid.trim()}`);
          } catch (killError) {
            console.error(`⚠️ Không thể kill process ${pid.trim()}: ${killError}`);
          }
        }
      }

      // Đợi một chút để đảm bảo port được giải phóng
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  } catch (error: any) {
    // Nếu lsof không tìm thấy process nào (exit code 1), có nghĩa là port đang trống
    if (error.code !== 1) {
      console.error(`❌ Lỗi khi kiểm tra port ${port}:`, error.message);
    }
  }
};

/**
 * Khởi tạo và cấu hình Express app
 */
const createApp = () => {
  const app = express();

  // Middleware
  app.use(cors());
  app.use(bodyParser.json());

  // Route mặc định
  app.get("/", (_req: Request, res: Response) => {
    res.send("Hello, TypeScript Express!");
  });

  // BullMQ Health check endpoints
  app.get("/health", createHealthCheckEndpoint(productSyncQueue));
  app.get("/metrics", createMetricsEndpoint());

  // Bull Board Dashboard - Đặt trước routes chính để tránh conflict
  try {
    // Sử dụng authentication middleware cho Bull Board
    app.use('/admin/queues', bullBoardDevMiddleware, getBullBoardRouter());
  } catch (error) {
    console.error('❌ Lỗi khi khởi tạo Bull Board:', error);
  }

  // Routes
  app.use("/api", routes);

  // Error handling middleware (luôn đặt ở cuối)
  app.use(errorMiddleware);

  return app;
};

/**
 * Kiểm tra kết nối với Weaviate server
 */
const checkWeaviateConnection = async () => {
  try {
    await getWeaviateClientInstance();
    return true;
  } catch (error) {
    console.error("❌ Không thể kết nối đến Weaviate server:", error);
    return false;
  }
};

/**
 * Khởi động server
 */
const startServer = async (app: express.Application) => {
  const PORT = process.env.PORT || 3003;

  // Kiểm tra và kill port nếu đang được sử dụng
  await killPortIfInUse(Number(PORT));

  // Kiểm tra kết nối Weaviate trước khi khởi động server
  await checkWeaviateConnection();

  // Kiểm tra xem có tắt BullMQ không
  const isBullMQDisabled = process.env.DISABLE_BULLMQ === 'true';
  const isIntegratedMode = process.env.INTEGRATED_MODE === 'true';

  if (!isBullMQDisabled && !isIntegratedMode) {
    // Khởi động BullMQ system
    try {
      await initializeBullMQSystem();
    } catch (error: any) {
      console.error('❌ Lỗi khi khởi động BullMQ workers:', error);

      // Kiểm tra xem có phải lỗi kết nối Redis/Dragonfly không
      const isRedisConnectionError =
        error.message && (
          error.message.includes('ECONNREFUSED') ||
          error.message.includes('connection is closed') ||
          error.message.includes('Redis connection lost') ||
          error.message.includes('Dragonfly connection lost') ||
          error.message.includes('Timeout') ||
          error.message.includes('timed out')
        );

      if (isRedisConnectionError) {
        console.error('⚠️ Không thể kết nối đến Redis/Dragonfly, hệ thống sẽ xử lý đồng bộ trực tiếp');
      }
    }
  }

  // Xử lý tắt server một cách an toàn
  process.on("SIGTERM", async () => {
    console.log("Nhận tín hiệu SIGTERM, đang dừng server...");
    await shutdownBullMQSystem();
    process.exit(0);
  });

  process.on("SIGINT", async () => {
    console.log("Nhận tín hiệu SIGINT, đang dừng server...");
    await shutdownBullMQSystem();
    process.exit(0);
  });

  return app.listen(PORT, () => {
    console.log(`Server đang chạy trên cổng ${PORT}`);
  });
};

// Khởi tạo Express app
const app = createApp();

// Khởi động server và kiểm tra kết nối Weaviate
startServer(app).catch((error) => {
  console.error("Lỗi khi khởi động server:", error);
  process.exit(1);
});
