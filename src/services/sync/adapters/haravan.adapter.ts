import { v4 as uuidv4 } from 'uuid';
import { haravanProductService } from '../../platforms';
import { PlatformAdapter, SystemProduct, SystemProductVariant } from '../types';
import { generateVariantSku } from '../sku-manager';

/**
 * Adapter cho nền tảng <PERSON>
 */
export const haravanAdapter: PlatformAdapter = {
  platformName: 'haravan',

  mapToSystem: (product: any, tenant_id: string, bot_id: string): SystemProduct => {
    // Xác định loại sản phẩm (đơn giản hay biến thể)
    const productType = product.variants && product.variants.length > 1 ? 'variable' : 'simple';

    // Xử lý hình ảnh
    const images = product.images || [];
    // Lưu URL gốc từ Haravan để xử lý sau
    const originalImageUrls = images.map((img: any) => img.src);
    // Avatar sẽ được cập nhật sau khi upload hình ảnh
    const avatar = originalImageUrls.length > 0 ? originalImageUrls[0] : null;

    // Xử lý thuộc tính từ options của Haravan
    const options = product.options || [];

    const attributes: Record<string, any> = {};

    // Chuyển đổi options từ Haravan thành attributes trong hệ thống
    options.forEach((option: any) => {
      if (option.name) {
        // Nếu có values thì sử dụng, nếu không thì tạo mảng rỗng
        attributes[option.name] = option.values || [];
      }
    });

    // Tạo mapping giữa image_id và URL hình ảnh để gán cho biến thể
    const imageMapping: {[key: string]: string} = {};
    images.forEach((img: any) => {
      if (img.id) {
        imageMapping[img.id.toString()] = img.src;
      }
    });

    // Xử lý biến thể
    const processedVariants: SystemProductVariant[] = product.variants ? product.variants.map((variant: any, variantIndex: number) => {
      // Tạo đối tượng attributes cho biến thể
      const variantAttributes: {[key: string]: string} = {};

      // Mapping option1, option2, option3 vào attributes tương ứng
      if (variant.option1 !== undefined && options.length >= 1) {
        variantAttributes[options[0].name] = variant.option1;
      }

      if (variant.option2 !== undefined && options.length >= 2) {
        variantAttributes[options[1].name] = variant.option2;
      }

      if (variant.option3 !== undefined && options.length >= 3) {
        variantAttributes[options[2].name] = variant.option3;
      }

      // Tạo tên cho biến thể từ các giá trị thuộc tính
      const variantName = Object.values(variantAttributes).join(' / ');

      // Lấy hình ảnh cho biến thể nếu có image_id
      const variantImageUrl = variant.image_id ? imageMapping[variant.image_id.toString()] : null;

      // Sử dụng SKU có sẵn từ variant hoặc để trống
      const variantSku = variant.sku && variant.sku.trim() !== ''
        ? variant.sku.trim()
        : '';

      return {
        id: uuidv4(),
        product_id: null, // Sẽ được cập nhật sau khi tạo sản phẩm
        sku: variantSku,
        barcode: variant.barcode || '',
        price: variant.price || 0,
        compare_at_price: variant.compare_at_price || null,
        cost_price: variant.cost || null,
        stock_quantity: variant.inventory_quantity || 0,
        weight: variant.weight || null,
        is_active: true,
        attributes: variantAttributes,
        avatar: variantImageUrl || avatar, // Sử dụng hình ảnh của biến thể hoặc hình ảnh mặc định
        name: variantName,
        tenant_id: tenant_id
      };
    }) : [];

    // Đảm bảo các trường bắt buộc luôn có giá trị
    const name = product.title || 'Sản phẩm không tên';
    const slug = product.handle || name.toLowerCase().replace(/[^a-z0-9]+/g, '-');

    // Đảm bảo stock_quantity luôn có giá trị số (0 nếu không có)
    let stockQuantity = 0;
    if (productType === 'simple') {
      // Nếu là sản phẩm đơn giản, lấy số lượng từ biến thể đầu tiên hoặc mặc định là 0
      stockQuantity = product.variants?.[0]?.inventory_quantity || 0;
    } else {
      // Nếu là sản phẩm có biến thể, tính tổng số lượng từ tất cả biến thể
      stockQuantity = product.variants?.reduce((total: number, variant: any) => {
        return total + (variant.inventory_quantity || 0);
      }, 0) || 0;
    }


    // Sử dụng SKU có sẵn từ variant đầu tiên hoặc để trống
    const productSku = product.variants?.[0]?.sku && product.variants[0].sku.trim() !== ''
      ? product.variants[0].sku.trim()
      : '';

    return {
      id: uuidv4(), // Tạo UUID mới cho sản phẩm
      name: name,
      slug: slug,
      description: product.body_html || '',
      short_description: product.summary || '',
      type: productType, // 'simple' hoặc 'variable'
      sku: productSku,
      barcode: product.variants?.[0]?.barcode || product.barcode || '',
      price: product.variants?.[0]?.price || product.price || 0,
      compare_at_price: product.variants?.[0]?.compare_at_price || product.compare_at_price || null,
      cost_price: product.variants?.[0]?.cost || product.cost || null,
      track_inventory: true,
      stock_quantity: stockQuantity, // Đảm bảo luôn có giá trị số
      weight: product.variants?.[0]?.weight || product.weight || null,
      is_active: product.published_at ? true : false,
      is_featured: product.featured || false,
      avatar: avatar,
      images: originalImageUrls,
      originalImageUrls: originalImageUrls,
      attributes: attributes,
      tenant_id: tenant_id,
      bot_id: bot_id,
      source: 'haravan',
      source_id: product.id.toString(),
      processedVariants: processedVariants // Thêm biến thể đã xử lý
    };
  },

  getProducts: async (token: string, _storeUrl: string | undefined, options: any) => {
    return haravanProductService.getProducts(token, options);
  }
};
