import { haravanAdapter } from './haravan.adapter';
import { sapoAdapter } from './sapo.adapter';
import { PlatformAdapter } from '../types';

/**
 * Map các nền tảng với adapter tương ứng
 */
export const platformAdapters: Record<string, PlatformAdapter> = {
  'haravan': haravanAdapter,
  'sapo': sapoAdapter
};

/**
 * Lấy adapter cho nền tảng cụ thể
 * @param platform Tên nền tảng
 * @returns Adapter cho nền tảng hoặc undefined nếu không tìm thấy
 */
export const getAdapter = (platform: string): PlatformAdapter | undefined => {
  return platformAdapters[platform.toLowerCase()];
};

/**
 * Kiểm tra xem nền tảng có được hỗ trợ không
 * @param platform Tên nền tảng
 * @returns true nếu nền tảng được hỗ trợ, false nếu không
 */
export const isPlatformSupported = (platform: string): boolean => {
  return !!platformAdapters[platform.toLowerCase()];
};

export {
  haravanAdapter,
  sapoAdapter
};
