import { getAdapter, isPlatformSupported } from "./adapters";
import {
  checkProductExists,
  checkMultipleProductsExist,
  processProductImages,
  saveProductToDatabase,
  saveVariantToDatabase,
} from "./product-processor";
import { SyncOptions, SyncResult, SystemProduct } from "./types";
import { haravanProductService, sapoProductService } from "../platforms";

/**
 * Đồng bộ sản phẩm từ nền tảng bên ngoài vào hệ thống
 * @param platform Tên nền tảng (haravan, sapo, ...)
 * @param token Token xác thực của nền tảng
 * @param tenant_id ID của tenant
 * @param bot_id ID của bot hoặc danh sách ID của bot
 * @param storeUrl URL của cửa hàng (chỉ cần thiết cho <PERSON>po)
 * @param options Tùy chọn đồng bộ
 * @returns <PERSON>ết quả đồng bộ
 */
export const syncProductsFromPlatform = async (
  platform: string,
  token: string,
  tenant_id: string,
  bot_id: string,
  storeUrl?: string,
  options: SyncOptions = {}
): Promise<SyncResult> => {
  try {
    // Kiểm tra nền tảng có được hỗ trợ không
    if (!isPlatformSupported(platform)) {
      return {
        success: false,
        message: `Nền tảng ${platform} không được hỗ trợ`,
      };
    }

    // Lấy adapter cho nền tảng
    const adapter = getAdapter(platform);
    if (!adapter) {
      return {
        success: false,
        message: `Không tìm thấy adapter cho nền tảng ${platform}`,
      };
    }

    // Lấy danh sách sản phẩm từ nền tảng
    const platformProducts = await adapter.getProducts(token, storeUrl, {
      limit: options.limit || 50,
      page: options.page || 1,
      updated_at_min: options.updated_at_min,
    });

    if (!platformProducts.success) {
      return {
        success: false,
        message: `Lỗi khi lấy danh sách sản phẩm từ ${platform}: ${platformProducts.message}`,
      };
    }

    if (!platformProducts.data || platformProducts.data.length === 0) {
      return {
        success: true,
        message: "Không có sản phẩm nào để đồng bộ",
        data: {
          total: 0,
          products: [],
          variants: [],
        },
      };
    }

    const products = platformProducts.data;

    // Chuyển đổi sản phẩm từ định dạng của nền tảng sang định dạng hệ thống
    const systemProducts: SystemProduct[] = products.map((product: any) =>
      adapter.mapToSystem(product, tenant_id, bot_id)
    );

    // Lấy danh sách source_id để kiểm tra hàng loạt
    const sourceIds = systemProducts.map((p) => p.source_id);

    // Kiểm tra hàng loạt các sản phẩm đã tồn tại
    const existingProducts = await checkMultipleProductsExist(
      platform,
      sourceIds,
      tenant_id
    );

    // Tạo Map để tra cứu nhanh
    const existingProductMap = new Map(
      existingProducts.map((p) => [p.source_id, p])
    );

    // Phân loại sản phẩm
    let newProducts: SystemProduct[] = [];
    let skippedProducts: any[] = [];

    for (const product of systemProducts) {
      const existingProduct = existingProductMap.get(product.source_id);

      if (existingProduct && !options.force_update) {
        // Bỏ qua sản phẩm đã tồn tại
        skippedProducts.push({
          source_id: product.source_id,
          name: product.name,
          reason: "Đã tồn tại",
          existing_id: existingProduct.id,
        });
      } else {
        // Sản phẩm mới hoặc cần cập nhật
        newProducts.push(product);
        if (existingProduct) {
        } else {
        }
      }
    }



    // Kết quả đồng bộ
    const syncResults = {
      products: [] as any[],
      variants: [] as any[],
      errors: [] as any[],
      skipped: skippedProducts,
    };

    // Nếu không có sản phẩm mới nào, trả về kết quả
    if (newProducts.length === 0) {
      return {
        success: true,
        message: `Không có sản phẩm mới từ ${platform}. Đã bỏ qua ${skippedProducts.length} sản phẩm đã tồn tại`,
        data: {
          total: 0,
          products: syncResults.products,
          variants: syncResults.variants,
          errors: syncResults.errors,
          skipped: syncResults.skipped,
        },
      };
    }

    // Xử lý hình ảnh cho tất cả sản phẩm cần đồng bộ
    const productsWithProcessedImages = await processProductImages(
      newProducts,
      tenant_id
    );


    // Đồng bộ từng sản phẩm
    for (const product of productsWithProcessedImages) {
      try {
        // Lưu sản phẩm vào cơ sở dữ liệu
        const savedProduct = await saveProductToDatabase(
          product,
          tenant_id
        );

        syncResults.products.push(savedProduct);

        // Lưu biến thể sản phẩm (nếu có)
        if (
          product.processedVariants &&
          product.processedVariants.length > 0
        ) {
          for (let variantIndex = 0; variantIndex < product.processedVariants.length; variantIndex++) {
            const variant = product.processedVariants[variantIndex];
            try {
              const savedVariant = await saveVariantToDatabase(
                variant,
                savedProduct.id,
                tenant_id,
                product.sku,
                variantIndex
              );

              syncResults.variants.push(savedVariant);
            } catch (variantError: any) {
              console.error(
                `Lỗi khi lưu biến thể "${variant.name}":`,
                variantError
              );
              syncResults.errors.push({
                type: "variant",
                variant_name: variant.name,
                product_name: product.name,
                error: variantError.message,
              });
            }
          }
        }

      } catch (error: any) {
        console.error(`❌ Lỗi không xác định khi lưu sản phẩm "${product.name}":`, error);
        syncResults.errors.push({
          type: "product",
          product_name: product.name,
          error: error.message,
        });
      }
    }

    // Tạo thông báo kết quả chi tiết
    let resultMessage = `Đã đồng bộ ${syncResults.products.length} sản phẩm và ${syncResults.variants.length} biến thể từ ${platform}`;

    if (syncResults.skipped.length > 0) {
      resultMessage += `, bỏ qua ${syncResults.skipped.length} sản phẩm đã tồn tại`;
    }

    if (syncResults.errors.length > 0) {
      resultMessage += `, ${syncResults.errors.length} lỗi`;
    }

    return {
      success: true,
      message: resultMessage,
      data: {
        total: syncResults.products.length,
        products: syncResults.products,
        variants: syncResults.variants,
        errors: syncResults.errors,
        skipped: syncResults.skipped,
      },
    };
  } catch (error: any) {
    console.error(`Lỗi khi đồng bộ sản phẩm từ ${platform}:`, error);
    return {
      success: false,
      message: `Lỗi khi đồng bộ sản phẩm từ ${platform}: ${error.message}`,
    };
  }
};

/**
 * Đồng bộ sản phẩm từ Haravan vào hệ thống
 */
export const syncProductsFromHaravan = async (
  token: string,
  tenant_id: string,
  bot_id: string,
  options: SyncOptions = {}
): Promise<SyncResult> => {
  return syncProductsFromPlatform(
    "haravan",
    token,
    tenant_id,
    bot_id,
    undefined,
    options
  );
};

/**
 * Đồng bộ sản phẩm từ Sapo vào hệ thống
 */
export const syncProductsFromSapo = async (
  sapoUrl: string,
  tenant_id: string,
  bot_id: string,
  options: SyncOptions = {}
): Promise<SyncResult> => {
  return syncProductsFromPlatform(
    "sapo",
    sapoUrl,
    tenant_id,
    bot_id,
    sapoUrl,
    options
  );
};

/**
 * Đồng bộ tất cả sản phẩm từ nền tảng với phân trang
 * @param platform Tên nền tảng (haravan, sapo, ...)
 * @param authToken Token xác thực hoặc URL đầy đủ (đối với Sapo)
 * @param tenant_id ID của tenant
 * @param bot_id ID của bot hoặc danh sách ID của bot
 * @param options Tùy chọn đồng bộ
 * @returns Kết quả đồng bộ
 */
export const syncAllProductsFromPlatform = async (
  platform: string,
  authToken: string,
  tenant_id: string,
  bot_id: string,
  options: SyncOptions = {}
): Promise<SyncResult> => {
  try {

    // Kiểm tra nền tảng có được hỗ trợ không
    if (!isPlatformSupported(platform)) {
      return {
        success: false,
        message: `Nền tảng ${platform} không được hỗ trợ`,
      };
    }

    // Lấy tổng số sản phẩm cần đồng bộ
    let totalProducts = 0;
    let countResult;

    if (platform === "haravan") {
      countResult = await haravanProductService.getProductCount(authToken, {
        updated_at_min: options.updated_at_min,
      });
      if (countResult.success) {
        totalProducts = countResult.count;
      }
    } else if (platform === "sapo") {
      countResult = await sapoProductService.getProductCount(authToken, {
        updated_at_min: options.updated_at_min,
      });
      if (countResult.success) {
        totalProducts = countResult.count;
      }
    }

    if (!countResult?.success) {
      return {
        success: false,
        message: `Lỗi khi lấy số lượng sản phẩm từ ${platform}: ${countResult?.message || "Không xác định"}`,
      };
    }
    // Nếu không có sản phẩm nào, trả về kết quả trống
    if (totalProducts === 0) {
      return {
        success: true,
        message: "Không có sản phẩm nào để đồng bộ",
        data: {
          total: 0,
          products: [],
          variants: [],
        },
      };
    }

    // Thiết lập kích thước trang và số trang
    const pageSize = options.limit || 50;
    const totalPages = Math.ceil(totalProducts / pageSize);


    // Mảng lưu trữ kết quả đồng bộ
    const syncResults = {
      products: [] as any[],
      variants: [] as any[],
      errors: [] as any[],
      skipped: [] as any[],
    };

    // Đồng bộ từng trang
    for (let page = 1; page <= totalPages; page++) {

      // Đồng bộ sản phẩm từ trang hiện tại
      const pageOptions = {
        ...options,
        page,
        limit: pageSize,
        // Giữ nguyên giá trị force_update từ options để tôn trọng lựa chọn của người dùng
        // Nếu force_update=false, sẽ bỏ qua các sản phẩm đã tồn tại
        // Nếu force_update=true, sẽ cập nhật lại tất cả sản phẩm
      };

      try {
        let result;
        if (platform === "haravan") {
          result = await syncProductsFromHaravan(
            authToken,
            tenant_id,
            bot_id,
            pageOptions
          );
        } else if (platform === "sapo") {
          result = await syncProductsFromSapo(
            authToken,
            tenant_id,
            bot_id,
            pageOptions
          );
        } else {
          // Nếu có nền tảng khác trong tương lai, xử lý ở đây
          result = await syncProductsFromPlatform(
            platform,
            authToken,
            tenant_id,
            bot_id,
            undefined,
            pageOptions
          );
        }

        // Cập nhật kết quả đồng bộ
        if (result.success && result.data) {
          const pageProducts = result.data.products || [];
          const pageVariants = result.data.variants || [];
          const pageErrors = result.data.errors || [];
          const pageSkipped = result.data.skipped || [];

          syncResults.products.push(...pageProducts);
          syncResults.variants.push(...pageVariants);
          syncResults.errors.push(...pageErrors);
          syncResults.skipped.push(...pageSkipped);

        } else {
          console.error(
            `❌ Lỗi khi đồng bộ trang ${page}/${totalPages} từ ${platform}:`,
            result.message
          );
          syncResults.errors.push({
            page,
            error: result.message,
          });
        }
      } catch (pageError: any) {
        console.error(
          `❌ Lỗi không xác định khi đồng bộ trang ${page}/${totalPages} từ ${platform}:`,
          pageError
        );
        syncResults.errors.push({
          page,
          error: pageError.message,
        });
      }

      // Tạm dừng giữa các trang để tránh quá tải API
      if (page < totalPages) {
        await new Promise((resolve) => setTimeout(resolve, 1000));
      }
    }

    // Kiểm tra tính toàn vẹn dữ liệu
    if (syncResults.products.length === 0 && syncResults.errors.length > 0) {
      console.error(`Cảnh báo: Không có sản phẩm nào được đồng bộ thành công, nhưng có ${syncResults.errors.length} lỗi xảy ra!`);
    }

    // Tạo thông báo kết quả chi tiết
    const syncMode = options.force_update === true || options.full_sync === true ? 'FULL' : 'INCREMENTAL';
    const resultMessage = `✅ [${platform.toUpperCase()}] Products: ${syncResults.products.length} | Variants: ${syncResults.variants.length} | Skipped: ${syncResults.skipped.length} | Errors: ${syncResults.errors.length} | Mode: ${syncMode}`;


    return {
      success: true,
      message: resultMessage,
      data: {
        total: syncResults.products.length,
        products: syncResults.products,
        variants: syncResults.variants,
        errors: syncResults.errors,
        skipped: syncResults.skipped,
      },
    };
  } catch (error: any) {
    console.error(`[syncAllProductsFromPlatform] Lỗi khi đồng bộ tất cả sản phẩm từ ${platform}:`, error);
    return {
      success: false,
      message: `Lỗi khi đồng bộ tất cả sản phẩm từ ${platform}: ${error.message}`,
    };
  }
};

/**
 * Đồng bộ tất cả sản phẩm từ Haravan vào hệ thống với phân trang
 */
export const syncAllProductsFromHaravan = async (
  token: string,
  tenant_id: string,
  bot_id: string,
  options: SyncOptions = {}
): Promise<SyncResult> => {
  // Đảm bảo force_update luôn được bật khi full_sync=true
  const syncOptions = {
    ...options,
    force_update: options.full_sync === true ? true : options.force_update
  };


  return syncAllProductsFromPlatform(
    "haravan",
    token,
    tenant_id,
    bot_id,
    syncOptions
  );
};

/**
 * Đồng bộ tất cả sản phẩm từ Sapo vào hệ thống với phân trang
 */
export const syncAllProductsFromSapo = async (
  sapoUrl: string,
  tenant_id: string,
  bot_id: string,
  options: SyncOptions = {}
): Promise<SyncResult> => {
  // Đảm bảo force_update luôn được bật khi full_sync=true
  const syncOptions = {
    ...options,
    force_update: options.full_sync === true ? true : options.force_update
  };


  return syncAllProductsFromPlatform(
    "sapo",
    sapoUrl,
    tenant_id,
    bot_id,
    syncOptions
  );
};

/**
 * Kiểm tra sản phẩm đã tồn tại trong hệ thống
 * Hàm này chỉ kiểm tra và trả về kết quả ngay lập tức, không thực hiện đồng bộ
 * @param platform Tên nền tảng (haravan, sapo, ...)
 * @param token Token xác thực của nền tảng
 * @param tenant_id ID của tenant
 * @param options Tùy chọn đồng bộ
 * @returns Kết quả kiểm tra
 */
export const checkProductsExistence = async (
  platform: string,
  token: string,
  tenant_id: string,
  options: SyncOptions = {}
): Promise<{
  success: boolean;
  message: string;
  data?: {
    total: number;
    existing: number;
    new: number;
    products: any[];
  }
}> => {
  try {
    // Kiểm tra nền tảng có được hỗ trợ không
    if (!isPlatformSupported(platform)) {
      return {
        success: false,
        message: `Nền tảng ${platform} không được hỗ trợ`,
      };
    }

    // Lấy adapter cho nền tảng
    const adapter = getAdapter(platform);
    if (!adapter) {
      return {
        success: false,
        message: `Không tìm thấy adapter cho nền tảng ${platform}`,
      };
    }

    // Lấy danh sách sản phẩm từ nền tảng (chỉ lấy trang đầu tiên để kiểm tra nhanh)
    const platformProducts = await adapter.getProducts(token, undefined, {
      limit: options.limit || 50,
      page: options.page || 1,
      updated_at_min: options.updated_at_min,
    });

    if (!platformProducts.success) {
      return {
        success: false,
        message: `Lỗi khi lấy danh sách sản phẩm từ ${platform}: ${platformProducts.message}`,
      };
    }

    if (!platformProducts.data || platformProducts.data.length === 0) {
      return {
        success: true,
        message: "Không có sản phẩm nào để đồng bộ",
        data: {
          total: 0,
          existing: 0,
          new: 0,
          products: []
        }
      };
    }

    // Chuyển đổi dữ liệu sản phẩm
    const mappedProducts = platformProducts.data.map(
      (product: any) => adapter.mapToSystem(product, tenant_id, "")
    );

    // Mảng lưu trữ kết quả kiểm tra
    const existingProducts: any[] = [];
    const newProducts: any[] = [];

    // Tối ưu hóa: kiểm tra hàng loạt thay vì từng sản phẩm một
    const sourceIds = mappedProducts.map((p: SystemProduct) => p.source_id);
    const existingProductsList = await checkMultipleProductsExist(
      mappedProducts[0].source,
      sourceIds,
      tenant_id
    );

    // Tạo map để tra cứu nhanh
    const existingProductsMap = new Map();
    existingProductsList.forEach(p => {
      existingProductsMap.set(p.source_id, p);
    });

    // Kiểm tra từng sản phẩm đã tồn tại chưa
    for (const product of mappedProducts) {
      const existingProduct = existingProductsMap.get(product.source_id);

      if (existingProduct) {
        existingProducts.push({
          name: product.name,
          source_id: product.source_id,
          id: existingProduct.id,
          updated_at: existingProduct.updated_at
        });
      } else {
        newProducts.push({
          name: product.name,
          source_id: product.source_id
        });
      }
    }

    // Tạo thông báo kết quả
    const totalProducts = mappedProducts.length;
    const existingCount = existingProducts.length;
    const newCount = newProducts.length;

    let resultMessage = `Đã kiểm tra ${totalProducts} sản phẩm từ ${platform}: `;

    if (existingCount > 0) {
      resultMessage += `${existingCount} sản phẩm đã tồn tại, `;
    }

    resultMessage += `${newCount} sản phẩm mới sẽ được đồng bộ`;

    return {
      success: true,
      message: resultMessage,
      data: {
        total: totalProducts,
        existing: existingCount,
        new: newCount,
        products: [...existingProducts, ...newProducts]
      }
    };
  } catch (error: any) {
    console.error(`Lỗi khi kiểm tra sản phẩm từ ${platform}:`, error);
    return {
      success: false,
      message: `Lỗi khi kiểm tra sản phẩm từ ${platform}: ${error.message}`,
    };
  }
};

/**
 * Kiểm tra sản phẩm từ Haravan đã tồn tại trong hệ thống
 * @param token Token xác thực Haravan
 * @param tenant_id ID của tenant
 * @param options Tùy chọn đồng bộ
 * @returns Kết quả kiểm tra
 */
export const checkHaravanProductsExistence = async (
  token: string,
  tenant_id: string,
  options: SyncOptions = {}
) => {
  return checkProductsExistence("haravan", token, tenant_id, options);
};

/**
 * Kiểm tra sản phẩm từ Sapo đã tồn tại trong hệ thống
 * @param sapoUrl URL đầy đủ của Sapo
 * @param tenant_id ID của tenant
 * @param options Tùy chọn đồng bộ
 * @returns Kết quả kiểm tra
 */
export const checkSapoProductsExistence = async (
  sapoUrl: string,
  tenant_id: string,
  options: SyncOptions = {}
) => {
  return checkProductsExistence("sapo", sapoUrl, tenant_id, options);
};

export default {
  syncProductsFromPlatform,
  syncProductsFromHaravan,
  syncProductsFromSapo,
  syncAllProductsFromPlatform,
  syncAllProductsFromHaravan,
  syncAllProductsFromSapo,
  checkProductsExistence,
  checkHaravanProductsExistence,
  checkSapoProductsExistence
};

