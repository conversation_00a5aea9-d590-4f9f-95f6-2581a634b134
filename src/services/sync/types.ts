/**
 * Interface định nghĩa cấu trúc dữ liệu sản phẩm trong hệ thống
 */
export interface SystemProduct {
  id: string;
  name: string;
  slug: string;
  description: string;
  short_description: string;
  type: 'simple' | 'variable';
  sku: string;
  barcode: string;
  price: number;
  compare_at_price: number | null;
  cost_price: number | null;
  track_inventory: boolean;
  stock_quantity: number | null;
  weight: number | null;
  is_active: boolean;
  is_featured: boolean;
  avatar: string | null;
  images: string[];
  originalImageUrls: string[];
  attributes: Record<string, any>;
  tenant_id: string;
  bot_id: string;
  source: string;
  source_id: string;
  processedVariants: SystemProductVariant[];
}

/**
 * Interface định nghĩa cấu trúc dữ liệu biến thể sản phẩm trong hệ thống
 */
export interface SystemProductVariant {
  id: string;
  product_id: string | null;
  sku: string;
  barcode: string;
  price: number;
  compare_at_price: number | null;
  cost_price: number | null;
  stock_quantity: number;
  weight: number | null;
  is_active: boolean;
  attributes: Record<string, string>;
  avatar: string | null;
  name: string;
  tenant_id: string;
}

/**
 * Interface định nghĩa cấu trúc adapter cho các nền tảng
 */
export interface PlatformAdapter {
  mapToSystem: (product: any, tenant_id: string, bot_id: string) => SystemProduct;
  getProducts: (token: string, storeUrl: string | undefined, options: any) => Promise<any>;
  platformName: string;
}

/**
 * Interface định nghĩa kết quả đồng bộ sản phẩm
 */
export interface SyncResult {
  success: boolean;
  message: string;
  data?: {
    total: number;
    products: any[];
    variants: any[];
    errors?: any[];
    skipped?: any[]; // Danh sách sản phẩm đã bỏ qua vì đã tồn tại
  };
}

/**
 * Interface định nghĩa tùy chọn đồng bộ sản phẩm
 */
export interface SyncOptions {
  limit?: number;
  page?: number;
  updated_at_min?: string;
  updated_at_max?: string;
  full_sync?: boolean;
  force_update?: boolean; // Cập nhật bắt buộc ngay cả khi sản phẩm đã tồn tại
}
