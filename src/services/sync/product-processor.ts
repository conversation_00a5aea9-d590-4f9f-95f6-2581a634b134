import { supabaseAdmin } from '../../config/supabase';
import { uploadMultipleProductImages } from '../supabase/tenant-storage.service';
import { SystemProduct } from './types';
import { generateVariantSku as generateVariantSkuFromManager, validateAndNormalizeSku } from './sku-manager';

/**
 * Kiểm tra sản phẩm đã tồn tại trong cơ sở dữ liệu chưa sử dụng Supabase Admin
 * @param source Nguồn sản phẩm (haravan, sapo, ...)
 * @param source_id ID sản phẩm từ nguồn
 * @param tenant_id ID của tenant
 * @returns Thông tin sản phẩm nếu đã tồn tại, null nếu chưa tồn tại
 */
export const checkProductExists = async (source: string, source_id: string, tenant_id: string) => {
  try {
    const { data: existingProduct, error } = await supabaseAdmin
      .from('products')
      .select('id, name, updated_at, created_at, price, stock_quantity, source_id')
      .eq('source', source)
      .eq('source_id', source_id)
      .eq('tenant_id', tenant_id)
      .single();

    if (error && error.code !== 'PGRST116') {
      console.error(`Lỗi khi kiểm tra sản phẩm tồn tại:`, error);
      return null;
    }

    return existingProduct || null;
  } catch (error: any) {
    console.error(`Lỗi khi kiểm tra sản phẩm tồn tại (source: ${source}, source_id: ${source_id}):`, error);
    return null;
  }
};

/**
 * Kiểm tra nhiều sản phẩm đã tồn tại trong cơ sở dữ liệu chưa sử dụng Supabase Admin (tối ưu hóa hiệu suất)
 * @param source Nguồn sản phẩm (haravan, sapo, ...)
 * @param source_ids Danh sách ID sản phẩm từ nguồn
 * @param tenant_id ID của tenant
 * @returns Danh sách thông tin sản phẩm đã tồn tại
 */
export const checkMultipleProductsExist = async (source: string, source_ids: string[], tenant_id: string) => {
  try {
    if (!source_ids || source_ids.length === 0) {
      return [];
    }

    const { data: existingProducts, error } = await supabaseAdmin
      .from('products')
      .select('id, name, updated_at, created_at, price, stock_quantity, source_id')
      .eq('source', source)
      .in('source_id', source_ids)
      .eq('tenant_id', tenant_id);

    if (error) {
      console.error(`Lỗi khi kiểm tra nhiều sản phẩm tồn tại:`, error);
      return [];
    }

    return existingProducts || [];
  } catch (error: any) {
    console.error(`Lỗi khi kiểm tra nhiều sản phẩm tồn tại (source: ${source}):`, error);
    return [];
  }
};

/**
 * Xử lý và upload hình ảnh cho sản phẩm
 * @param products Danh sách sản phẩm cần xử lý hình ảnh
 * @param tenant_id ID của tenant
 * @returns Danh sách sản phẩm đã được cập nhật URL hình ảnh
 */
export const processProductImages = async (products: SystemProduct[], tenant_id: string): Promise<SystemProduct[]> => {
  const processedProducts = [...products];

  for (let i = 0; i < processedProducts.length; i++) {
    const product = processedProducts[i];
    try {
      // Upload hình ảnh lên Supabase storage
      if (product.originalImageUrls && product.originalImageUrls.length > 0) {

        try {
          // Sử dụng tenant-based storage service với service role cho đồng bộ sản phẩm
          const uploadedImageUrls = await uploadMultipleProductImages(
            product.originalImageUrls,
            tenant_id,
            true // useServiceRole=true cho đồng bộ sản phẩm
          );

          // Cập nhật URL hình ảnh trong sản phẩm
          processedProducts[i].images = uploadedImageUrls;
          processedProducts[i].avatar = uploadedImageUrls[0] || product.avatar;



          // Cập nhật URL hình ảnh trong biến thể nếu có
          if (product.processedVariants && product.processedVariants.length > 0) {
            for (let j = 0; j < product.processedVariants.length; j++) {
              const variant = product.processedVariants[j];
              // Tìm URL hình ảnh tương ứng trong mảng đã upload
              if (variant.avatar) {
                const originalIndex = product.originalImageUrls.findIndex((url: string) => url === variant.avatar);
                if (originalIndex >= 0 && uploadedImageUrls[originalIndex]) {
                  processedProducts[i].processedVariants[j].avatar = uploadedImageUrls[originalIndex];
                }
              }
            }
          }
        } catch (uploadError: any) {
          console.error(`❌ [IMAGES] Failed to upload images for "${product.name}":`, uploadError.message);

          // Nếu có lỗi khi upload, vẫn giữ lại URL gốc để đảm bảo sản phẩm được lưu
          processedProducts[i].images = product.originalImageUrls;
          processedProducts[i].avatar = product.originalImageUrls[0] || product.avatar;
        }
      }
    } catch (error: any) {
      console.error(`❌ Lỗi khi xử lý hình ảnh cho sản phẩm "${product.name}":`, error.message);
      // Tiếp tục với URL gốc nếu có lỗi khi upload
      if (product.originalImageUrls && product.originalImageUrls.length > 0) {
        processedProducts[i].images = product.originalImageUrls;
        processedProducts[i].avatar = product.originalImageUrls[0] || product.avatar;
      }
    }
  }

  return processedProducts;
};

/**
 * Lưu sản phẩm vào cơ sở dữ liệu sử dụng Supabase Admin client trực tiếp
 * @param product Sản phẩm cần lưu
 * @param tenant_id ID của tenant
 * @returns Kết quả lưu sản phẩm
 */
export const saveProductToDatabase = async (product: SystemProduct, tenant_id: string) => {
  try {
    // Kiểm tra và chuẩn hóa dữ liệu trước khi lưu
    const sanitizedProduct = await sanitizeProductData(product);

    // Kiểm tra xem sản phẩm đã tồn tại chưa (dựa vào source_id) sử dụng Supabase Admin
    const { data: existingProduct, error: checkError } = await supabaseAdmin
      .from('products')
      .select('id')
      .eq('source', sanitizedProduct.source)
      .eq('source_id', sanitizedProduct.source_id)
      .eq('tenant_id', tenant_id)
      .single();

    if (checkError && checkError.code !== 'PGRST116') {
      console.error(`Lỗi khi kiểm tra sản phẩm tồn tại:`, checkError);
    }

    let productId;

    if (existingProduct) {
      // Cập nhật sản phẩm đã tồn tại
      productId = existingProduct.id;

      try {
        const { error: updateError } = await supabaseAdmin
          .from('products')
          .update({
            name: sanitizedProduct.name,
            slug: sanitizedProduct.slug,
            description: sanitizedProduct.description,
            short_description: sanitizedProduct.short_description,
            type: sanitizedProduct.type,
            sku: sanitizedProduct.sku,
            barcode: sanitizedProduct.barcode,
            price: sanitizedProduct.price,
            compare_at_price: sanitizedProduct.compare_at_price,
            cost_price: sanitizedProduct.cost_price,
            track_inventory: sanitizedProduct.track_inventory,
            stock_quantity: sanitizedProduct.stock_quantity,
            weight: sanitizedProduct.weight,
            is_active: true, // Luôn đánh dấu sản phẩm là active khi đồng bộ
            is_featured: sanitizedProduct.is_featured,
            avatar: sanitizedProduct.avatar,
            images: sanitizedProduct.images,
            attributes: sanitizedProduct.attributes,
            bot_id: sanitizedProduct.bot_id,
            source: sanitizedProduct.source,
            source_id: sanitizedProduct.source_id,
            updated_at: new Date().toISOString()
          })
          .eq('id', productId)
          .select()
          .single();

        if (updateError) {
          throw updateError;
        }

      } catch (updateError: any) {
        console.error(`Lỗi khi cập nhật sản phẩm ${sanitizedProduct.name}:`, updateError);

        try {
          // Thử cập nhật với các trường bắt buộc sử dụng Supabase JS trực tiếp
          const { error: minimalUpdateError } = await supabaseAdmin
            .from('products')
            .update({
              name: sanitizedProduct.name,
              slug: sanitizedProduct.slug,
              type: sanitizedProduct.type,
              sku: sanitizedProduct.sku,
              price: sanitizedProduct.price,
              compare_at_price: sanitizedProduct.compare_at_price,
              stock_quantity: sanitizedProduct.stock_quantity,
              is_active: true,
              avatar: sanitizedProduct.avatar,
              images: sanitizedProduct.images,
              bot_id: sanitizedProduct.bot_id,
              source: sanitizedProduct.source,
              source_id: sanitizedProduct.source_id,
              updated_at: new Date().toISOString()
            })
            .eq('id', productId)
            .select()
            .single();

          if (minimalUpdateError) {
            throw minimalUpdateError;
          }

        } catch (minimalUpdateError: any) {
          console.error(`Lỗi nghiêm trọng khi cập nhật sản phẩm ${sanitizedProduct.name} với các trường bắt buộc:`, minimalUpdateError);

          // Thử lần cuối với các trường tối thiểu và giá trị mặc định
          const { error: finalUpdateError } = await supabaseAdmin
            .from('products')
            .update({
              name: sanitizedProduct.name,
              type: 'simple',
              sku: sanitizedProduct.sku,
              price: sanitizedProduct.price || 0,
              stock_quantity: 0,
              is_active: true,
              updated_at: new Date().toISOString()
            })
            .eq('id', productId)
            .select()
            .single();

          if (finalUpdateError) {
            throw finalUpdateError;
          }

        }
      }

      return {
        id: productId,
        name: sanitizedProduct.name,
        action: 'updated'
      };
    } else {
      // Thêm sản phẩm mới sử dụng Supabase JS trực tiếp
      try {
        // Đảm bảo các trường bắt buộc luôn có giá trị
        const { data: insertResult, error: insertError } = await supabaseAdmin
          .from('products')
          .insert({
            id: sanitizedProduct.id,
            tenant_id: tenant_id,
            name: sanitizedProduct.name,
            slug: sanitizedProduct.slug,
            description: sanitizedProduct.description,
            short_description: sanitizedProduct.short_description,
            type: sanitizedProduct.type,
            sku: sanitizedProduct.sku,
            barcode: sanitizedProduct.barcode,
            price: sanitizedProduct.price,
            compare_at_price: sanitizedProduct.compare_at_price,
            cost_price: sanitizedProduct.cost_price,
            track_inventory: sanitizedProduct.track_inventory,
            stock_quantity: sanitizedProduct.stock_quantity,
            weight: sanitizedProduct.weight,
            is_active: true, // Luôn đánh dấu sản phẩm là active khi đồng bộ
            is_featured: sanitizedProduct.is_featured,
            avatar: sanitizedProduct.avatar,
            images: sanitizedProduct.images,
            attributes: sanitizedProduct.attributes,
            bot_id: sanitizedProduct.bot_id,
            source: sanitizedProduct.source,
            source_id: sanitizedProduct.source_id,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .select()
          .single();

        if (insertError) {
          throw insertError;
        }

        productId = insertResult.id;

      } catch (insertError: any) {
        console.error(`Lỗi khi thêm sản phẩm ${sanitizedProduct.name}:`, insertError);

        try {
          // Thử thêm với các trường bắt buộc sử dụng Supabase JS trực tiếp
          const { data: minimalResult, error: minimalInsertError } = await supabaseAdmin
            .from('products')
            .insert({
              id: sanitizedProduct.id,
              tenant_id: tenant_id,
              name: sanitizedProduct.name,
              slug: sanitizedProduct.slug,
              type: sanitizedProduct.type,
              sku: sanitizedProduct.sku,
              price: sanitizedProduct.price,
              compare_at_price: sanitizedProduct.compare_at_price,
              stock_quantity: sanitizedProduct.stock_quantity,
              is_active: true,
              avatar: sanitizedProduct.avatar,
              images: sanitizedProduct.images,
              bot_id: sanitizedProduct.bot_id,
              source: sanitizedProduct.source,
              source_id: sanitizedProduct.source_id,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            })
            .select()
            .single();

          if (minimalInsertError) {
            throw minimalInsertError;
          }

          productId = minimalResult.id;
        } catch (minimalInsertError: any) {
          console.error(`Lỗi nghiêm trọng khi thêm sản phẩm ${sanitizedProduct.name} với thông tin tối thiểu:`, minimalInsertError);

          // Thử lần cuối với các trường bắt buộc và giá trị mặc định
          const { data: finalResult, error: finalInsertError } = await supabaseAdmin
            .from('products')
            .insert({
              id: sanitizedProduct.id,
              tenant_id: tenant_id,
              name: sanitizedProduct.name,
              slug: sanitizedProduct.slug,
              type: 'simple',
              sku: sanitizedProduct.sku,
              price: 0,
              stock_quantity: 0,
              is_active: true,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            })
            .select()
            .single();

          if (finalInsertError) {
            throw finalInsertError;
          }

          productId = finalResult.id;

        }
      }

      return {
        id: productId,
        name: sanitizedProduct.name,
        action: 'created'
      };
    }
  } catch (error: any) {
    console.error(`Lỗi nghiêm trọng khi lưu sản phẩm:`, error);
    throw new Error(`Không thể lưu sản phẩm: ${error.message}`);
  }
};



/**
 * Chuẩn hóa dữ liệu sản phẩm trước khi lưu vào cơ sở dữ liệu
 * @param product Sản phẩm cần chuẩn hóa
 * @returns Sản phẩm đã được chuẩn hóa
 */
async function sanitizeProductData(product: SystemProduct): Promise<SystemProduct> {
  // Tạo bản sao để không ảnh hưởng đến dữ liệu gốc
  const sanitized = { ...product };

  // Đảm bảo các trường chuỗi bắt buộc không bị null
  sanitized.name = sanitized.name || 'Sản phẩm không tên';
  sanitized.slug = sanitized.slug || sanitized.name.toLowerCase().replace(/[^a-z0-9]+/g, '-');

  // Đảm bảo trường type luôn có giá trị (bắt buộc)
  sanitized.type = sanitized.type || 'simple'; // Mặc định là 'simple' nếu không có giá trị

  // Xử lý SKU - chỉ sử dụng SKU có sẵn hoặc tạo từ source_id
  if (!sanitized.sku || sanitized.sku.trim() === '') {
    // Chỉ tạo SKU nếu có source và source_id
    if (sanitized.source && sanitized.source_id) {
      sanitized.sku = await validateAndNormalizeSku('', sanitized.source, sanitized.tenant_id, sanitized.source_id);
    } else {
      // Nếu không có source_id, để SKU rỗng - sản phẩm sẽ không có SKU
      sanitized.sku = '';
    }
  } else {
    // Giữ nguyên SKU có sẵn, chỉ trim whitespace
    sanitized.sku = sanitized.sku.trim();
  }

  // Các trường chuỗi khác
  sanitized.description = sanitized.description || '';
  sanitized.short_description = sanitized.short_description || '';
  sanitized.barcode = sanitized.barcode || '';

  // Đảm bảo các trường số bắt buộc không bị null
  sanitized.price = isNaN(sanitized.price) ? 0 : sanitized.price;
  sanitized.stock_quantity = sanitized.stock_quantity === null || isNaN(Number(sanitized.stock_quantity)) ? 0 : Number(sanitized.stock_quantity); // Đảm bảo stock_quantity luôn có giá trị

  // Các trường số khác
  sanitized.compare_at_price = sanitized.compare_at_price === null || isNaN(Number(sanitized.compare_at_price)) ? null : Number(sanitized.compare_at_price);
  sanitized.cost_price = sanitized.cost_price === null || isNaN(Number(sanitized.cost_price)) ? null : Number(sanitized.cost_price);
  sanitized.weight = sanitized.weight === null || isNaN(Number(sanitized.weight)) ? null : Number(sanitized.weight);

  // Đảm bảo các trường boolean có giá trị mặc định
  sanitized.track_inventory = sanitized.track_inventory !== undefined ? !!sanitized.track_inventory : true;
  sanitized.is_active = true; // Luôn đánh dấu sản phẩm là active khi đồng bộ
  sanitized.is_featured = sanitized.is_featured !== undefined ? !!sanitized.is_featured : false;

  // Đảm bảo mảng hình ảnh không bị null
  sanitized.images = sanitized.images || [];

  // Đảm bảo attributes là object hợp lệ
  sanitized.attributes = sanitized.attributes || {};



  return sanitized;
}

/**
 * Chuẩn hóa SKU cho biến thể sản phẩm - chỉ sử dụng SKU có sẵn
 * @param variant Biến thể cần chuẩn hóa SKU
 * @param productSku SKU của sản phẩm cha (không sử dụng)
 * @param variantIndex Chỉ số của biến thể (không sử dụng)
 * @returns SKU đã được chuẩn hóa hoặc rỗng
 */
function sanitizeVariantSku(variant: any, productSku: string, variantIndex: number = 0): string {
  // Chỉ sử dụng SKU có sẵn từ variant
  if (variant.sku && variant.sku.trim() !== '') {
    return variant.sku.trim();
  }

  // Không tạo SKU tự động nữa
  return '';
}

/**
 * Lưu biến thể sản phẩm vào cơ sở dữ liệu sử dụng Supabase Admin client
 * @param variant Biến thể cần lưu
 * @param productId ID của sản phẩm cha
 * @param tenant_id ID của tenant
 * @param productSku SKU của sản phẩm cha (để tạo SKU cho biến thể nếu cần)
 * @param variantIndex Chỉ số của biến thể (để tạo SKU chuẩn hóa)
 * @returns Kết quả lưu biến thể
 */
export const saveVariantToDatabase = async (variant: any, productId: string, tenant_id: string, productSku?: string, variantIndex: number = 0) => {
  try {
    // Cập nhật product_id cho biến thể
    variant.product_id = productId;
    variant.tenant_id = tenant_id; // Đảm bảo tenant_id được set

    // Giữ nguyên SKU có sẵn hoặc để trống nếu không có
    if (!variant.sku || variant.sku.trim() === '') {
      variant.sku = ''; // Để trống nếu không có SKU từ source
    } else {
      variant.sku = variant.sku.trim();
    }

    // Kiểm tra xem biến thể đã tồn tại chưa sử dụng Supabase Admin
    const { data: existingVariant, error: checkError } = await supabaseAdmin
      .from('product_variants')
      .select('id')
      .eq('product_id', productId)
      .contains('attributes', variant.attributes)
      .eq('tenant_id', tenant_id)
      .single();

    if (checkError && checkError.code !== 'PGRST116') {
      console.error(`Lỗi khi kiểm tra biến thể tồn tại:`, checkError);
    }

    if (existingVariant) {
      // Cập nhật biến thể đã tồn tại sử dụng Supabase JS trực tiếp
      const variantId = existingVariant.id;

      const { error: updateError } = await supabaseAdmin
        .from('product_variants')
        .update({
          sku: variant.sku,
          barcode: variant.barcode,
          price: variant.price,
          compare_at_price: variant.compare_at_price,
          cost_price: variant.cost_price,
          stock_quantity: variant.stock_quantity,
          weight: variant.weight,
          is_active: true, // Luôn đánh dấu biến thể là active khi đồng bộ
          attributes: variant.attributes,
          avatar: variant.avatar,
          name: variant.name,
          updated_at: new Date().toISOString()
        })
        .eq('id', variantId)
        .select()
        .single();

      if (updateError) {
        throw updateError;
      }

      return {
        id: variantId,
        name: variant.name,
        action: 'updated'
      };
    } else {
      // Thêm biến thể mới sử dụng Supabase JS trực tiếp
      const { data: insertResult, error: insertError } = await supabaseAdmin
        .from('product_variants')
        .insert({
          id: variant.id,
          product_id: variant.product_id,
          tenant_id: tenant_id,
          sku: variant.sku,
          barcode: variant.barcode,
          price: variant.price,
          compare_at_price: variant.compare_at_price,
          cost_price: variant.cost_price,
          stock_quantity: variant.stock_quantity,
          weight: variant.weight,
          is_active: true, // Luôn đánh dấu biến thể là active khi đồng bộ
          attributes: variant.attributes,
          avatar: variant.avatar,
          name: variant.name,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (insertError) {
        throw insertError;
      }

      const variantId = insertResult.id;

      return {
        id: variantId,
        name: variant.name,
        action: 'created'
      };
    }
  } catch (error: any) {
    throw error;
  }
};

