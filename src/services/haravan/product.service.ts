import axios from 'axios';
import dotenv from 'dotenv';

dotenv.config();

// Cấu hình API Haravan
const HARAVAN_API_BASE_URL = 'https://apis.haravan.com/com';

/**
 * L<PERSON>y danh sách sản phẩm từ <PERSON>van
 * @param token Token xác thực <PERSON>
 * @param params Tham số truy vấn (limit, page, v.v.)
 */
export const getProducts = async (
  token: string,
  params: {
    limit?: number;
    page?: number;
    updated_at_min?: string;
    updated_at_max?: string;
    created_at_min?: string;
    created_at_max?: string;
    status?: 'active' | 'archived' | 'draft';
    ids?: string;
  } = {}
) => {
  try {
    const response = await axios.get(`${HARAVAN_API_BASE_URL}/products.json`, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      params
    });

    return {
      success: true,
      data: response.data.products,
      total: response.data.products?.length || 0
    };
  } catch (error: any) {
    console.error('Lỗi khi lấy danh sách sản phẩm từ <PERSON>van:', error.response?.data || error.message);
    return {
      success: false,
      message: error.response?.data?.error || error.message,
      status: error.response?.status
    };
  }
};

/**
 * Lấy thông tin chi tiết của một sản phẩm từ Haravan
 * @param token Token xác thực Haravan
 * @param productId ID sản phẩm trên Haravan
 */
export const getProductById = async (token: string, productId: string) => {
  try {
    const response = await axios.get(`${HARAVAN_API_BASE_URL}/products/${productId}.json`, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    });

    return {
      success: true,
      data: response.data.product
    };
  } catch (error: any) {
    console.error('Lỗi khi lấy thông tin sản phẩm từ Haravan:', error.response?.data || error.message);
    return {
      success: false,
      message: error.response?.data?.error || error.message,
      status: error.response?.status
    };
  }
};

/**
 * Lấy số lượng sản phẩm từ Haravan
 * @param token Token xác thực Haravan
 */
export const getProductCount = async (token: string, params: any = {}) => {
  try {
    const response = await axios.get(`${HARAVAN_API_BASE_URL}/products/count.json`, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      params
    });

    return {
      success: true,
      count: response.data.count
    };
  } catch (error: any) {
    console.error('Lỗi khi lấy số lượng sản phẩm từ Haravan:', error.response?.data || error.message);
    return {
      success: false,
      message: error.response?.data?.error || error.message,
      status: error.response?.status
    };
  }
};

/**
 * Lấy danh sách biến thể của một sản phẩm từ Haravan
 * @param token Token xác thực Haravan
 * @param productId ID sản phẩm trên Haravan
 */
export const getProductVariants = async (token: string, productId: string) => {
  try {
    const response = await axios.get(`${HARAVAN_API_BASE_URL}/products/${productId}/variants.json`, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    });

    return {
      success: true,
      data: response.data.variants
    };
  } catch (error: any) {
    console.error('Lỗi khi lấy danh sách biến thể từ Haravan:', error.response?.data || error.message);
    return {
      success: false,
      message: error.response?.data?.error || error.message,
      status: error.response?.status
    };
  }
};

export default {
  getProducts,
  getProductById,
  getProductCount,
  getProductVariants
};
