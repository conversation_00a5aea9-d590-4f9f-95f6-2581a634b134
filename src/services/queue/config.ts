/**
 * <PERSON><PERSON><PERSON> hình tối ưu cho BullMQ với Dragonfly/Redis
 */
import { ConnectionOptions, DefaultJobOptions, WorkerOptions, QueueOptions } from 'bullmq';
import dotenv from 'dotenv';

// Load biến môi trường
dotenv.config();

// C<PERSON>u hình môi trường
const NODE_ENV = process.env.NODE_ENV || 'development';
const IS_PRODUCTION = NODE_ENV === 'production';

/**
 * C<PERSON>u hình kết nối Redis/Dragonfly tối ưu
 */
export const redisConnection: ConnectionOptions = {
  host: process.env.REDIS_HOST || '*************',
  port: parseInt(process.env.REDIS_PORT || '6361'),
  password: process.env.REDIS_PASSWORD || 'tvqbjr0pnrnrluxtf9zdqkvgtoykpkb0',
  username: process.env.REDIS_USERNAME,
  db: parseInt(process.env.REDIS_DB || '0'),

  // Cấu hình tối ưu cho BullMQ với Dragonfly
  maxRetriesPerRequest: null, // Bắt buộc cho Dragonfly
  enableReadyCheck: false,    // Tắt ready check cho Dragonfly
  enableOfflineQueue: false,  // Tắt offline queue cho BullMQ
  lazyConnect: true,          // Kết nối lazy

  // Timeout settings - tối ưu cho production
  connectTimeout: IS_PRODUCTION ? 60000 : 30000,
  commandTimeout: IS_PRODUCTION ? 60000 : 30000,
  keepAlive: 30000,

  // Connection pool settings
  family: 4, // Force IPv4

  // Retry strategy tối ưu
  retryStrategy: (times: number) => {
    const maxRetries = IS_PRODUCTION ? 5 : 3;
    if (times > maxRetries) {
      return null;
    }

    // Exponential backoff với jitter
    const delay = Math.min(times * 1000 + Math.random() * 1000, 10000);
    return delay;
  },

  // Event handlers
  // maxLoadingTimeout: 30000, // Removed - not a valid ConnectionOptions property
};

/**
 * Cấu hình Redis riêng cho Message Buffer Service
 * Khác với BullMQ, cần enableOfflineQueue để tránh lỗi khi connection bị mất
 */
export const messageBufferRedisConnection: ConnectionOptions = {
  ...redisConnection,
  enableOfflineQueue: true,   // Bật offline queue cho message buffer
  enableReadyCheck: true,     // Bật ready check cho message buffer
  lazyConnect: false,         // Kết nối ngay lập tức
  maxRetriesPerRequest: 3,    // Cho phép retry cho message buffer
  // retryDelayOnFailover: 100,  // Delay khi failover
};

/**
 * Cấu hình chung cho tất cả Queue và Worker
 */
export const bullMQConfig: QueueOptions = {
  connection: redisConnection,
  // Sử dụng prefix với hashtag cho Dragonfly cluster optimization
  prefix: process.env.BULLMQ_PREFIX || '{mooly-queue}',
  
  // Note: stalledInterval và maxStalledCount sẽ được config ở Worker level
};

/**
 * Cấu hình mặc định cho các Queue
 */
export const defaultQueueConfig: QueueOptions = {
  ...bullMQConfig,
  
  defaultJobOptions: {
    // Job retention - tối ưu cho production
    removeOnComplete: IS_PRODUCTION ? 50 : 10,   // Giữ ít job completed hơn trong production
    removeOnFail: IS_PRODUCTION ? 100 : 20,      // Giữ nhiều failed job hơn để debug
    
    // Retry configuration
    attempts: IS_PRODUCTION ? 5 : 3,             // Nhiều attempts hơn trong production
    backoff: {
      type: 'exponential',
      delay: 2000,                               // Bắt đầu với 2s
    },
    
    // Job timeouts
    delay: 0,                                    // Không delay mặc định
    
    // Job priority (cao hơn = ưu tiên hơn)
    priority: 0,
  } as DefaultJobOptions,
};

/**
 * Cấu hình mặc định cho Worker
 */
export const defaultWorkerConfig: Omit<WorkerOptions, 'connection'> = {
  // Concurrency dựa trên môi trường và số CPU - tăng để xử lý job nhanh hơn
  concurrency: parseInt(process.env.QUEUE_CONCURRENCY || (IS_PRODUCTION ? '15' : '5')),
  
  // Limiter để tránh overload - nới lỏng để job chạy nhanh hơn
  limiter: {
    max: parseInt(process.env.QUEUE_RATE_LIMIT_MAX || '20'),      // Tăng từ 10 lên 20
    duration: parseInt(process.env.QUEUE_RATE_LIMIT_DURATION || '60000'), // Trong 60s
  },
  
  // Worker specific settings - tối ưu cho tốc độ
  stalledInterval: 10000,      // Kiểm tra stalled jobs mỗi 10s (giảm từ 30s)
  maxStalledCount: 1,          // Job chỉ có thể stalled 1 lần
  
  // Auto-run - quan trọng!
  autorun: true,
  
  // Thêm cấu hình để worker xử lý job ngay lập tức
  drainDelay: 1,               // Chờ 1ms trước khi tắt worker
};

/**
 * Tên các Queue - sử dụng naming convention nhất quán
 */
export const QUEUE_NAMES = {
  PRODUCT_SYNC: '{product-sync}',
  EMAIL_NOTIFICATION: '{email-notification}',
  DATA_CLEANUP: '{data-cleanup}',
  WEBHOOK_PROCESSING: '{webhook-processing}',
  MESSAGE_BATCHING: '{message-batching}',
  FOLLOW_UP: '{follow-up}',
} as const;

/**
 * Cấu hình cho từng loại Queue cụ thể
 */
export const queueConfigs = {
  [QUEUE_NAMES.PRODUCT_SYNC]: {
    ...defaultQueueConfig,
    defaultJobOptions: {
      ...defaultQueueConfig.defaultJobOptions,
      attempts: 5,                    // Quan trọng nên retry nhiều hơn
      priority: 10,                   // Ưu tiên cao
      backoff: {
        type: 'exponential',
        delay: 5000,                  // Chờ lâu hơn vì sync có thể bị rate limit
      },
    },
  },
  
  [QUEUE_NAMES.EMAIL_NOTIFICATION]: {
    ...defaultQueueConfig,
    defaultJobOptions: {
      ...defaultQueueConfig.defaultJobOptions,
      attempts: 3,
      priority: 5,
      backoff: {
        type: 'fixed',
        delay: 10000,                 // Fixed delay cho email
      },
    },
  },

  [QUEUE_NAMES.MESSAGE_BATCHING]: {
    ...defaultQueueConfig,
    defaultJobOptions: {
      ...defaultQueueConfig.defaultJobOptions,
      attempts: 3,                    // Retry ít hơn vì là real-time
      priority: 15,                   // Ưu tiên cao nhất cho user experience
      backoff: {
        type: 'fixed',
        delay: 1000,                  // Retry nhanh cho message
      },
      removeOnComplete: 20,           // Giữ nhiều hơn để debug
      removeOnFail: 50,               // Giữ failed jobs để phân tích
    },
  },

  [QUEUE_NAMES.FOLLOW_UP]: {
    ...defaultQueueConfig,
    defaultJobOptions: {
      ...defaultQueueConfig.defaultJobOptions,
      attempts: 3,                    // Retry ít cho follow-up
      priority: 8,                    // Ưu tiên trung bình
      backoff: {
        type: 'exponential',
        delay: 2000,                  // Exponential backoff cho follow-up
      },
      removeOnComplete: 30,           // Giữ nhiều để track follow-up history
      removeOnFail: 100,              // Giữ failed jobs để debug follow-up issues
    },
  },
} as const;

/**
 * Cấu hình cho từng loại Worker cụ thể
 */
export const workerConfigs = {
  [QUEUE_NAMES.PRODUCT_SYNC]: {
    ...defaultWorkerConfig,
    concurrency: parseInt(process.env.PRODUCT_SYNC_CONCURRENCY || '5'), // Tăng từ 3 lên 5 để xử lý nhanh hơn
    limiter: {
      max: 20,                        // Tăng từ 10 lên 20 để xử lý nhiều job hơn
      duration: 60000,                // Trong 60s
    },
    stalledInterval: 5000,            // Kiểm tra stalled nhanh hơn (5s)
    maxStalledCount: 1,               // Job chỉ có thể stalled 1 lần
    drainDelay: 1,                    // Xử lý job ngay lập tức
  },
  
  [QUEUE_NAMES.EMAIL_NOTIFICATION]: {
    ...defaultWorkerConfig,
    concurrency: parseInt(process.env.EMAIL_CONCURRENCY || '5'),
    limiter: {
      max: 50,                        // Email có thể xử lý nhiều hơn
      duration: 60000,
    },
  },

  [QUEUE_NAMES.MESSAGE_BATCHING]: {
    ...defaultWorkerConfig,
    concurrency: parseInt(process.env.MESSAGE_BATCH_CONCURRENCY || '15'), // Cao hơn cho real-time
    limiter: {
      max: 100,                       // Nhiều message hơn
      duration: 60000,                // Trong 60s
    },
    stalledInterval: 10000,           // Kiểm tra stalled nhanh hơn (10s)
    maxStalledCount: 2,               // Cho phép stalled 2 lần
  },

  [QUEUE_NAMES.FOLLOW_UP]: {
    ...defaultWorkerConfig,
    concurrency: parseInt(process.env.FOLLOW_UP_CONCURRENCY || '10'), // Vừa phải cho follow-up
    limiter: {
      max: 50,                        // Giới hạn follow-up messages
      duration: 60000,                // Trong 60s
    },
    stalledInterval: 15000,           // Kiểm tra stalled chậm hơn (15s)
    maxStalledCount: 2,               // Cho phép stalled 2 lần
  },
} as const;

/**
 * Health check configuration
 */
export const healthCheckConfig = {
  checkInterval: 60000,               // Tăng từ 30s lên 60s để ít log hơn
  unhealthyThreshold: 3,              // 3 lần fail = unhealthy
  healthyThreshold: 2,                // 2 lần success = healthy
  timeout: 5000,                      // Timeout cho health check
  silentMode: IS_PRODUCTION,          // Tắt log info trong production
};

/**
 * Logging configuration - Tối ưu cho production
 */
export const loggingConfig = {
  logLevel: process.env.QUEUE_LOG_LEVEL || (IS_PRODUCTION ? 'error' : 'info'),
  logJobData: !IS_PRODUCTION,        // Chỉ log job data trong development
  logJobResult: !IS_PRODUCTION,      // Chỉ log job result trong development
  healthCheckSilent: IS_PRODUCTION,   // Tắt log health check trong production
  logWorkerEvents: !IS_PRODUCTION,   // Tắt log worker events trong production
};



// Export types cho TypeScript
export type QueueName = keyof typeof QUEUE_NAMES;
export type { ConnectionOptions, DefaultJobOptions, WorkerOptions, QueueOptions };
