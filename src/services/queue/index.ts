/**
 * BullMQ Service - T<PERSON>i ưu hóa và chuẩn hóa
 * Export tất cả các components cần thiết
 */

// Config exports
export {
  redisConnection,
  bullMQConfig,
  defaultQueueConfig,
  defaultWorkerConfig,
  QUEUE_NAMES,
  queueConfigs,
  workerConfigs,
  healthCheckConfig,
  loggingConfig,
  type QueueName,
  type ConnectionOptions,
  type DefaultJobOptions,
  type WorkerOptions,
  type QueueOptions
} from './config';

// Queue exports - sử dụng instances từ queues.ts
export {
  productSyncQueue,
  followUpQueue,
  addHaravanSyncJob,
  addSapoSyncJob,
  getQueue,
  getQueuesStats,
  pauseQueue,
  resumeQueue,
  cleanQueue,
  closeAllQueues,
} from './queues';

// Worker exports
export {
  startAllWorkers,
  stopAllWorkers,
  getWorker,
  getProductSyncWorker,
  getWorkersHealthStatus,
  restartWorker,
} from './workers';

// Bull Board exports - sử dụng cùng queue instances
export {
  getBullBoardRouter,
  addQueueToBoard,
  getAllQueuesStats as getBullBoardStats,
  cleanupAllQueues,
  pauseAllQueues,
  resumeAllQueues,
  getBullBoardHealth,
  queues as bullBoardQueues,
} from './bull-board.service';

// Health monitoring exports
export {
  performHealthCheck,
  startPeriodicHealthCheck,
  createHealthCheckEndpoint,
  createMetricsEndpoint,
} from './health';

// Processor exports
export { processProductSyncJob } from './processors/product-sync.processor';
export { followUpJobProcessor, handleFailedFollowUpJob } from './processors/follow-up.processor';

// Follow-up service exports
export {
  initializeFollowUpQueue,
  getFollowUpQueue,
  getChannelFollowUpConfig,
  createFollowUpJob,
  cancelFollowUpJobs,
  getConversationFollowUpState,
  closeFollowUpQueue,
} from './follow-up.service';

// Import functions for internal use
import { startAllWorkers as _startAllWorkers, stopAllWorkers as _stopAllWorkers } from './workers';
import { productSyncQueue as _productSyncQueue, addHaravanSyncJob as _addHaravanSyncJob, addSapoSyncJob as _addSapoSyncJob, closeAllQueues as _closeAllQueues } from './queues';
import { startPeriodicHealthCheck as _startPeriodicHealthCheck, performHealthCheck as _performHealthCheck } from './health';
import { QUEUE_NAMES as _QUEUE_NAMES } from './config';

// Utility function để khởi tạo toàn bộ BullMQ system
export const initializeBullMQSystem = async () => {
  
  try {
    // Khởi động workers
    await _startAllWorkers();
    
    // Khởi động health monitoring
    const healthInterval = _startPeriodicHealthCheck(_productSyncQueue);
    
    
    return {
      success: true,
      healthInterval
    };
  } catch (error: any) {
    console.error('❌ Lỗi khi khởi tạo BullMQ System:', error);
    throw error;
  }
};

// Utility function để tắt toàn bộ BullMQ system
export const shutdownBullMQSystem = async () => {
  
  try {
    // Dừng workers
    await _stopAllWorkers();
    
    // Đóng queues
    await _closeAllQueues();
    
  } catch (error: any) {
    console.error('❌ Lỗi khi tắt BullMQ System:', error);
    throw error;
  }
};

// Export default với các utility functions
export default {
  initializeBullMQSystem,
  shutdownBullMQSystem,
  QUEUE_NAMES: _QUEUE_NAMES,
  productSyncQueue: _productSyncQueue,
  addHaravanSyncJob: _addHaravanSyncJob,
  addSapoSyncJob: _addSapoSyncJob,
  startAllWorkers: _startAllWorkers,
  stopAllWorkers: _stopAllWorkers,
  performHealthCheck: _performHealthCheck
}; 