/**
 * Health monitoring và diagnostics cho BullMQ
 */
import { Queue, Worker } from 'bullmq';
import { healthCheckConfig, loggingConfig } from './config';
import { getWorkersHealthStatus } from './workers';
import { getQueuesStats } from './queues';

// Interface cho health status
interface HealthStatus {
  status: 'healthy' | 'warning' | 'critical';
  timestamp: Date;
  details: {
    redis: {
      connected: boolean;
      ping?: number;
      error?: string;
    };
    queues: Record<string, any>;
    workers: Record<string, any>;
    memory: {
      used: number;
      total: number;
      percentage: number;
    };
  };
  metrics: {
    totalJobs: number;
    completedJobs: number;
    failedJobs: number;
    activeJobs: number;
    waitingJobs: number;
  };
}

// Logging utility
const log = (level: 'info' | 'warn' | 'error', message: string, data?: any) => {
  const timestamp = new Date().toISOString();
  const logData = data && loggingConfig.logJobData ? JSON.stringify(data) : '';
  
  switch (level) {
    case 'info':
      if (loggingConfig.logLevel === 'info' && !loggingConfig.healthCheckSilent) {
      }
      break;
    case 'warn':
      console.warn(`[${timestamp}] ⚠️ ${message}`, logData);
      break;
    case 'error':
      console.error(`[${timestamp}] 🚨 ${message}`, logData);
      break;
  }
};

/**
 * Kiểm tra kết nối Redis
 */
const checkRedisConnection = async (queue: Queue): Promise<{ connected: boolean; ping?: number; error?: string }> => {
  try {
    const startTime = Date.now();
    const client = await queue.client;
    await client.ping();
    const ping = Date.now() - startTime;
    
    return {
      connected: true,
      ping
    };
  } catch (error: any) {
    return {
      connected: false,
      error: error.message
    };
  }
};

/**
 * Lấy memory usage
 */
const getMemoryUsage = () => {
  const usage = process.memoryUsage();
  const total = usage.heapTotal;
  const used = usage.heapUsed;
  
  return {
    used,
    total,
    percentage: Math.round((used / total) * 100)
  };
};

/**
 * Tính toán tổng metrics từ tất cả queues
 */
const calculateTotalMetrics = (queuesStats: Record<string, any>) => {
  let totalJobs = 0;
  let completedJobs = 0;
  let failedJobs = 0;
  let activeJobs = 0;
  let waitingJobs = 0;

  for (const queueName in queuesStats) {
    const stats = queuesStats[queueName];
    if (stats.jobCounts) {
      totalJobs += Object.values(stats.jobCounts).reduce((sum: number, count: any) => sum + (count || 0), 0);
      completedJobs += stats.jobCounts.completed || 0;
      failedJobs += stats.jobCounts.failed || 0;
      activeJobs += stats.jobCounts.active || 0;
      waitingJobs += stats.jobCounts.waiting || 0;
    }
  }

  return {
    totalJobs,
    completedJobs,
    failedJobs,
    activeJobs,
    waitingJobs
  };
};

/**
 * Xác định overall health status
 */
const determineHealthStatus = (
  redisStatus: any,
  workersStatus: any,
  memoryUsage: any,
  metrics: any
): 'healthy' | 'warning' | 'critical' => {
  // Critical conditions
  if (!redisStatus.connected) {
    return 'critical';
  }

  const hasUnhealthyWorkers = Object.values(workersStatus).some((worker: any) => !worker.isHealthy);
  if (hasUnhealthyWorkers) {
    return 'critical';
  }

  // Warning conditions
  if (memoryUsage.percentage > 85) {
    return 'warning';
  }

  if (redisStatus.ping && redisStatus.ping > 1000) {
    return 'warning';
  }

  const failureRate = metrics.totalJobs > 0 ? (metrics.failedJobs / metrics.totalJobs) : 0;
  if (failureRate > 0.1) { // Hơn 10% job thất bại
    return 'warning';
  }

  return 'healthy';
};

/**
 * Thực hiện health check tổng quát
 */
export const performHealthCheck = async (sampleQueue?: Queue): Promise<HealthStatus> => {
  try {
    // Chỉ log bắt đầu nếu không phải silent mode
    if (!loggingConfig.healthCheckSilent) {
      log('info', 'Bắt đầu health check...');
    }

    // Lấy stats từ workers và queues
    const [workersStatus, queuesStats] = await Promise.all([
      getWorkersHealthStatus(),
      getQueuesStats()
    ]);

    // Kiểm tra Redis connection (sử dụng queue bất kỳ)
    let redisStatus: { connected: boolean; ping?: number; error?: string } = { connected: false, error: 'No queue available for testing' };
    if (sampleQueue) {
      redisStatus = await checkRedisConnection(sampleQueue);
    }

    // Lấy memory usage
    const memoryUsage = getMemoryUsage();

    // Tính metrics
    const metrics = calculateTotalMetrics(queuesStats);

    // Xác định health status
    const status = determineHealthStatus(redisStatus, workersStatus, memoryUsage, metrics);

    const healthStatus: HealthStatus = {
      status,
      timestamp: new Date(),
      details: {
        redis: redisStatus,
        queues: queuesStats,
        workers: workersStatus,
        memory: memoryUsage
      },
      metrics
    };

    // Log kết quả
    // if (status === 'healthy') {
    //   // Chỉ log healthy status nếu không phải silent mode
    //   if (!loggingConfig.healthCheckSilent) {
    //     log('info', `✅ Health check: ${status}`);
    //   }
    // } else if (status === 'warning') {
    //   log('warn', `⚠️ Health check: ${status}`, healthStatus.details);
    // } else {
    //   log('error', `🚨 Health check: ${status}`, healthStatus.details);
    // }

    return healthStatus;

  } catch (error: any) {
    log('error', 'Lỗi khi thực hiện health check:', error);
    
    return {
      status: 'critical',
      timestamp: new Date(),
      details: {
        redis: { connected: false, error: error.message },
        queues: {},
        workers: {},
        memory: getMemoryUsage()
      },
      metrics: {
        totalJobs: 0,
        completedJobs: 0,
        failedJobs: 0,
        activeJobs: 0,
        waitingJobs: 0
      }
    };
  }
};

/**
 * Tạo health check endpoint cho Express
 */
export const createHealthCheckEndpoint = (sampleQueue?: Queue) => {
  return async (req: any, res: any) => {
    try {
      const healthStatus = await performHealthCheck(sampleQueue);
      
      const httpStatus = healthStatus.status === 'healthy' ? 200 : 
                        healthStatus.status === 'warning' ? 200 : 503;
      
      res.status(httpStatus).json(healthStatus);
    } catch (error: any) {
      res.status(503).json({
        status: 'critical',
        timestamp: new Date(),
        error: error.message
      });
    }
  };
};

/**
 * Tạo metrics endpoint cho Prometheus/monitoring
 */
export const createMetricsEndpoint = () => {
  return async (req: any, res: any) => {
    try {
      const [workersStatus, queuesStats] = await Promise.all([
        getWorkersHealthStatus(),
        getQueuesStats()
      ]);

      const metrics = calculateTotalMetrics(queuesStats);
      const memoryUsage = getMemoryUsage();

      // Format Prometheus style metrics
      const prometheusMetrics = `
# HELP bullmq_jobs_total Total number of jobs
# TYPE bullmq_jobs_total counter
bullmq_jobs_total ${metrics.totalJobs}

# HELP bullmq_jobs_completed_total Total number of completed jobs
# TYPE bullmq_jobs_completed_total counter
bullmq_jobs_completed_total ${metrics.completedJobs}

# HELP bullmq_jobs_failed_total Total number of failed jobs
# TYPE bullmq_jobs_failed_total counter
bullmq_jobs_failed_total ${metrics.failedJobs}

# HELP bullmq_jobs_active Current number of active jobs
# TYPE bullmq_jobs_active gauge
bullmq_jobs_active ${metrics.activeJobs}

# HELP bullmq_jobs_waiting Current number of waiting jobs
# TYPE bullmq_jobs_waiting gauge
bullmq_jobs_waiting ${metrics.waitingJobs}

# HELP bullmq_memory_usage_bytes Memory usage in bytes
# TYPE bullmq_memory_usage_bytes gauge
bullmq_memory_usage_bytes ${memoryUsage.used}

# HELP bullmq_memory_usage_percentage Memory usage percentage
# TYPE bullmq_memory_usage_percentage gauge
bullmq_memory_usage_percentage ${memoryUsage.percentage}

# HELP bullmq_workers_healthy Number of healthy workers
# TYPE bullmq_workers_healthy gauge
bullmq_workers_healthy ${Object.values(workersStatus).filter((w: any) => w.isHealthy).length}

# HELP bullmq_workers_total Total number of workers
# TYPE bullmq_workers_total gauge
bullmq_workers_total ${Object.keys(workersStatus).length}
`.trim();

      res.set('Content-Type', 'text/plain');
      res.send(prometheusMetrics);
    } catch (error: any) {
      res.status(500).send(`# Error: ${error.message}`);
    }
  };
};

/**
 * Khởi tạo periodic health checking
 */
export const startPeriodicHealthCheck = (sampleQueue?: Queue) => {
  const interval = setInterval(async () => {
    try {
      await performHealthCheck(sampleQueue);
    } catch (error) {
      log('error', 'Lỗi periodic health check:', error);
    }
  }, healthCheckConfig.checkInterval);

  // Chỉ log thông báo bắt đầu nếu không phải silent mode
  if (!loggingConfig.healthCheckSilent) {
    log('info', `🏥 Bắt đầu periodic health check mỗi ${healthCheckConfig.checkInterval}ms`);
  }

  return interval;
};

export default {
  performHealthCheck,
  createHealthCheckEndpoint,
  createMetricsEndpoint,
  startPeriodicHealthCheck
}; 