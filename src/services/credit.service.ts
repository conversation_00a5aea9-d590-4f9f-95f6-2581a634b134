import { supabaseAdmin } from '../config/supabase';

/**
 * Kiểm tra số dư credit của tenant
 * @param tenant_id ID của tenant
 * @returns Thông tin về số dư credit và trạng thái kiểm tra
 */
export const checkTenantCredit = async (tenant_id: string) => {
  try {
    // Kiểm tra xem tenant_id có phải là UUID hợp lệ không
    const isValidUUID = tenant_id && typeof tenant_id === 'string' && tenant_id.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i);

    if (!isValidUUID) {
      return {
        success: false,
        message: 'Tenant ID không hợp lệ',
        hasCredit: false
      };
    }

    // Sử dụng stored procedure để kiểm tra credit với supabaseAdmin để bypass RLS
    const { data, error } = await supabaseAdmin.rpc('check_tenant_credit', {
      p_tenant_id: tenant_id
    });

    if (error) {
      return {
        success: false,
        message: 'Không thể kiểm tra số dư credit',
        error: error.message,
        hasCredit: false
      };
    }

    if (!data) {
      console.error('Không có dữ liệu trả về từ stored procedure');
      return {
        success: false,
        message: 'Không tìm thấy thông tin credit của tenant',
        hasCredit: false
      };
    }

    // Trả về kết quả từ stored procedure
    return {
      ...data,
      balance: data.balance ? parseFloat(data.balance) : 0
    };
  } catch (error: any) {
    console.error('Lỗi khi kiểm tra số dư credit:', error);
    return {
      success: false,
      message: `Lỗi khi kiểm tra số dư credit: ${error?.message || 'Lỗi không xác định'}`,
      hasCredit: false,
    };
  }
};

/**
 * Trừ credit của tenant sau khi sử dụng dịch vụ
 * @param tenant_id ID của tenant
 * @param creditAmount Số lượng credit cần trừ
 * @param userId ID của người dùng thực hiện hành động (nếu có)
 * @param referenceType Loại tham chiếu (ví dụ: 'agent_response')
 * @param description Mô tả giao dịch
 * @returns Kết quả của việc trừ credit
 */
export const deductTenantCredit = async ({
  tenant_id,
  creditAmount,
  userId,
  referenceType = 'agent_response',
  description = 'Sử dụng chatbot AI',
}: {
  tenant_id: string;
  creditAmount: number;
  userId?: string;
  referenceType?: string;
  description?: string;
}) => {
  try {
    // Kiểm tra số dư hiện tại
    const creditCheck = await checkTenantCredit(tenant_id);

    if (!creditCheck.success || !creditCheck.hasCredit) {
      return {
        success: false,
        message: 'Không đủ credit để thực hiện hành động này',
        ...creditCheck,
      };
    }


    // Chỉ thêm created_by nếu userId tồn tại và là UUID hợp lệ
    const isValidUUID = userId && typeof userId === 'string' && userId.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i);


    // Thực hiện giao dịch trong một transaction để đảm bảo tính nhất quán
    // Sử dụng supabaseAdmin để bypass RLS
    const { data, error } = await supabaseAdmin.rpc('deduct_tenant_credit', {
      p_tenant_id: tenant_id,
      p_amount: creditAmount,
      p_description: description,
      p_reference_type: referenceType,
      p_created_by: isValidUUID ? userId : null
    });

    if (error) {
      console.error('Lỗi khi trừ credit:', error);
      return {
        success: false,
        message: `Lỗi khi trừ credit: ${error.message}`,
      };
    }

    if (!data) {
      console.error('Không có dữ liệu trả về từ stored procedure deduct_tenant_credit');
      return {
        success: false,
        message: 'Không thể trừ credit: Không có dữ liệu trả về',
      };
    }


    // Trả về kết quả từ stored procedure
    return {
      success: data.success,
      message: data.message,
      newBalance: data.new_balance ? parseFloat(data.new_balance) : 0,
      transactionId: data.transaction_id,
      amountDeducted: data.amount_deducted ? parseFloat(data.amount_deducted) : 0
    };
  } catch (error: any) {
    console.error('Lỗi khi trừ credit:', error);
    return {
      success: false,
      message: `Lỗi khi trừ credit: ${error?.message || 'Lỗi không xác định'}`,
    };
  }
};
