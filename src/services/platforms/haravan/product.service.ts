import axios from 'axios';

/**
 * Service để lấy dữ liệu sản phẩm từ Haravan
 */
export const getProducts = async (
  token: string,
  params: {
    limit?: number;
    page?: number;
    updated_at_min?: string;
    updated_at_max?: string;
    created_at_min?: string;
    created_at_max?: string;
    status?: 'active' | 'archived' | 'draft';
    ids?: string;
  } = {}
) => {
  try {
    const response = await axios.get('https://apis.haravan.com/com/products.json', {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      params
    });

    return {
      success: true,
      data: response.data.products,
      total: response.data.products?.length || 0,
      platform: 'haravan'
    };
  } catch (error: any) {
    console.error('Lỗi khi lấy danh sách sản phẩm từ Haravan:', error.response?.data || error.message);
    return {
      success: false,
      message: error.response?.data?.error || error.message,
      status: error.response?.status,
      platform: 'haravan'
    };
  }
};

/**
 * L<PERSON><PERSON> số lượng sản phẩm từ Haravan
 */
export const getProductCount = async (token: string, params: any = {}) => {
  try {
    const response = await axios.get('https://apis.haravan.com/com/products/count.json', {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      params
    });

    return {
      success: true,
      count: response.data.count,
      platform: 'haravan'
    };
  } catch (error: any) {
    console.error('Lỗi khi lấy số lượng sản phẩm từ Haravan:', error.response?.data || error.message);
    return {
      success: false,
      message: error.response?.data?.error || error.message,
      status: error.response?.status,
      platform: 'haravan'
    };
  }
};

export default {
  getProducts,
  getProductCount
};
