import axios from 'axios';

/**
 * Trích xuất thông tin xác thực từ URL Sapo
 * Format: https://[api_key]:[password]@[domain]/admin/...
 * @param sapoUrl URL đầy đủ của Sapo
 * @returns Thông tin xác thực và base URL
 */
export const extractSapoCredentials = (sapoUrl: string) => {
  try {
    // Kiểm tra định dạng URL
    if (!sapoUrl.includes('@') || !sapoUrl.includes('/admin')) {
      throw new Error('URL Sapo không đúng định dạng. Yêu cầu: https://[api_key]:[password]@[domain]/admin/...');
    }

    // Trích xuất phần trước "/admin"
    const baseUrlMatch = sapoUrl.match(/(https?:\/\/[^\/]+)/);
    if (!baseUrlMatch) {
      throw new Error('<PERSON>hông thể trích xuất base URL từ URL Sapo');
    }

    const baseUrl = baseUrlMatch[1];
    return { baseUrl };
  } catch (error: any) {
    console.error('Lỗi khi trích xuất thông tin xác thực từ URL Sapo:', error.message);
    throw error;
  }
};

/**
 * Service để lấy dữ liệu sản phẩm từ Sapo
 */
export const getProducts = async (
  sapoUrl: string,
  params: {
    limit?: number;
    page?: number;
    updated_at_min?: string;
    updated_at_max?: string;
    created_at_min?: string;
    created_at_max?: string;
    ids?: string;
    vendor?: string;
    handle?: string;
    product_type?: string;
    collection_id?: string;
    sku?: string;
    barcode?: string;
    fields?: string;
  } = {}
) => {
  try {
    // Đảm bảo URL kết thúc bằng "/admin"
    const baseUrl = sapoUrl.endsWith('/admin')
      ? sapoUrl
      : sapoUrl.substring(0, sapoUrl.indexOf('/admin') + 7);

    const response = await axios.get(`${baseUrl}/products.json`, {
      headers: {
        'Content-Type': 'application/json'
      },
      params
    });

    return {
      success: true,
      data: response.data.products,
      total: response.data.products?.length || 0,
      platform: 'sapo'
    };
  } catch (error: any) {
    console.error('Lỗi khi lấy danh sách sản phẩm từ Sapo:', error.response?.data || error.message);
    return {
      success: false,
      message: error.response?.data?.error || error.message,
      status: error.response?.status,
      platform: 'sapo'
    };
  }
};

/**
 * Lấy số lượng sản phẩm từ Sapo
 */
export const getProductCount = async (sapoUrl: string, params: any = {}) => {
  try {
    // Đảm bảo URL kết thúc bằng "/admin"
    const baseUrl = sapoUrl.endsWith('/admin')
      ? sapoUrl
      : sapoUrl.substring(0, sapoUrl.indexOf('/admin') + 7);

    const response = await axios.get(`${baseUrl}/products/count.json`, {
      headers: {
        'Content-Type': 'application/json'
      },
      params
    });

    return {
      success: true,
      count: response.data.count,
      platform: 'sapo'
    };
  } catch (error: any) {
    console.error('Lỗi khi lấy số lượng sản phẩm từ Sapo:', error.response?.data || error.message);
    return {
      success: false,
      message: error.response?.data?.error || error.message,
      status: error.response?.status,
      platform: 'sapo'
    };
  }
};

export default {
  getProducts,
  getProductCount
};
