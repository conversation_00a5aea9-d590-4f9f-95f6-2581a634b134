import { client } from "../../config/weaviate";
import weaviate, { vectorizer, dataType, configure } from "weaviate-client";

/**
 * Khởi tạo collection "Faqs" trong Weaviate
 * Collection này sẽ lưu trữ các câu hỏi thường gặp với các trường "topic" và "content"
 */
export const initFaqsCollection = async () => {
  try {
    // Kiểm tra xem collection đã tồn tại chưa
    const collections = await client.collections.get("Faqs");
    const faqsExists = await collections.exists();

    // Nếu collection đã tồn tại, không cần tạo lại
    if (faqsExists) {
      // return { success: true, message: 'Collection "Faqs" đã tồn tại' };
      await client.collections.delete("Faqs");
    }

    // Tạo collection mới
    await client.collections.create({
      name: "Faqs",
      multiTenancy: weaviate.configure.multiTenancy({ enabled: true }),
      vectorizers: vectorizer.text2VecTransformers({
        sourceProperties: ["topic", "content"],
        vectorIndexConfig: configure.vectorIndex.hnsw(),
        // model: "text-embedding-3-small",
      }),
      properties: [
        { name: "topic", dataType: dataType.TEXT },
        { name: "content", dataType: dataType.TEXT },
        { name: "bot_id", dataType: dataType.TEXT },
        { name: "tenant_id", dataType: dataType.TEXT },
        { name: "supabase_id", dataType: dataType.TEXT },
      ],
    });

    return { success: true, message: 'Đã tạo collection "Faqs" thành công' };
  } catch (error: any) {
    console.error('Lỗi khi tạo collection "Faqs":', error);
    return {
      success: false,
      message: `Lỗi khi tạo collection "Faqs": ${error?.message || "Lỗi không xác định"}`,
    };
  }
};

/**
 * Thêm một FAQ mới vào collection
 */
export const addFaq = async ({
  topic,
  content,
  bot_id,
  tenant_id,
  supabase_id,
}: {
  topic: string;
  content: string;
  bot_id: string;
  tenant_id: string;
  supabase_id: string;
}) => {
  try {
    const faqsCollection = await client.collections.get("Faqs");

    // Kiểm tra và tạo tenant nếu chưa tồn tại
    try {
      const existingTenants = await faqsCollection.tenants.get();
      
      // Xử lý cả trường hợp existingTenants là array hoặc object
      let tenantExists = false;
      if (Array.isArray(existingTenants)) {
        tenantExists = existingTenants.some((tenant: any) => tenant.name === tenant_id);
      } else if (existingTenants && typeof existingTenants === 'object') {
        // Nếu existingTenants là object, kiểm tra key hoặc values
        tenantExists = tenant_id in existingTenants || 
                      Object.values(existingTenants).some((tenant: any) => tenant.name === tenant_id);
      }

      if (!tenantExists) {
        await faqsCollection.tenants.create([{ name: tenant_id }]);
      } else {
      }
    } catch (error: any) {
      console.error("Lỗi khi kiểm tra tenant:", error);
      // Thử tạo tenant trực tiếp
      try {
        await faqsCollection.tenants.create([{ name: tenant_id }]);
      } catch (createError: any) {
        console.error("Lỗi khi tạo tenant:", createError);
        // Tiếp tục thử thêm FAQ dù tenant có thể đã tồn tại
      }
    }

    const tenantCollection = faqsCollection.withTenant(tenant_id);

    // Thêm FAQ mới
    const result = await tenantCollection.data.insert({
      properties: {
        topic,
        content,
        bot_id,
        tenant_id,
        supabase_id,
      },
    });

    return { success: true, data: result };
  } catch (error: any) {
    console.error("Lỗi khi thêm FAQ:", error);
    return {
      success: false,
      message: `Lỗi khi thêm FAQ: ${error?.message || "Lỗi không xác định"}`,
    };
  }
};

/**
 * Thêm nhiều FAQ cùng lúc vào collection
 * @param faqs Mảng các đối tượng FAQ cần thêm
 */
export const addFaqs = async (
  faqs: Array<{
    topic: string;
    content: string;
    bot_id: string;
    tenant_id: string;
    supabase_id: string;
  }>
) => {
  try {
    if (!faqs || faqs.length === 0) {
      return { success: false, message: "Danh sách FAQ không được để trống" };
    }

    const faqsCollection = await client.collections.get("Faqs");
    const tenant_id = faqs[0].tenant_id;

    // Kiểm tra và tạo tenant nếu chưa tồn tại
    try {
      const existingTenants = await faqsCollection.tenants.get();
      
      // Xử lý cả trường hợp existingTenants là array hoặc object
      let tenantExists = false;
      if (Array.isArray(existingTenants)) {
        tenantExists = existingTenants.some((tenant: any) => tenant.name === tenant_id);
      } else if (existingTenants && typeof existingTenants === 'object') {
        // Nếu existingTenants là object, kiểm tra key hoặc values
        tenantExists = tenant_id in existingTenants || 
                      Object.values(existingTenants).some((tenant: any) => tenant.name === tenant_id);
      }

      if (!tenantExists) {
        await faqsCollection.tenants.create([{ name: tenant_id }]);
      } else {
      }
    } catch (error: any) {
      console.error("Lỗi khi kiểm tra tenant:", error);
      // Thử tạo tenant trực tiếp
      try {
        await faqsCollection.tenants.create([{ name: tenant_id }]);
      } catch (createError: any) {
        console.error("Lỗi khi tạo tenant:", createError);
        return {
          success: false,
          message: `Lỗi khi tạo tenant ${tenant_id}: ${createError?.message || "Lỗi không xác định"}`,
        };
      }
    }

    const tenantCollection = faqsCollection.withTenant(tenant_id);

    // Tối ưu: chuyển đổi dữ liệu để phù hợp với insertMany
    const faqData = faqs.map(faq => ({
      properties: {
        topic: faq.topic,
        content: faq.content,
        bot_id: faq.bot_id,
        tenant_id: faq.tenant_id,
        supabase_id: faq.supabase_id,
      }
    }));

    const result = await tenantCollection.data.insertMany(faqData);

    return { success: true, data: result };
  } catch (error: any) {
    console.error("Lỗi khi thêm nhiều FAQ:", error);
    return {
      success: false,
      message: `Lỗi khi thêm nhiều FAQ: ${error?.message || "Lỗi không xác định"}`,
    };
  }
};

/**
 * Tìm kiếm FAQ dựa trên nội dung - Tối ưu hiệu suất
 */
export const searchFaqs = async (query: string, tenant_id: string, limit: number = 5, bot_id?: string) => {
  try {
    if (!query?.trim()) {
      return { success: false, message: "Query không được để trống" };
    }

    const faqsCollection = await client.collections.get("Faqs");
    const tenant = faqsCollection.withTenant(tenant_id);

    // Tối ưu: chỉ tạo filter khi cần thiết
    const searchOptions: any = {
      limit: Math.min(limit, 20), // Giới hạn tối đa để tối ưu hiệu suất
      returnMetadata: ["score"], // Chỉ lấy metadata cần thiết
    };

    if (bot_id) {
      searchOptions.filters = tenant.filter.byProperty('bot_id').equal(bot_id);
    }

    // Sử dụng hybrid search để có kết quả tốt nhất
    const result = await tenant.query.hybrid(query, searchOptions);

    return { success: true, data: result };
  } catch (error: any) {
    console.error("Lỗi khi tìm kiếm FAQ:", error);
    return {
      success: false,
      message: `Lỗi khi tìm kiếm FAQ: ${error?.message || "Lỗi không xác định"}`,
    };
  }
};

/**
 * Lấy ID của FAQ dựa trên supabase_id và tenant_id
 * @param supabase_id ID của FAQ trong Supabase
 * @param tenant_id ID của tenant
 * @returns ID của FAQ hoặc null nếu không tìm thấy
 */
export const getFaqIdBySupabaseId = async ({
  supabase_id,
  tenant_id,
}: {
  supabase_id: string;
  tenant_id: string;
}) => {
  try {
    // Lấy collection Faqs
    const faqsCollection = await client.collections.get("Faqs");
    const tenant = faqsCollection.withTenant(tenant_id);

    // Tìm kiếm FAQ theo supabase_id
    const result = await tenant.query.fetchObjects({
      filters: tenant.filter.byProperty("supabase_id").equal(supabase_id),
      limit: 1,
    });

    // Kiểm tra kết quả
    if (result.objects && result.objects.length > 0 && result.objects[0].uuid) {
      return {
        success: true,
        id: result.objects[0].uuid,
        data: result.objects[0],
      };
    } else {
      return { success: false, message: "Không tìm thấy FAQ" };
    }
  } catch (error: any) {
    return {
      success: false,
      message: `Lỗi khi tìm kiếm FAQ: ${error?.message || "Lỗi không xác định"}`,
    };
  }
};

/**
 * Cập nhật thông tin FAQ theo supabase_id
 * @param supabase_id ID của FAQ trong Supabase cần cập nhật
 * @param tenant_id ID của tenant
 * @param updateData Dữ liệu cập nhật cho FAQ
 */
export const updateFaqBySupabaseId = async ({
  supabase_id,
  tenant_id,
  updateData,
}: {
  supabase_id: string;
  tenant_id: string;
  updateData: {
    topic?: string;
    content?: string;
    bot_id?: string;
  };
}) => {
  try {
    // Lấy collection Faqs
    const faqsCollection = await client.collections.get("Faqs");
    const tenant = faqsCollection.withTenant(tenant_id);

    // Tạo bộ lọc để tìm FAQ theo supabase_id
    const filterQuery = tenant.filter
      .byProperty("supabase_id")
      .equal(supabase_id);

    // Tìm kiếm các FAQ phù hợp để lấy ID
    const searchResult = await tenant.query.fetchObjects({
      filters: filterQuery,
    });

    // Kiểm tra xem có FAQ nào được tìm thấy không
    if (!searchResult.objects || searchResult.objects.length === 0) {
      return {
        success: false,
        message: `Không tìm thấy FAQ với supabase_id: ${supabase_id}`,
      };
    }

    // Tối ưu: sử dụng batch update thay vì update từng item
    const validObjects = searchResult.objects.filter((obj: any) => obj.uuid);

    if (validObjects.length === 0) {
      return {
        success: false,
        message: `Không tìm thấy FAQ hợp lệ với supabase_id: ${supabase_id}`,
      };
    }

    // Cập nhật batch cho hiệu suất tốt hơn
    const updatePromises = validObjects.map((obj: any) =>
      tenant.data.update({
        id: obj.uuid,
        properties: updateData,
      })
    );

    const updateResults = await Promise.all(updatePromises);

    if (updateResults.length > 0) {
      return {
        success: true,
        data: updateResults,
        message: `Đã cập nhật thông tin cho ${updateResults.length} FAQ với supabase_id: ${supabase_id}`,
      };
    } else {
      return {
        success: false,
        message: `Không thể cập nhật thông tin cho FAQ với supabase_id: ${supabase_id}`,
      };
    }
  } catch (error: any) {
    return {
      success: false,
      message: `Lỗi khi cập nhật thông tin FAQ: ${error?.message || "Lỗi không xác định"}`,
    };
  }
};

/**
 * Xóa FAQ theo supabase_id
 * @param supabase_id ID của FAQ trong Supabase cần xóa
 * @param tenant_id ID của tenant
 */
export const deleteFaqBySupabaseId = async ({
  supabase_id,
  tenant_id,
}: {
  supabase_id: string;
  tenant_id: string;
}) => {
  try {
    // Lấy collection Faqs
    const faqsCollection = await client.collections.get("Faqs");
    const tenant = faqsCollection.withTenant(tenant_id);

    // Tạo bộ lọc để tìm FAQ theo supabase_id
    const filterQuery = tenant.filter
      .byProperty("supabase_id")
      .equal(supabase_id);

    // Xóa FAQ theo supabase_id
    const response = await tenant.data.deleteMany(filterQuery);
    
    // Kiểm tra xem có FAQ nào bị xóa không
    if (response && response.successful > 0) {
      return { success: true, data: response };
    } else {
      return {
        success: false,
        message: `Không tìm thấy FAQ với supabase_id: ${supabase_id}`,
      };
    }
  } catch (error: any) {
    return {
      success: false,
      message: `Lỗi khi xóa FAQ theo supabase_id: ${error?.message || "Lỗi không xác định"}`,
    };
  }
};

/**
 * Xóa nhiều FAQ theo danh sách supabase_id
 * @param supabase_ids Mảng các ID FAQ trong Supabase cần xóa
 * @param tenant_id ID của tenant
 */
export const deleteFaqsBySupabaseIds = async ({
  supabase_ids,
  tenant_id,
}: {
  supabase_ids: string[];
  tenant_id: string;
}) => {
  try {
    if (!supabase_ids || supabase_ids.length === 0) {
      return {
        success: false,
        message: "Danh sách supabase_id không được để trống",
      };
    }

    // Lấy collection Faqs
    const faqsCollection = await client.collections.get("Faqs");
    const tenant = faqsCollection.withTenant(tenant_id);

    // Tối ưu hiệu suất: sử dụng containsAny thay vì OR nhiều filter
    const combinedFilter = tenant.filter.byProperty("supabase_id").containsAny(supabase_ids);

    // Xóa FAQ theo danh sách supabase_id
    const response = await tenant.data.deleteMany(combinedFilter);
    
    // Kiểm tra kết quả
    if (response && response.successful > 0) {
      return { 
        success: true, 
        data: response,
        message: `Đã xóa thành công ${response.successful} FAQ từ ${supabase_ids.length} FAQ được yêu cầu`
      };
    } else {
      return {
        success: false,
        message: `Không tìm thấy FAQ nào với danh sách supabase_id được cung cấp`,
        data: response
      };
    }
  } catch (error: any) {
    return {
      success: false,
      message: `Lỗi khi xóa nhiều FAQ: ${error?.message || "Lỗi không xác định"}`,
    };
  }
};
