import { supabaseAdmin } from '../../config/supabase';
import * as pgCustomerService from '../postgres/customer.service';

/**
 * Tìm kiếm khách hàng theo số điện thoại sử dụng Supabase Admin
 * @param phone Số điện thoại của khách hàng
 * @param tenant_id ID của tenant
 */
export const findCustomerByPhone = async ({
  phone,
  tenant_id,
}: {
  phone: string;
  tenant_id: string;
}) => {
  try {

    const { data: customers, error } = await supabaseAdmin
      .from('customers')
      .select('*')
      .eq('phone', phone)
      .eq('tenant_id', tenant_id)
      .limit(1);

    if (error) {
      console.error('Lỗi khi tìm kiếm khách hàng:', error);
      return {
        success: false,
        message: `Lỗi khi tìm kiếm khách hàng: ${error.message}`,
      };
    }

    if (!customers || customers.length === 0) {
      return {
        success: false,
        message: 'Không tìm thấy khách hàng',
      };
    }

    return {
      success: true,
      data: customers[0],
    };
  } catch (error: any) {
    console.error('Lỗi khi tìm kiếm khách hàng:', error);
    return {
      success: false,
      message: `Lỗi khi tìm kiếm khách hàng: ${error?.message || 'Lỗi không xác định'}`,
    };
  }
};

/**
 * Tạo khách hàng mới sử dụng Supabase Admin
 * @param customerData Thông tin khách hàng
 * @param tenant_id ID của tenant
 */
export const createCustomer = async ({
  customerData,
  tenant_id,
}: {
  customerData: {
    full_name: string;
    phone: string;
    email?: string | null;
    notes?: string;
  };
  tenant_id: string;
}) => {
  try {

    const { data: newCustomer, error } = await supabaseAdmin
      .from('customers')
      .insert({
        tenant_id,
        full_name: customerData.full_name,
        phone: customerData.phone,
        email: customerData.email || null,
        notes: customerData.notes || null,
        total_spent: 0,
        orders_count: 0,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      console.error('Lỗi khi tạo khách hàng mới:', error);
      return {
        success: false,
        message: `Lỗi khi tạo khách hàng mới: ${error.message}`,
      };
    }

    return {
      success: true,
      data: newCustomer,
    };
  } catch (error: any) {
    console.error('Lỗi khi tạo khách hàng mới:', error);
    return {
      success: false,
      message: `Lỗi khi tạo khách hàng mới: ${error?.message || 'Lỗi không xác định'}`,
    };
  }
};

/**
 * Tìm kiếm khách hàng theo số điện thoại hoặc tạo mới nếu không tồn tại sử dụng Supabase Admin
 * @param customerData Thông tin khách hàng
 * @param tenant_id ID của tenant
 */
export const findOrCreateCustomer = async ({
  customerData,
  tenant_id,
}: {
  customerData: {
    full_name: string;
    phone: string;
    email?: string | null;
    notes?: string;
  };
  tenant_id: string;
}) => {
  try {

    // Tìm kiếm khách hàng theo số điện thoại
    const existingCustomer = await findCustomerByPhone({
      phone: customerData.phone,
      tenant_id,
    });

    // Nếu tìm thấy khách hàng, trả về thông tin khách hàng
    if (existingCustomer.success) {
      return existingCustomer;
    }

    // Nếu không tìm thấy, tạo khách hàng mới
    return createCustomer({
      customerData,
      tenant_id,
    });
  } catch (error: any) {
    console.error('Lỗi khi tìm kiếm hoặc tạo khách hàng:', error);
    return {
      success: false,
      message: `Lỗi khi tìm kiếm hoặc tạo khách hàng: ${error?.message || 'Lỗi không xác định'}`,
    };
  }
};

/**
 * Lấy danh sách địa chỉ của khách hàng
 * @param customer_id ID của khách hàng
 * @param tenant_id ID của tenant
 */
export const getCustomerAddresses = async ({
  customer_id,
  tenant_id,
}: {
  customer_id: string;
  tenant_id: string;
}) => {
  // Sử dụng kết nối PostgreSQL trực tiếp thay vì Supabase
  return pgCustomerService.getCustomerAddresses({
    customer_id,
    tenant_id
  });
};

/**
 * Lấy thông tin chi tiết của một địa chỉ
 * @param address_id ID của địa chỉ
 * @param tenant_id ID của tenant
 */
export const getAddressDetails = async ({
  address_id,
  tenant_id,
}: {
  address_id: string;
  tenant_id: string;
}) => {
  // Sử dụng kết nối PostgreSQL trực tiếp thay vì Supabase
  return pgCustomerService.getAddressDetails({
    address_id,
    tenant_id
  });
};

/**
 * Tạo địa chỉ mới cho khách hàng
 * @param addressData Thông tin địa chỉ
 * @param tenant_id ID của tenant
 */
export const createCustomerAddress = async ({
  addressData,
  tenant_id,
}: {
  addressData: {
    customer_id: string;
    full_name: string;
    phone: string;
    address: string;
    province?: string;
    district?: string;
    ward?: string;
    country?: string;
    address_type?: 'shipping' | 'billing' | 'both';
    is_default_shipping?: boolean;
    is_default_billing?: boolean;
    notes?: string;
  };
  tenant_id: string;
}) => {
  // Sử dụng kết nối PostgreSQL trực tiếp thay vì Supabase
  return pgCustomerService.createCustomerAddress({
    addressData,
    tenant_id
  });
};

/**
 * Cập nhật thông tin địa chỉ
 * @param address_id ID của địa chỉ
 * @param addressData Thông tin địa chỉ cần cập nhật
 * @param tenant_id ID của tenant
 */
export const updateCustomerAddress = async ({
  address_id,
  addressData,
  tenant_id,
}: {
  address_id: string;
  addressData: {
    full_name?: string;
    phone?: string;
    address?: string;
    province?: string;
    district?: string;
    ward?: string;
    country?: string;
    address_type?: 'shipping' | 'billing' | 'both';
    is_default_shipping?: boolean;
    is_default_billing?: boolean;
    notes?: string;
  };
  tenant_id: string;
}) => {
  // Sử dụng kết nối PostgreSQL trực tiếp thay vì Supabase
  return pgCustomerService.updateCustomerAddress({
    address_id,
    addressData,
    tenant_id
  });
};

/**
 * Xóa địa chỉ
 * @param address_id ID của địa chỉ
 * @param tenant_id ID của tenant
 */
export const deleteCustomerAddress = async ({
  address_id,
  tenant_id,
}: {
  address_id: string;
  tenant_id: string;
}) => {
  // Sử dụng kết nối PostgreSQL trực tiếp thay vì Supabase
  return pgCustomerService.deleteCustomerAddress({
    address_id,
    tenant_id
  });
};
