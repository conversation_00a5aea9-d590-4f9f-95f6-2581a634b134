import { supabase, supabaseAdmin, isServiceKeyConfigured } from '../../config/supabase';
import axios from 'axios';
import { v4 as uuidv4 } from 'uuid';
import path from 'path';

/**
 * Upload hình ảnh lên Supabase storage với tenant-based access control
 * @param imageBuffer Buffer chứa dữ liệu hình ảnh
 * @param fileName Tên file để lưu
 * @param tenant_id ID của tenant (bắt buộc cho tenant isolation)
 * @param bucket Tên bucket trong Supabase storage (mặc định là 'products')
 * @param useAdmin Sử dụng admin client để bypass RLS (mặc định là false để tuân thủ RLS)
 * @returns Promise<string> URL của hình ảnh đã upload lên Supabase
 */
export const uploadImageToStorage = async (
  imageBuffer: Buffer,
  fileName: string,
  tenant_id: string,
  bucket: string = 'public', // Sử dụng bucket 'public' theo cấu hình hiện tại
  useAdmin: boolean = false
): Promise<string> => {
  try {
    // Tạo đường dẫn với tenant isolation: products/{tenant_id}/{fileName}
    const filePath = `products/${tenant_id}/${fileName}`;

    // Sử dụng admin client hoặc client thông thường
    // Ưu tiên sử dụng client thông thường để tuân thủ RLS policies
    const client = useAdmin && isServiceKeyConfigured() ? supabaseAdmin : supabase;


    // Kiểm tra xem bucket có tồn tại không
    const { data: buckets, error: bucketsError } = await client.storage.listBuckets();

    if (bucketsError) {
      throw new Error(`Không thể kiểm tra bucket: ${bucketsError.message}`);
    }

    // Tìm bucket trong danh sách
    const bucketExists = buckets.some(b => b.name === bucket);

    // Tạo bucket nếu không tồn tại (chỉ với admin client)
    if (!bucketExists) {
      if (!useAdmin || !isServiceKeyConfigured()) {
        throw new Error(`Bucket '${bucket}' không tồn tại và không có quyền tạo bucket mới`);
      }

      const { data: newBucket, error: createBucketError } = await client.storage.createBucket(bucket, {
        public: true
      });

      if (createBucketError) {
        throw new Error(`Không thể tạo bucket: ${createBucketError.message}`);
      }

    }

    const { data, error } = await client.storage
      .from(bucket)
      .upload(filePath, imageBuffer, {
        contentType: 'image/jpeg',
        cacheControl: '3600',
        upsert: true
      });

    if (error) {
      console.error(`[uploadImageToStorage] Upload error:`, error);

      // Nếu lỗi 403 và chưa sử dụng admin client, thử lại với admin client
      if (error.message.includes('403') && !useAdmin && isServiceKeyConfigured()) {
        return uploadImageToStorage(imageBuffer, fileName, tenant_id, bucket, true);
      }

      throw new Error(`Không thể upload file: ${error.message}`);
    }

    // Lấy URL public của file
    const { data: publicUrlData } = client.storage
      .from(bucket)
      .getPublicUrl(filePath);

    return publicUrlData.publicUrl;
  } catch (error: any) {
    console.error(`[uploadImageToStorage] Error:`, error);
    throw new Error(`Không thể upload hình ảnh lên Supabase storage: ${error.message || 'Lỗi không xác định'}`);
  }
};

/**
 * Tải hình ảnh từ URL và upload lên Supabase storage với tenant isolation
 * @param imageUrl URL của hình ảnh cần tải
 * @param tenant_id ID của tenant (bắt buộc cho tenant isolation)
 * @param bucket Tên bucket trong Supabase storage (mặc định là 'products')
 * @param useAdmin Sử dụng admin client để bypass RLS (mặc định là false)
 * @returns Promise<string> URL của hình ảnh đã upload lên Supabase
 */
export const downloadAndUploadImage = async (
  imageUrl: string,
  tenant_id: string,
  bucket: string = 'public', // Sử dụng bucket 'public' theo cấu hình hiện tại
  useAdmin: boolean = false
): Promise<string> => {
  try {
    // Kiểm tra nếu URL không hợp lệ
    if (!imageUrl || !imageUrl.trim()) {
      throw new Error('URL hình ảnh không hợp lệ');
    }

    // Kiểm tra nếu URL đã là từ Supabase thì không cần tải lại
    if (imageUrl.includes('supabase.co/storage')) {
      return imageUrl;
    }

    // Tải hình ảnh từ URL
    const response = await axios.get(imageUrl, {
      responseType: 'arraybuffer'
    });

    // Lấy extension của file từ URL
    const fileExtension = path.extname(imageUrl).toLowerCase() || '.jpg';

    // Tạo tên file ngẫu nhiên với timestamp để tránh trùng lặp
    const fileName = `img_${Date.now()}_${uuidv4().substring(0, 6)}${fileExtension}`;

    // Upload hình ảnh lên Supabase storage với tenant isolation
    const publicUrl = await uploadImageToStorage(
      Buffer.from(response.data),
      fileName,
      tenant_id,
      bucket,
      useAdmin
    );

    return publicUrl;
  } catch (error: any) {
    throw new Error(`Không thể tải và upload hình ảnh: ${error.message || 'Lỗi không xác định'}`);
  }
};

/**
 * Tải nhiều hình ảnh từ URL và upload lên Supabase storage với tenant isolation
 * @param imageUrls Mảng URL của các hình ảnh cần tải
 * @param tenant_id ID của tenant (bắt buộc cho tenant isolation)
 * @param bucket Tên bucket trong Supabase storage (mặc định là 'products')
 * @param useAdmin Sử dụng admin client để bypass RLS (mặc định là false)
 * @returns Promise<string[]> Mảng URL của các hình ảnh đã upload lên Supabase
 */
export const downloadAndUploadMultipleImages = async (
  imageUrls: string[],
  tenant_id: string,
  bucket: string = 'public', // Sử dụng bucket 'public' theo cấu hình hiện tại
  useAdmin: boolean = false
): Promise<string[]> => {
  try {
    if (!imageUrls || imageUrls.length === 0) {
      return [];
    }

    // Xử lý từng hình ảnh
    const uploadPromises = imageUrls.map(url =>
      downloadAndUploadImage(url, tenant_id, bucket, useAdmin)
        .catch(error => {
          console.error(`[downloadAndUploadMultipleImages] Failed to upload ${url}:`, error);
          return url; // Giữ nguyên URL gốc nếu không thể tải
        })
    );

    // Chờ tất cả các hình ảnh được xử lý
    const uploadedUrls = await Promise.all(uploadPromises);

    return uploadedUrls;
  } catch (error: any) {
    console.error(`[downloadAndUploadMultipleImages] Error:`, error);
    return imageUrls; // Trả về mảng URL gốc nếu có lỗi
  }
};

/**
 * Xóa hình ảnh từ Supabase storage với tenant isolation
 * @param filePath Đường dẫn file cần xóa (bao gồm tenant_id)
 * @param bucket Tên bucket (mặc định là 'products')
 * @param useAdmin Sử dụng admin client để bypass RLS (mặc định là false)
 * @returns Promise<boolean> True nếu xóa thành công
 */
export const deleteImageFromStorage = async (
  filePath: string,
  bucket: string = 'public', // Sử dụng bucket 'public' theo cấu hình hiện tại
  useAdmin: boolean = false
): Promise<boolean> => {
  try {
    const client = useAdmin && isServiceKeyConfigured() ? supabaseAdmin : supabase;


    const { data, error } = await client.storage
      .from(bucket)
      .remove([filePath]);

    if (error) {
      console.error(`[deleteImageFromStorage] Delete error:`, error);
      throw new Error(`Không thể xóa file: ${error.message}`);
    }

    return true;
  } catch (error: any) {
    console.error(`[deleteImageFromStorage] Error:`, error);
    return false;
  }
};

/**
 * Lấy danh sách hình ảnh trong thư mục tenant
 * @param tenant_id ID của tenant
 * @param bucket Tên bucket (mặc định là 'products')
 * @param useAdmin Sử dụng admin client để bypass RLS (mặc định là false)
 * @returns Promise<any[]> Danh sách file trong thư mục tenant
 */
export const listTenantImages = async (
  tenant_id: string,
  bucket: string = 'public', // Sử dụng bucket 'public' theo cấu hình hiện tại
  useAdmin: boolean = false
): Promise<any[]> => {
  try {
    const client = useAdmin && isServiceKeyConfigured() ? supabaseAdmin : supabase;


    const { data, error } = await client.storage
      .from(bucket)
      .list(`products/${tenant_id}`);

    if (error) {
      console.error(`[listTenantImages] List error:`, error);
      throw new Error(`Không thể lấy danh sách file: ${error.message}`);
    }

    return data || [];
  } catch (error: any) {
    console.error(`[listTenantImages] Error:`, error);
    return [];
  }
};

/**
 * Kiểm tra quyền truy cập file theo tenant
 * @param filePath Đường dẫn file
 * @param tenant_id ID của tenant
 * @returns boolean True nếu file thuộc về tenant
 */
export const checkTenantFileAccess = (filePath: string, tenant_id: string): boolean => {
  try {
    // Tách đường dẫn để lấy tenant folder
    const pathParts = filePath.split('/');
    const fileTenantId = pathParts[0];

    return fileTenantId === tenant_id;
  } catch (error) {
    console.error(`[checkTenantFileAccess] Error checking access:`, error);
    return false;
  }
};
