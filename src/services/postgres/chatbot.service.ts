import { supabaseAdmin } from '../../config/supabase';

/**
 * Interface cho thông tin chatbot channel
 */
interface ChatbotChannel {
  id: string;
  is_active: boolean;
  bot_id: string;
  tenant_id: string;
}

/**
 * Interface cho thông tin chatbot instruction
 */
interface ChatbotInstruction {
  id: string;
  name: string;
  description: string;
  instruction: string;
  delay_time: number;
  is_active: boolean;
  type: 'sale' | 'rag_bot' | 'sale_bot';
  created_at: string;
  updated_at: string;
}

/**
 * Lấy thông tin chatbot dựa trên inbox_id và tenant_id
 * @param inbox_id ID của inbox
 * @returns Thông tin chatbot channel và instruction
 */
export const getChatbotByInboxId = async ({
  inbox_id,
}: {
  inbox_id: string;
}): Promise<{
  success: boolean;
  data?: {
    channel: ChatbotChannel | null;
    instruction: ChatbotInstruction | null;
  };
  message: string;
}> => {
  try {
    // Kiểm tra tham số đầu vào
    if (!inbox_id) {
      return {
        success: false,
        message: "Thiếu thông tin inbox_id",
      };
    }

    // Truy vấn thông tin chatbot channel
    const { data: chatbotChannels, error: channelError } = await supabaseAdmin
      .from('chatbot_channels')
      .select('id, is_active, bot_id, tenant_id')
      .eq('inbox_id', inbox_id)
      .limit(1);

    if (channelError) {
      console.error("Lỗi khi truy vấn chatbot channel:", channelError);
      return {
        success: false,
        message: `Lỗi khi truy vấn chatbot channel: ${channelError.message}`,
      };
    }

    // Nếu không tìm thấy chatbot channel
    if (!chatbotChannels || chatbotChannels.length === 0) {
      return {
        success: false,
        message: "Không tìm thấy chatbot cho inbox này",
      };
    }

    const chatbotChannel = chatbotChannels[0];

    // Kiểm tra trạng thái kích hoạt của chatbot channel
    if (!chatbotChannel.is_active) {
      return {
        success: false,
        data: {
          channel: chatbotChannel,
          instruction: null,
        },
        message: "Chatbot cho inbox này đã bị vô hiệu hóa",
      };
    }

    // Truy vấn thông tin chatbot instruction và cấu hình đầy đủ
    const { data: chatbotInstructions, error: instructionError } = await supabaseAdmin
      .from('chatbot_configurations')
      .select('id, name, description, instruction, delay_time, is_active, type, created_at, updated_at')
      .eq('id', chatbotChannel.bot_id)
      .limit(1);

    if (instructionError) {
      console.error("Lỗi khi truy vấn chatbot instruction:", instructionError);
      return {
        success: false,
        message: `Lỗi khi truy vấn chatbot instruction: ${instructionError.message}`,
      };
    }

    // Nếu không tìm thấy chatbot instruction
    if (!chatbotInstructions || chatbotInstructions.length === 0) {
      return {
        success: false,
        data: {
          channel: chatbotChannel,
          instruction: null,
        },
        message: "Không tìm thấy thông tin cấu hình cho chatbot này",
      };
    }

    const chatbotInstruction = chatbotInstructions[0];

    // Trả về kết quả thành công
    return {
      success: true,
      data: {
        channel: chatbotChannel,
        instruction: chatbotInstruction,
      },
      message: "Lấy thông tin chatbot thành công",
    };
  } catch (error: any) {
    console.error("Lỗi khi lấy thông tin chatbot:", error);
    return {
      success: false,
      message: `Lỗi khi lấy thông tin chatbot: ${error?.message || "Lỗi không xác định"}`,
    };
  }
};

/**
 * Cập nhật cấu hình chatbot
 * @param bot_id ID của bot cần cập nhật
 * @param updateData Dữ liệu cập nhật
 */
// export const updateChatbotConfig = async ({
//   bot_id,
//   updateData,
// }: {
//   bot_id: string;
//   updateData: {
//     instruction?: string;
//     delay_time?: number;
//     name?: string;
//     description?: string;
//     is_active?: boolean;
//   };
// }): Promise<{
//   success: boolean;
//   data?: ChatbotInstruction;
//   message: string;
// }> => {
//   try {
//     // Kiểm tra tham số đầu vào
//     if (!bot_id) {
//       return {
//         success: false,
//         message: "Thiếu thông tin bot_id",
//       };
//     }

//     // Validate delay_time nếu có
//     if (updateData.delay_time !== undefined) {
//       if (updateData.delay_time < 0 || updateData.delay_time > 30) {
//         return {
//           success: false,
//           message: "delay_time phải từ 0-30 giây (0 = không delay)",
//         };
//       }
//     }

//     // Tạo danh sách các trường cần cập nhật
//     const updateFields: string[] = [];
//     const updateValues: any[] = [];
//     let paramIndex = 1;

//     if (updateData.instruction !== undefined) {
//       updateFields.push(`instruction = $${paramIndex++}`);
//       updateValues.push(updateData.instruction);
//     }

//     if (updateData.delay_time !== undefined) {
//       updateFields.push(`delay_time = $${paramIndex++}`);
//       updateValues.push(updateData.delay_time);
//     }

//     if (updateData.name !== undefined) {
//       updateFields.push(`name = $${paramIndex++}`);
//       updateValues.push(updateData.name);
//     }

//     if (updateData.description !== undefined) {
//       updateFields.push(`description = $${paramIndex++}`);
//       updateValues.push(updateData.description);
//     }

//     if (updateData.is_active !== undefined) {
//       updateFields.push(`is_active = $${paramIndex++}`);
//       updateValues.push(updateData.is_active);
//     }

//     // Thêm updated_at
//     updateFields.push(`updated_at = NOW()`);

//     // Nếu không có trường nào được cập nhật
//     if (updateFields.length === 1) { // Chỉ có updated_at
//       return {
//         success: false,
//         message: "Không có thông tin nào được cập nhật",
//       };
//     }

//     // Tạo object cập nhật cho Supabase
//     const updateObject: any = {
//       updated_at: new Date().toISOString(),
//     };

//     if (updateData.instruction !== undefined) {
//       updateObject.instruction = updateData.instruction;
//     }
//     if (updateData.delay_time !== undefined) {
//       updateObject.delay_time = updateData.delay_time;
//     }
//     if (updateData.name !== undefined) {
//       updateObject.name = updateData.name;
//     }
//     if (updateData.description !== undefined) {
//       updateObject.description = updateData.description;
//     }
//     if (updateData.is_active !== undefined) {
//       updateObject.is_active = updateData.is_active;
//     }

//     // Thực hiện cập nhật với Supabase
//     const { data: updatedConfigs, error: updateError } = await supabaseAdmin
//       .from('chatbot_configurations')
//       .update(updateObject)
//       .eq('id', bot_id)
//       .select('id, name, description, instruction, delay_time, is_active, created_at, updated_at');

//     if (updateError) {
//       console.error("Lỗi khi cập nhật chatbot configuration:", updateError);
//       return {
//         success: false,
//         message: `Lỗi khi cập nhật chatbot configuration: ${updateError.message}`,
//       };
//     }

//     if (!updatedConfigs || updatedConfigs.length === 0) {
//       return {
//         success: false,
//         message: "Không tìm thấy chatbot configuration để cập nhật",
//       };
//     }

//     const updatedConfig = updatedConfigs[0];

//     if (!updatedConfig) {
//       return {
//         success: false,
//         message: "Không tìm thấy chatbot configuration để cập nhật",
//       };
//     }

//     return {
//       success: true,
//       data: updatedConfig,
//       message: "Cập nhật cấu hình chatbot thành công",
//     };

//   } catch (error: any) {
//     console.error("Lỗi khi cập nhật cấu hình chatbot:", error);
//     return {
//       success: false,
//       message: `Lỗi khi cập nhật cấu hình chatbot: ${error?.message || "Lỗi không xác định"}`,
//     };
//   }
// };

export const getMoolyAccount = async ({ accoutnId }: { accoutnId: string }) => {
  try {
    const { data: accounts, error } = await supabaseAdmin
      .from('mooly_accounts')
      .select('*')
      .eq('account_id', accoutnId)
      .limit(1);

    if (error) {
      console.error("Lỗi khi truy vấn mooly account:", error);
      return {
        success: false,
        message: `Lỗi khi truy vấn mooly account: ${error.message}`,
      };
    }

    if (!accounts || accounts.length === 0) {
      return {
        success: false,
        message: "Không tìm thấy tài khoản",
      };
    }

    return {
      success: true,
      data: accounts[0],
    };
  } catch (error: any) {
    console.error("Lỗi khi lấy thông tin tài khoản:", error);
    return {
      success: false,
      message: `Lỗi khi lấy thông tin tài khoản: ${error?.message || "Lỗi không xác định"}`,
    };
  }
};
