# Dịch vụ PostgreSQL

Thư mục này chứa các dịch vụ kết nối trực tiếp đến PostgreSQL thay vì sử dụng Supabase client. Điều này giúp tối ưu hóa hiệu suất và giảm thiểu lỗi khi làm việc với dữ liệu.

## Dịch vụ vận chuyển (shipping.service.ts)

Dịch vụ này cung cấp các chức năng liên quan đến vận chuyển:

### 1. getShippingOptions

Lấy danh sách các phương thức vận chuyển có sẵn dựa trên địa chỉ giao hàng.

```typescript
const result = await getShippingOptions({
  address: {
    city: 'Hà Nội',
    district: 'Cầu Giấy',
    province: 'Hà Nội', // Tùy chọn
    ward: 'Dịch Vọng', // Tùy chọn
    country: 'Vietnam', // Tùy chọn
  },
  tenant_id: 'your-tenant-id',
});

if (result.success) {
  const shippingOptions = result.data;
  // X<PERSON> lý danh sách phương thức vận chuyển
}
```

### 2. getShippingMethodDetails

Lấy thông tin chi tiết của một phương thức vận chuyển cụ thể.

```typescript
const result = await getShippingMethodDetails({
  shipping_method_id: 'shipping-method-uuid',
  tenant_id: 'your-tenant-id',
});

if (result.success) {
  const shippingMethod = result.data;
  // Xử lý thông tin phương thức vận chuyển
}
```

### 3. calculateShippingFee

Tính phí vận chuyển dựa trên phương thức vận chuyển và giá trị đơn hàng.

```typescript
const result = await calculateShippingFee({
  shipping_method_id: 'shipping-method-uuid',
  order_value: 500000, // Giá trị đơn hàng (VND)
  tenant_id: 'your-tenant-id',
});

if (result.success) {
  const { shipping_fee, method_name, estimated_delivery } = result.data;
  // Xử lý thông tin phí vận chuyển
}
```

## Cấu trúc dữ liệu

### Phương thức vận chuyển (shipping_methods)

```typescript
{
  id: string; // UUID của phương thức vận chuyển
  code: string; // Mã phương thức vận chuyển (ví dụ: 'STANDARD', 'EXPRESS')
  name: string; // Tên phương thức vận chuyển
  description: string; // Mô tả phương thức vận chuyển
  fee: number; // Phí vận chuyển
  estimated_delivery: string; // Thời gian giao hàng dự kiến (ví dụ: '3-5 ngày')
  free_shipping_threshold: number | null; // Ngưỡng miễn phí vận chuyển
  minimum_order_amount: number; // Giá trị đơn hàng tối thiểu
}
```

## Kiểm tra dịch vụ

Để kiểm tra dịch vụ vận chuyển, bạn có thể chạy file `src/test-shipping.ts`:

```bash
npx ts-node src/test-shipping.ts
```

Lưu ý: Đảm bảo bạn đã cấu hình kết nối PostgreSQL trong file `.env` hoặc `config/postgres.ts`.
