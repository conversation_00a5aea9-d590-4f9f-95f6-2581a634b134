import { sql } from '../../config/postgres';

/**
 * Tì<PERSON> kiếm khách hàng theo số điện thoại
 * @param phone Số điện thoại của khách hàng
 * @param tenant_id ID của tenant
 */
export const findCustomerByPhone = async ({
  phone,
  tenant_id,
}: {
  phone: string;
  tenant_id: string;
}) => {
  try {

    const customers = await sql`
      SELECT *
      FROM customers
      WHERE phone = ${phone}
      AND tenant_id = ${tenant_id}
    `;

    if (!customers || customers.length === 0) {
      return {
        success: false,
        message: 'Không tìm thấy khách hàng',
      };
    }

    return {
      success: true,
      data: customers[0],
    };
  } catch (error: any) {
    console.error('Lỗi khi tìm kiếm khách hàng:', error);
    return {
      success: false,
      message: `Lỗi khi tìm kiếm khách hàng: ${error?.message || 'Lỗi không xác định'}`,
    };
  }
};

/**
 * Tạo khách hàng mới
 * @param customerData Thông tin khách hàng
 * @param tenant_id ID của tenant
 */
export const createCustomer = async ({
  customerData,
  tenant_id,
}: {
  customerData: {
    full_name: string;
    phone: string;
    email?: string;
    notes?: string;
  };
  tenant_id: string;
}) => {
  try {

    const newCustomer = await sql`
      INSERT INTO customers (
        tenant_id,
        full_name,
        phone,
        email,
        notes,
        total_spent,
        orders_count
      ) VALUES (
        ${tenant_id},
        ${customerData.full_name},
        ${customerData.phone},
        ${customerData.email || null},
        ${customerData.notes || null},
        0,
        0
      )
      RETURNING *
    `;

    return {
      success: true,
      data: newCustomer[0],
    };
  } catch (error: any) {
    console.error('Lỗi khi tạo khách hàng mới:', error);
    return {
      success: false,
      message: `Lỗi khi tạo khách hàng mới: ${error?.message || 'Lỗi không xác định'}`,
    };
  }
};

/**
 * Tìm kiếm khách hàng theo số điện thoại hoặc tạo mới nếu không tồn tại
 * @param customerData Thông tin khách hàng
 * @param tenant_id ID của tenant
 */
export const findOrCreateCustomer = async ({
  customerData,
  tenant_id,
}: {
  customerData: {
    full_name: string;
    phone: string;
    email?: string;
    notes?: string;
  };
  tenant_id: string;
}) => {
  try {

    // Tìm kiếm khách hàng theo số điện thoại
    const existingCustomer = await findCustomerByPhone({
      phone: customerData.phone,
      tenant_id,
    });

    // Nếu tìm thấy khách hàng, trả về thông tin khách hàng
    if (existingCustomer.success) {
      return existingCustomer;
    }

    // Nếu không tìm thấy, tạo khách hàng mới
    return createCustomer({
      customerData,
      tenant_id,
    });
  } catch (error: any) {
    console.error('Lỗi khi tìm kiếm hoặc tạo khách hàng:', error);
    return {
      success: false,
      message: `Lỗi khi tìm kiếm hoặc tạo khách hàng: ${error?.message || 'Lỗi không xác định'}`,
    };
  }
};

/**
 * Lấy danh sách địa chỉ của khách hàng
 * @param customer_id ID của khách hàng
 * @param tenant_id ID của tenant
 */
export const getCustomerAddresses = async ({
  customer_id,
  tenant_id,
}: {
  customer_id: string;
  tenant_id: string;
}) => {
  try {

    const addresses = await sql`
      SELECT *
      FROM customer_addresses
      WHERE customer_id = ${customer_id}
      AND tenant_id = ${tenant_id}
      ORDER BY is_default_shipping DESC, is_default_billing DESC, created_at DESC
    `;

    return {
      success: true,
      data: addresses || [],
    };
  } catch (error: any) {
    console.error('Lỗi khi lấy danh sách địa chỉ khách hàng:', error);
    return {
      success: false,
      message: `Lỗi khi lấy danh sách địa chỉ khách hàng: ${error?.message || 'Lỗi không xác định'}`,
    };
  }
};

/**
 * Lấy thông tin chi tiết của một địa chỉ
 * @param address_id ID của địa chỉ
 * @param tenant_id ID của tenant
 */
export const getAddressDetails = async ({
  address_id,
  tenant_id,
}: {
  address_id: string;
  tenant_id: string;
}) => {
  try {

    const addresses = await sql`
      SELECT *
      FROM customer_addresses
      WHERE id = ${address_id}
      AND tenant_id = ${tenant_id}
    `;

    if (!addresses || addresses.length === 0) {
      return {
        success: false,
        message: 'Không tìm thấy địa chỉ',
      };
    }

    return {
      success: true,
      data: addresses[0],
    };
  } catch (error: any) {
    console.error('Lỗi khi lấy thông tin chi tiết địa chỉ:', error);
    return {
      success: false,
      message: `Lỗi khi lấy thông tin chi tiết địa chỉ: ${error?.message || 'Lỗi không xác định'}`,
    };
  }
};

/**
 * Tạo địa chỉ mới cho khách hàng
 * @param addressData Thông tin địa chỉ
 * @param tenant_id ID của tenant
 */
export const createCustomerAddress = async ({
  addressData,
  tenant_id,
}: {
  addressData: {
    customer_id: string;
    full_name: string;
    phone: string;
    address: string;
    province?: string;
    district?: string;
    ward?: string;
    country?: string;
    address_type?: 'shipping' | 'billing' | 'both';
    is_default_shipping?: boolean;
    is_default_billing?: boolean;
    notes?: string;
  };
  tenant_id: string;
}) => {
  try {

    // Nếu đánh dấu là địa chỉ mặc định, cập nhật các địa chỉ khác
    if (addressData.is_default_shipping) {
      await sql`
        UPDATE customer_addresses
        SET is_default_shipping = false
        WHERE customer_id = ${addressData.customer_id}
        AND tenant_id = ${tenant_id}
        AND is_default_shipping = true
      `;
    }

    if (addressData.is_default_billing) {
      await sql`
        UPDATE customer_addresses
        SET is_default_billing = false
        WHERE customer_id = ${addressData.customer_id}
        AND tenant_id = ${tenant_id}
        AND is_default_billing = true
      `;
    }

    // Tạo địa chỉ mới
    const newAddress = await sql`
      INSERT INTO customer_addresses (
        customer_id,
        tenant_id,
        full_name,
        phone,
        address,
        province,
        district,
        ward,
        country,
        address_type,
        is_default_shipping,
        is_default_billing,
        notes
      ) VALUES (
        ${addressData.customer_id},
        ${tenant_id},
        ${addressData.full_name},
        ${addressData.phone},
        ${addressData.address},
        ${addressData.province || null},
        ${addressData.district || null},
        ${addressData.ward || null},
        ${addressData.country || 'Vietnam'},
        ${addressData.address_type || 'shipping'},
        ${addressData.is_default_shipping || false},
        ${addressData.is_default_billing || false},
        ${addressData.notes || null}
      )
      RETURNING *
    `;

    return {
      success: true,
      data: newAddress[0],
    };
  } catch (error: any) {
    console.error('Lỗi khi tạo địa chỉ mới:', error);
    return {
      success: false,
      message: `Lỗi khi tạo địa chỉ mới: ${error?.message || 'Lỗi không xác định'}`,
    };
  }
};

/**
 * Cập nhật thông tin địa chỉ
 * @param address_id ID của địa chỉ
 * @param addressData Thông tin địa chỉ cần cập nhật
 * @param tenant_id ID của tenant
 */
export const updateCustomerAddress = async ({
  address_id,
  addressData,
  tenant_id,
}: {
  address_id: string;
  addressData: {
    full_name?: string;
    phone?: string;
    address?: string;
    province?: string;
    district?: string;
    ward?: string;
    country?: string;
    address_type?: 'shipping' | 'billing' | 'both';
    is_default_shipping?: boolean;
    is_default_billing?: boolean;
    notes?: string;
  };
  tenant_id: string;
}) => {
  try {

    // Lấy thông tin địa chỉ hiện tại
    const currentAddress = await sql`
      SELECT customer_id FROM customer_addresses
      WHERE id = ${address_id} AND tenant_id = ${tenant_id}
    `;

    if (!currentAddress || currentAddress.length === 0) {
      return {
        success: false,
        message: 'Không tìm thấy địa chỉ',
      };
    }

    const customer_id = currentAddress[0].customer_id;

    // Nếu đánh dấu là địa chỉ mặc định, cập nhật các địa chỉ khác
    if (addressData.is_default_shipping) {
      await sql`
        UPDATE customer_addresses
        SET is_default_shipping = false
        WHERE customer_id = ${customer_id}
        AND tenant_id = ${tenant_id}
        AND id != ${address_id}
        AND is_default_shipping = true
      `;
    }

    if (addressData.is_default_billing) {
      await sql`
        UPDATE customer_addresses
        SET is_default_billing = false
        WHERE customer_id = ${customer_id}
        AND tenant_id = ${tenant_id}
        AND id != ${address_id}
        AND is_default_billing = true
      `;
    }

    // Xây dựng câu truy vấn động để cập nhật chỉ các trường được cung cấp
    let updateFields = [];
    let updateValues: any[] = [];

    if (addressData.full_name !== undefined) {
      updateFields.push('full_name = $' + (updateValues.length + 1));
      updateValues.push(addressData.full_name);
    }

    if (addressData.phone !== undefined) {
      updateFields.push('phone = $' + (updateValues.length + 1));
      updateValues.push(addressData.phone);
    }

    if (addressData.address !== undefined) {
      updateFields.push('address = $' + (updateValues.length + 1));
      updateValues.push(addressData.address);
    }

    if (addressData.province !== undefined) {
      updateFields.push('province = $' + (updateValues.length + 1));
      updateValues.push(addressData.province);
    }

    if (addressData.district !== undefined) {
      updateFields.push('district = $' + (updateValues.length + 1));
      updateValues.push(addressData.district);
    }

    if (addressData.ward !== undefined) {
      updateFields.push('ward = $' + (updateValues.length + 1));
      updateValues.push(addressData.ward);
    }

    if (addressData.country !== undefined) {
      updateFields.push('country = $' + (updateValues.length + 1));
      updateValues.push(addressData.country);
    }

    if (addressData.address_type !== undefined) {
      updateFields.push('address_type = $' + (updateValues.length + 1));
      updateValues.push(addressData.address_type);
    }

    if (addressData.is_default_shipping !== undefined) {
      updateFields.push('is_default_shipping = $' + (updateValues.length + 1));
      updateValues.push(addressData.is_default_shipping);
    }

    if (addressData.is_default_billing !== undefined) {
      updateFields.push('is_default_billing = $' + (updateValues.length + 1));
      updateValues.push(addressData.is_default_billing);
    }

    if (addressData.notes !== undefined) {
      updateFields.push('notes = $' + (updateValues.length + 1));
      updateValues.push(addressData.notes);
    }

    // Thêm updated_at
    updateFields.push('updated_at = $' + (updateValues.length + 1));
    updateValues.push(new Date());

    // Nếu không có trường nào được cập nhật
    if (updateFields.length === 0) {
      return {
        success: false,
        message: 'Không có thông tin nào được cập nhật',
      };
    }

    // Thực hiện cập nhật
    const query = `
      UPDATE customer_addresses
      SET ${updateFields.join(', ')}
      WHERE id = $${updateValues.length + 1} AND tenant_id = $${updateValues.length + 2}
      RETURNING *
    `;

    updateValues.push(address_id);
    updateValues.push(tenant_id);

    const updatedAddress = await sql.unsafe(query, updateValues);

    return {
      success: true,
      data: updatedAddress[0],
    };
  } catch (error: any) {
    console.error('Lỗi khi cập nhật địa chỉ:', error);
    return {
      success: false,
      message: `Lỗi khi cập nhật địa chỉ: ${error?.message || 'Lỗi không xác định'}`,
    };
  }
};

/**
 * Xóa địa chỉ
 * @param address_id ID của địa chỉ
 * @param tenant_id ID của tenant
 */
export const deleteCustomerAddress = async ({
  address_id,
  tenant_id,
}: {
  address_id: string;
  tenant_id: string;
}) => {
  try {

    await sql`
      DELETE FROM customer_addresses
      WHERE id = ${address_id}
      AND tenant_id = ${tenant_id}
    `;

    return {
      success: true,
      message: 'Xóa địa chỉ thành công',
    };
  } catch (error: any) {
    console.error('Lỗi khi xóa địa chỉ:', error);
    return {
      success: false,
      message: `Lỗi khi xóa địa chỉ: ${error?.message || 'Lỗi không xác định'}`,
    };
  }
};
