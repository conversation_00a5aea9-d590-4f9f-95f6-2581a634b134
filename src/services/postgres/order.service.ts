import { sql } from '../../config/postgres';

/**
 * Cập nhật thông tin đơn hàng
 * Cho phép cập nhật các thông tin khác nhau tùy thuộc vào trạng thái đơn hàng
 * @param order_id ID của đơn hàng
 * @param updateData Dữ liệu cập nhật
 * @param tenant_id ID của tenant
 */
export const updateOrder = async ({
  order_id,
  updateData,
  tenant_id,
}: {
  order_id: string;
  updateData: {
    // Thông tin khách hàng
    customer_name?: string;
    customer_phone?: string;
    customer_email?: string;

    // Địa chỉ giao hàng
    shipping_address?: {
      full_name?: string;
      phone?: string;
      address?: string;
      province?: string;
      district?: string;
      ward?: string;
      country?: string;
    };

    // Thông tin đơn hàng
    notes?: string;
    shipping_method?: string;
    payment_method?: string;

    // Cập nhật sản phẩm trong đơn hàng
    items_update?: Array<{
      item_id: string; // ID của order_item cần cập nhật
      quantity?: number; // Số lượng mới
      action: 'update' | 'remove'; // Hành động: cập nhật hoặc xóa
    }>;

    // Thêm sản phẩm mới vào đơn hàng
    new_items?: Array<{
      product_id: string;
      variant_id?: string;
      quantity: number;
      unit_price: number;
    }>;
  };
  tenant_id: string;
}) => {
  try {

    // Sử dụng sql.begin() để bắt đầu transaction
    return await sql.begin(async (transaction) => {
      try {
        // Kiểm tra đơn hàng tồn tại và lấy trạng thái hiện tại
        const orderCheck = await transaction.unsafe(`
          SELECT id, status FROM orders
          WHERE id = $1
          AND tenant_id = $2
        `, [order_id, tenant_id]);

        if (!orderCheck || orderCheck.length === 0) {
          return {
            success: false,
            message: `Không tìm thấy đơn hàng với ID ${order_id}`,
          };
        }

        const currentStatus = orderCheck[0].status;

        // Kiểm tra các điều kiện cập nhật dựa trên trạng thái đơn hàng
        const canUpdateItems = ['pending', 'confirmed', 'processing'].includes(currentStatus);
        const canUpdateAddress = ['pending', 'confirmed', 'processing'].includes(currentStatus);
        const canUpdatePhone = true; // Có thể cập nhật số điện thoại ở mọi trạng thái

        // Chuẩn bị dữ liệu cập nhật cho bảng orders
        const updateFields = [];
        const updateValues = [];

        // Cập nhật thông tin khách hàng
        if (updateData.customer_name) {
          updateFields.push('customer_name');
          updateValues.push(updateData.customer_name);
        }

        if (updateData.customer_phone) {
          updateFields.push('customer_phone');
          updateValues.push(updateData.customer_phone);
        }

        if (updateData.customer_email) {
          updateFields.push('customer_email');
          updateValues.push(updateData.customer_email);
        }

        // Cập nhật ghi chú đơn hàng
        if (updateData.notes) {
          updateFields.push('notes');
          updateValues.push(updateData.notes);
        }

        // Cập nhật phương thức vận chuyển và thanh toán nếu đơn hàng chưa được xử lý
        if (canUpdateItems) {
          if (updateData.shipping_method) {
            updateFields.push('shipping_method');
            updateValues.push(updateData.shipping_method);
          }

          if (updateData.payment_method) {
            updateFields.push('payment_method');
            updateValues.push(updateData.payment_method);
          }
        }

        // Cập nhật thời gian cập nhật
        updateFields.push('updated_at');
        updateValues.push('NOW()');

        // Thực hiện cập nhật bảng orders nếu có trường cần cập nhật
        if (updateFields.length > 0) {
          const setClause = updateFields.map((field, index) => {
            return field === 'updated_at' ? `${field} = NOW()` : `${field} = $${index + 1}`;
          }).join(', ');

          await transaction.unsafe(`
            UPDATE orders
            SET ${setClause}
            WHERE id = $${updateFields.length + 1}
            AND tenant_id = $${updateFields.length + 2}
          `, [...updateValues.filter(v => v !== 'NOW()'), order_id, tenant_id]);
        }

        // Cập nhật địa chỉ giao hàng nếu được phép
        if (updateData.shipping_address && canUpdateAddress) {
          // Lấy ID địa chỉ giao hàng hiện tại
          const orderAddressInfo = await transaction`
            SELECT shipping_address_id FROM orders
            WHERE id = ${order_id}
            AND tenant_id = ${tenant_id}
          `;

          if (orderAddressInfo && orderAddressInfo.length > 0 && orderAddressInfo[0].shipping_address_id) {
            const addressId = orderAddressInfo[0].shipping_address_id;

            // Chuẩn bị dữ liệu cập nhật cho bảng customer_addresses
            const addressUpdateFields = [];
            const addressUpdateValues = [];

            if (updateData.shipping_address.full_name) {
              addressUpdateFields.push('full_name');
              addressUpdateValues.push(updateData.shipping_address.full_name);
            }

            if (updateData.shipping_address.phone) {
              addressUpdateFields.push('phone');
              addressUpdateValues.push(updateData.shipping_address.phone);
            }

            if (updateData.shipping_address.address) {
              addressUpdateFields.push('address');
              addressUpdateValues.push(updateData.shipping_address.address);
            }

            if (updateData.shipping_address.province) {
              addressUpdateFields.push('province');
              addressUpdateValues.push(updateData.shipping_address.province);
            }

            if (updateData.shipping_address.district) {
              addressUpdateFields.push('district');
              addressUpdateValues.push(updateData.shipping_address.district);
            }

            if (updateData.shipping_address.ward) {
              addressUpdateFields.push('ward');
              addressUpdateValues.push(updateData.shipping_address.ward);
            }

            if (updateData.shipping_address.country) {
              addressUpdateFields.push('country');
              addressUpdateValues.push(updateData.shipping_address.country);
            }

            // Cập nhật thời gian cập nhật
            addressUpdateFields.push('updated_at');
            addressUpdateValues.push('NOW()');

            // Thực hiện cập nhật bảng customer_addresses nếu có trường cần cập nhật
            if (addressUpdateFields.length > 0) {
              const addressSetClause = addressUpdateFields.map((field, index) => {
                return field === 'updated_at' ? `${field} = NOW()` : `${field} = $${index + 1}`;
              }).join(', ');

              await transaction.unsafe(`
                UPDATE customer_addresses
                SET ${addressSetClause}
                WHERE id = $${addressUpdateFields.length + 1}
                AND tenant_id = $${addressUpdateFields.length + 2}
              `, [...addressUpdateValues.filter(v => v !== 'NOW()'), addressId, tenant_id]);
            }
          }
        }

        // Cập nhật sản phẩm trong đơn hàng nếu được phép
        if (canUpdateItems) {
          // Cập nhật các sản phẩm hiện có
          if (updateData.items_update && updateData.items_update.length > 0) {
            for (const item of updateData.items_update) {
              if (item.action === 'update' && item.quantity) {
                // Lấy thông tin sản phẩm hiện tại
                const currentItem = await transaction.unsafe(`
                  SELECT * FROM order_items
                  WHERE id = $1
                  AND order_id = $2
                  AND tenant_id = $3
                `, [item.item_id, order_id, tenant_id]);

                if (currentItem && currentItem.length > 0) {
                  const oldQuantity = currentItem[0].quantity;
                  const unitPrice = currentItem[0].unit_price;
                  const newTotalPrice = item.quantity * unitPrice;

                  // Cập nhật số lượng và tổng giá
                  await transaction.unsafe(`
                    UPDATE order_items
                    SET
                      quantity = $1,
                      total_price = $2
                    WHERE id = $3
                    AND order_id = $4
                    AND tenant_id = $5
                  `, [item.quantity, newTotalPrice, item.item_id, order_id, tenant_id]);
                }
              } else if (item.action === 'remove') {
                // Xóa sản phẩm khỏi đơn hàng
                await transaction.unsafe(`
                  DELETE FROM order_items
                  WHERE id = $1
                  AND order_id = $2
                  AND tenant_id = $3
                `, [item.item_id, order_id, tenant_id]);
              }
            }
          }

          // Thêm sản phẩm mới vào đơn hàng
          if (updateData.new_items && updateData.new_items.length > 0) {
            for (const newItem of updateData.new_items) {
              // Lấy thông tin sản phẩm
              const productInfo = await transaction.unsafe(`
                SELECT name, sku, image_url FROM products
                WHERE id = $1
                AND tenant_id = $2
              `, [newItem.product_id, tenant_id]);

              if (productInfo && productInfo.length > 0) {
                const product = productInfo[0];
                let variantInfo = null;

                // Nếu có variant_id, lấy thông tin biến thể
                if (newItem.variant_id) {
                  const variantData = await transaction.unsafe(`
                    SELECT attributes FROM product_variants
                    WHERE id = $1
                    AND product_id = $2
                    AND tenant_id = $3
                  `, [newItem.variant_id, newItem.product_id, tenant_id]);

                  if (variantData && variantData.length > 0) {
                    variantInfo = variantData[0].attributes;
                  }
                }

                // Thêm sản phẩm mới vào đơn hàng
                await transaction.unsafe(`
                  INSERT INTO order_items (
                    tenant_id,
                    order_id,
                    product_id,
                    variant_id,
                    name,
                    sku,
                    quantity,
                    unit_price,
                    total_price,
                    image_url,
                    variant_info
                  ) VALUES (
                    $1,
                    $2,
                    $3,
                    $4,
                    $5,
                    $6,
                    $7,
                    $8,
                    $9,
                    $10,
                    $11
                  )
                `, [
                  tenant_id,
                  order_id,
                  newItem.product_id,
                  newItem.variant_id || null,
                  product.name,
                  product.sku,
                  newItem.quantity,
                  newItem.unit_price,
                  newItem.quantity * newItem.unit_price,
                  product.image_url,
                  variantInfo
                ]);
              }
            }
          }

          // Cập nhật tổng giá trị đơn hàng
          const orderItems = await transaction.unsafe(`
            SELECT SUM(total_price) as subtotal
            FROM order_items
            WHERE order_id = $1
            AND tenant_id = $2
          `, [order_id, tenant_id]);

          if (orderItems && orderItems.length > 0) {
            const subtotal = orderItems[0].subtotal || 0;

            await transaction.unsafe(`
              UPDATE orders
              SET
                subtotal = $1,
                total_amount = $2
              WHERE id = $3
              AND tenant_id = $4
            `, [subtotal, subtotal, order_id, tenant_id]);
          }
        }

        // Thêm lịch sử cập nhật đơn hàng
        await transaction.unsafe(`
          INSERT INTO order_history (
            tenant_id,
            order_id,
            status,
            comment
          ) VALUES (
            $1,
            $2,
            $3::order_status_enum,
            $4
          )
        `, [tenant_id, order_id, currentStatus, 'Đơn hàng đã được cập nhật thông tin']);

        // Lấy thông tin đơn hàng sau khi cập nhật
        const updatedOrder = await transaction.unsafe(`
          SELECT * FROM orders
          WHERE id = $1
          AND tenant_id = $2
        `, [order_id, tenant_id]);

        // Lấy thông tin các sản phẩm trong đơn hàng
        const updatedOrderItems = await transaction.unsafe(`
          SELECT * FROM order_items
          WHERE order_id = $1
          AND tenant_id = $2
        `, [order_id, tenant_id]);

        return {
          success: true,
          data: {
            ...updatedOrder[0],
            items: updatedOrderItems,
          },
          message: 'Cập nhật đơn hàng thành công',
        };
      } catch (error: any) {
        console.error('Lỗi trong transaction:', error);
        throw error;
      }
    });
  } catch (error: any) {
    console.error('Lỗi khi cập nhật đơn hàng:', error);
    return {
      success: false,
      message: `Lỗi khi cập nhật đơn hàng: ${error?.message || 'Lỗi không xác định'}`,
    };
  }
};
