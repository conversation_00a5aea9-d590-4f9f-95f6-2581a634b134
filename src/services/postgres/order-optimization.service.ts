import { sql } from "../../config/postgres";
import { getActivePromotions } from "./promotion.service";
import { calculateShippingFee, getShippingOptions } from "./shipping.service";

/**
 * Service tối ưu hóa đơn hàng - tự động áp dụng khuyến mãi và vận chuyển tốt nhất
 */

/**
 * Tìm khuyến mãi tốt nhất cho đơn hàng
 * @param orderData Thông tin đơn hàng
 * @param tenant_id ID của tenant
 */
export const findBestPromotion = async ({
  orderData,
  tenant_id,
}: {
  orderData: {
    items: Array<{
      product_id: string;
      variant_id?: string;
      quantity: number;
      unit_price: number;
    }>;
    subtotal: number;
  };
  tenant_id: string;
}) => {
  try {

    // Lấy tất cả khuyến mãi đang hoạt động
    const promotionsResult = await getActivePromotions({
      tenant_id,
    });

    if (!promotionsResult.success || !promotionsResult.data) {
      return {
        success: false,
        message: "Không tìm thấy khuyến mãi nào",
      };
    }

    const promotions = promotionsResult.data;
    const productIds = orderData.items.map(item => item.product_id);

    let bestPromotion = null;
    let maxDiscount = 0;

    // Duyệt qua tất cả khuyến mãi để tìm khuyến mãi tốt nhất
    for (const promo of promotions) {
      let canApply = true;
      let discountAmount = 0;

      // Kiểm tra điều kiện áp dụng khuyến mãi
      // 1. Kiểm tra giá trị đơn hàng tối thiểu
      if (promo.min_purchase_amount && orderData.subtotal < parseFloat(promo.min_purchase_amount.toString())) {
        canApply = false;
        continue;
      }

      // 2. Kiểm tra giới hạn sử dụng
      if (promo.usage_limit && promo.usage_count >= promo.usage_limit) {
        canApply = false;
        continue;
      }

      // 3. Kiểm tra phạm vi áp dụng
      if (promo.applies_to === 'products' && promo.eligible_product_ids) {
        // eligible_product_ids là jsonb, cần parse
        let eligibleProductIds = [];
        try {
          if (typeof promo.eligible_product_ids === 'string') {
            eligibleProductIds = JSON.parse(promo.eligible_product_ids);
          } else if (Array.isArray(promo.eligible_product_ids)) {
            eligibleProductIds = promo.eligible_product_ids;
          } else if (typeof promo.eligible_product_ids === 'object') {
            eligibleProductIds = promo.eligible_product_ids;
          }
        } catch (error) {
          console.error('Lỗi parse eligible_product_ids:', error);
          canApply = false;
          continue;
        }

        // Kiểm tra xem có sản phẩm nào trong đơn hàng thuộc danh sách sản phẩm được áp dụng không
        const hasEligibleProduct = productIds.some(id => eligibleProductIds.includes(id));
        if (!hasEligibleProduct) {
          canApply = false;
          continue;
        }
      }

      if (promo.applies_to === 'categories' && promo.eligible_category_ids) {
        // Lấy danh mục của các sản phẩm trong đơn hàng
        const productCategories = await sql`
          SELECT DISTINCT category_id FROM products
          WHERE id IN ${sql(productIds)}
          AND tenant_id = ${tenant_id}
        `;

        const categoryIds = productCategories.map(p => p.category_id);

        // eligible_category_ids là jsonb, cần parse
        let eligibleCategoryIds = [];
        try {
          if (typeof promo.eligible_category_ids === 'string') {
            eligibleCategoryIds = JSON.parse(promo.eligible_category_ids);
          } else if (Array.isArray(promo.eligible_category_ids)) {
            eligibleCategoryIds = promo.eligible_category_ids;
          } else if (typeof promo.eligible_category_ids === 'object') {
            eligibleCategoryIds = promo.eligible_category_ids;
          }
        } catch (error) {
          console.error('Lỗi parse eligible_category_ids:', error);
          canApply = false;
          continue;
        }

        const hasEligibleCategory = categoryIds.some(id => eligibleCategoryIds.includes(id));
        if (!hasEligibleCategory) {
          canApply = false;
          continue;
        }
      }

      // Tính giá trị khuyến mãi nếu có thể áp dụng
      if (canApply) {
        if (promo.value_type === 'percentage') {
          discountAmount = (orderData.subtotal * parseFloat(promo.value.toString())) / 100;
        } else if (promo.value_type === 'fixed') {
          discountAmount = parseFloat(promo.value.toString());
        }

        // Kiểm tra giá trị khuyến mãi tối đa
        if (promo.max_discount_amount && discountAmount > parseFloat(promo.max_discount_amount.toString())) {
          discountAmount = parseFloat(promo.max_discount_amount.toString());
        }

        // Cập nhật khuyến mãi tốt nhất nếu giảm giá nhiều hơn
        if (discountAmount > maxDiscount) {
          maxDiscount = discountAmount;
          bestPromotion = {
            ...promo,
            discount_amount: discountAmount,
          };
        }
      }
    }

    if (bestPromotion) {
      return {
        success: true,
        data: bestPromotion,
        message: `Tìm thấy khuyến mãi tốt nhất: ${bestPromotion.name} - Giảm ${maxDiscount.toLocaleString('vi-VN')} VNĐ`,
      };
    } else {
      return {
        success: false,
        message: "Không có khuyến mãi nào phù hợp với đơn hàng này",
      };
    }
  } catch (error: any) {
    console.error('Lỗi khi tìm khuyến mãi tốt nhất:', error);
    return {
      success: false,
      message: `Lỗi khi tìm khuyến mãi: ${error?.message || 'Lỗi không xác định'}`,
    };
  }
};

/**
 * Tìm phương thức vận chuyển tốt nhất cho đơn hàng
 * @param orderData Thông tin đơn hàng
 * @param tenant_id ID của tenant
 */
export const findBestShippingMethod = async ({
  orderData,
  tenant_id,
}: {
  orderData: {
    subtotal: number;
    shipping_address?: {
      province?: string;
      district?: string;
      city?: string;
    };
  };
  tenant_id: string;
}) => {
  try {

    // Lấy tất cả phương thức vận chuyển có sẵn trực tiếp từ database
    const shippingMethods = await sql`
      SELECT
        id,
        name,
        description,
        price,
        free_shipping_threshold,
        minimum_order_amount,
        sort_order
      FROM shipping_methods
      WHERE tenant_id = ${tenant_id}
      AND is_active = true
      ORDER BY sort_order ASC
    `;

    if (!shippingMethods || shippingMethods.length === 0) {
      return {
        success: false,
        message: "Không tìm thấy phương thức vận chuyển nào",
      };
    }
    let bestMethod = null;
    let lowestFee = Infinity;

    // Duyệt qua tất cả phương thức vận chuyển để tìm phương thức tốt nhất
    for (const method of shippingMethods) {
      // Tính phí vận chuyển trực tiếp
      let shippingFee = parseFloat(method.price.toString()) || 0;

      // Kiểm tra ngưỡng miễn phí vận chuyển
      if (method.free_shipping_threshold && orderData.subtotal >= parseFloat(method.free_shipping_threshold.toString())) {
        shippingFee = 0;
      }

      // Kiểm tra giá trị đơn hàng tối thiểu
      if (method.minimum_order_amount && orderData.subtotal < parseFloat(method.minimum_order_amount.toString())) {
        continue; // Bỏ qua phương thức này nếu không đạt giá trị tối thiểu
      }

      // Chọn phương thức có phí thấp nhất
      if (shippingFee < lowestFee) {
        lowestFee = shippingFee;
        bestMethod = {
          id: method.id,
          name: method.name,
          description: method.description,
          code: method.id, // Sử dụng id làm code
          calculated_fee: shippingFee,
          estimated_delivery: "3-5 ngày", // Giá trị mặc định
        };
      }
    }

    if (bestMethod) {
      return {
        success: true,
        data: bestMethod,
        message: `Tìm thấy phương thức vận chuyển tốt nhất: ${bestMethod.name} - Phí ${lowestFee.toLocaleString('vi-VN')} VNĐ`,
      };
    } else {
      return {
        success: false,
        message: "Không thể tính phí vận chuyển cho bất kỳ phương thức nào",
      };
    }
  } catch (error: any) {
    console.error('Lỗi khi tìm phương thức vận chuyển tốt nhất:', error);
    return {
      success: false,
      message: `Lỗi khi tìm phương thức vận chuyển: ${error?.message || 'Lỗi không xác định'}`,
    };
  }
};

/**
 * Tối ưu hóa đơn hàng - tự động áp dụng khuyến mãi và vận chuyển tốt nhất
 * @param orderData Thông tin đơn hàng
 * @param tenant_id ID của tenant
 */
export const optimizeOrder = async ({
  orderData,
  tenant_id,
}: {
  orderData: {
    items: Array<{
      product_id: string;
      variant_id?: string;
      quantity: number;
      unit_price: number;
    }>;
    shipping_address?: {
      province?: string;
      district?: string;
      city?: string;
    };
  };
  tenant_id: string;
}) => {
  try {
    // Tính subtotal
    const subtotal = orderData.items.reduce(
      (total, item) => total + item.unit_price * item.quantity,
      0
    );

    const optimizedOrderData = {
      ...orderData,
      subtotal,
    };

    // Tìm khuyến mãi tốt nhất
    const promotionResult = await findBestPromotion({
      orderData: optimizedOrderData,
      tenant_id,
    });

    // Tìm phương thức vận chuyển tốt nhất
    const shippingResult = await findBestShippingMethod({
      orderData: optimizedOrderData,
      tenant_id,
    });

    return {
      success: true,
      data: {
        subtotal,
        best_promotion: promotionResult.success ? promotionResult.data : null,
        best_shipping: shippingResult.success ? shippingResult.data : null,
        promotion_message: promotionResult.message,
        shipping_message: shippingResult.message,
      },
    };
  } catch (error: any) {
    console.error('Lỗi khi tối ưu hóa đơn hàng:', error);
    return {
      success: false,
      message: `Lỗi khi tối ưu hóa đơn hàng: ${error?.message || 'Lỗi không xác định'}`,
    };
  }
};
