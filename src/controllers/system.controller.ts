import { Request, Response, NextFunction } from 'express';
import { checkSupabaseConnection, checkSupabaseAdminConnection, isServiceKeyConfigured } from '../config/supabase';
import { sendError, sendSuccess } from '../utils/response.utils';
import { messageBufferService } from "../services/queue/message-buffer.service";
import { getQueuesStats } from "../services/queue/queues";
import {
  getAllQueuesStats,
  cleanupAllQueues,
  pauseAllQueues,
  resumeAllQueues,
  getBullBoardHealth
} from "../services/queue/bull-board.service";
import {  getChatbotByInboxId } from "../services/postgres/chatbot.service";

/**
 * Kiểm tra kết nối Supabase
 */
export const checkSupabase = async (
  _req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const result = await checkSupabaseConnection();
    return sendSuccess(res, result, result.message);
  } catch (error) {
    next(error);
  }
};

/**
 * <PERSON><PERSON>m tra kết nối Supabase Admin
 */
export const checkSupabaseAdmin = async (
  _req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    // Kiểm tra xem service key có được cấu hình hay không
    if (!isServiceKeyConfigured()) {
      return sendError(
        res,
        'Supabase Service Key chưa được cấu hình. Vui lòng thêm SUPABASE_SERVICE_KEY vào file .env',
        400
      );
    }

    const result = await checkSupabaseAdminConnection();
    return sendSuccess(res, result, result.message);
  } catch (error) {
    next(error);
  }
};

/**
 * Kiểm tra trạng thái hệ thống
 */
export const getSystemStatus = async (
  _req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    // Kiểm tra kết nối Supabase
    const supabaseResult = await checkSupabaseConnection();
    
    // Kiểm tra kết nối Supabase Admin nếu có service key
    let supabaseAdminResult = {
      success: false,
      message: 'Supabase Service Key chưa được cấu hình',
    };
    
    if (isServiceKeyConfigured()) {
      supabaseAdminResult = await checkSupabaseAdminConnection();
    }
    
    // Trả về kết quả
    return sendSuccess(
      res,
      {
        supabase: supabaseResult,
        supabaseAdmin: supabaseAdminResult,
        serviceKeyConfigured: isServiceKeyConfigured(),
      },
      'Kiểm tra trạng thái hệ thống thành công'
    );
  } catch (error) {
    next(error);
  }
};

/**
 * Lấy thống kê Message Batching System
 */
export const getMessageBatchingStats = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    // Lấy thống kê buffer
    const bufferStats = await messageBufferService.getBufferStats();

    // Lấy trạng thái kết nối Redis
    const connectionStatus = await messageBufferService.getConnectionStatus();

    // Lấy thống kê queue
    const queueStats = await getQueuesStats();

    // Lấy thông tin message batching queue
    const messageBatchingQueueStats = queueStats['message-batching'] || {
      waiting: 0,
      active: 0,
      completed: 0,
      failed: 0,
      delayed: 0,
    };

    const stats = {
      buffer: bufferStats,
      connection: connectionStatus,
      queue: messageBatchingQueueStats,
      performance: {
        avgMessagesPerBatch: bufferStats.avgMessagesPerBatch,
        totalBatches: bufferStats.totalBatches,
        totalMessages: bufferStats.totalMessages,
        batchingEfficiency: bufferStats.totalBatches > 0
          ? ((bufferStats.totalMessages / bufferStats.totalBatches) / bufferStats.totalMessages * 100).toFixed(2) + '%'
          : '0%',
      },
      health: {
        isHealthy: connectionStatus.isConnected && messageBatchingQueueStats.failed < 10,
        redisConnected: connectionStatus.isConnected,
        redisStatus: connectionStatus.redisStatus,
        retryCount: connectionStatus.retryCount,
        activeJobs: messageBatchingQueueStats.active,
        failedJobs: messageBatchingQueueStats.failed,
        delayedJobs: messageBatchingQueueStats.delayed,
      },
      timestamp: new Date().toISOString(),
    };

    return sendSuccess(res, stats);
  } catch (error) {
    console.error('❌ Lỗi khi lấy thống kê message batching:', error);
    return sendError(res, "Không thể lấy thống kê", 500);
  }
};

/**
 * Kiểm tra trạng thái kết nối Message Buffer Service
 */
export const getMessageBufferConnectionStatus = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const connectionStatus = await messageBufferService.getConnectionStatus();

    return sendSuccess(res, {
      ...connectionStatus,
      timestamp: new Date().toISOString(),
      message: connectionStatus.isConnected
        ? 'Message Buffer Service kết nối thành công'
        : 'Message Buffer Service không thể kết nối',
    });
  } catch (error) {
    console.error('❌ Lỗi khi kiểm tra trạng thái Message Buffer:', error);
    return sendError(res, "Không thể kiểm tra trạng thái kết nối", 500);
  }
};

/**
 * Cleanup expired batches (manual trigger)
 */
export const cleanupExpiredBatches = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const cleanedCount = await messageBufferService.cleanupExpiredBatches();
    
    return sendSuccess(res, {
      message: `Đã cleanup ${cleanedCount} expired batches`,
      cleanedCount,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('❌ Lỗi khi cleanup batches:', error);
    return sendError(res, "Không thể cleanup batches", 500);
  }
};

/**
 * Cấu hình Message Batching cho bot
 */
// export const updateBotBatchingConfig = async (
//   req: Request,
//   res: Response,
//   next: NextFunction
// ) => {
//   try {
//     const { bot_id, delay_time, instruction } = req.body;

//     if (!bot_id) {
//       return sendError(res, "Thiếu thông tin bot_id", 400);
//     }

//     // Validate delay_time nếu có
//     if (delay_time !== undefined && (delay_time < 0 || delay_time > 30)) {
//       return sendError(res, "delay_time phải từ 0-30 giây (0 = không delay)", 400);
//     }

//     // Tạo object cập nhật
//     const updateData: any = {};
//     if (delay_time !== undefined) {
//       updateData.delay_time = delay_time;
//     }
//     if (instruction !== undefined) {
//       updateData.instruction = instruction;
//     }

//     // Kiểm tra có dữ liệu để cập nhật không
//     if (Object.keys(updateData).length === 0) {
//       return sendError(res, "Không có thông tin nào để cập nhật", 400);
//     }

//     // Cập nhật cấu hình chatbot
//     const result = await updateChatbotConfig({
//       bot_id,
//       updateData,
//     });

//     if (!result.success) {
//       return sendError(res, result.message, 400);
//     }

//     return sendSuccess(res, {
//       message: result.message,
//       data: result.data,
//       timestamp: new Date().toISOString(),
//     });
//   } catch (error) {
//     console.error('❌ Lỗi khi cập nhật cấu hình chatbot:', error);
//     return sendError(res, "Không thể cập nhật cấu hình", 500);
//   }
// };

/**
 * Lấy cấu hình chatbot theo inbox_id
 */
export const getChatbotConfig = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { inbox_id } = req.params;

    if (!inbox_id) {
      return sendError(res, "Thiếu thông tin inbox_id", 400);
    }

    // Lấy cấu hình chatbot
    const result = await getChatbotByInboxId({ inbox_id });

    if (!result.success) {
      return sendError(res, result.message, 404);
    }

    return sendSuccess(res, {
      channel: result.data?.channel,
      configuration: result.data?.instruction,
      timestamp: new Date().toISOString(),
    }, result.message);
  } catch (error) {
    console.error('❌ Lỗi khi lấy cấu hình chatbot:', error);
    return sendError(res, "Không thể lấy cấu hình chatbot", 500);
  }
};

/**
 * Lấy thống kê tất cả queues (cho Bull Board API)
 */
export const getAllQueuesStatsAPI = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const stats = await getAllQueuesStats();
    
    return sendSuccess(res, {
      queues: stats,
      summary: {
        totalQueues: Object.keys(stats).length,
        totalJobs: Object.values(stats).reduce((acc: number, queueStats: any) => {
          return acc + (queueStats.total || 0);
        }, 0),
        healthyQueues: Object.values(stats).filter((queueStats: any) => !queueStats.error).length,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('❌ Lỗi khi lấy thống kê queues:', error);
    return sendError(res, "Không thể lấy thống kê queues", 500);
  }
};

/**
 * Cleanup tất cả queues
 */
export const cleanupAllQueuesAPI = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { keepJobs = 100 } = req.body;
    
    if (keepJobs < 10 || keepJobs > 1000) {
      return sendError(res, "keepJobs phải từ 10-1000", 400);
    }

    const results = await cleanupAllQueues(keepJobs);
    
    const successCount = Object.values(results).filter((r: any) => r.success).length;
    const totalQueues = Object.keys(results).length;

    return sendSuccess(res, {
      message: `Cleanup hoàn thành: ${successCount}/${totalQueues} queues thành công`,
      results,
      keepJobs,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('❌ Lỗi khi cleanup queues:', error);
    return sendError(res, "Không thể cleanup queues", 500);
  }
};

/**
 * Pause tất cả queues (emergency stop)
 */
export const pauseAllQueuesAPI = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const results = await pauseAllQueues();
    
    const successCount = Object.values(results).filter((r: any) => r.success).length;
    const totalQueues = Object.keys(results).length;

    return sendSuccess(res, {
      message: `Pause hoàn thành: ${successCount}/${totalQueues} queues đã dừng`,
      results,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('❌ Lỗi khi pause queues:', error);
    return sendError(res, "Không thể pause queues", 500);
  }
};

/**
 * Resume tất cả queues
 */
export const resumeAllQueuesAPI = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const results = await resumeAllQueues();
    
    const successCount = Object.values(results).filter((r: any) => r.success).length;
    const totalQueues = Object.keys(results).length;

    return sendSuccess(res, {
      message: `Resume hoàn thành: ${successCount}/${totalQueues} queues đã khởi động`,
      results,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('❌ Lỗi khi resume queues:', error);
    return sendError(res, "Không thể resume queues", 500);
  }
};

/**
 * Health check cho Bull Board
 */
export const getBullBoardHealthAPI = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const health = await getBullBoardHealth();
    
    return sendSuccess(res, health);
  } catch (error) {
    console.error('❌ Lỗi khi kiểm tra Bull Board health:', error);
    return sendError(res, "Không thể kiểm tra Bull Board health", 500);
  }
};
