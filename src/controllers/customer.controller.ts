import { Request, Response } from 'express';
import * as customerService from '../services/supabase/customer.service';

/**
 * Tìm kiếm khách hàng theo số điện thoại
 * @param req Request
 * @param res Response
 */
export const findCustomerByPhone = async (req: Request, res: Response) => {
  try {
    const { phone } = req.params;
    const tenant_id = req.headers['tenant-id'] as string;

    if (!tenant_id) {
      return res.status(400).json({
        success: false,
        message: 'Thiếu thông tin tenant_id trong header',
      });
    }

    if (!phone) {
      return res.status(400).json({
        success: false,
        message: 'Thiếu thông tin số điện thoại',
      });
    }

    const result = await customerService.findCustomerByPhone({
      phone,
      tenant_id,
    });

    return res.status(result.success ? 200 : 404).json(result);
  } catch (error: any) {
    console.error('Lỗi khi tìm kiếm khách hàng:', error);
    return res.status(500).json({
      success: false,
      message: `Lỗi khi tìm kiếm khách hàng: ${error?.message || 'Lỗi không xác định'}`,
    });
  }
};

/**
 * Tìm kiếm khách hàng theo số điện thoại hoặc tạo mới nếu không tồn tại
 * @param req Request
 * @param res Response
 */
export const findOrCreateCustomer = async (req: Request, res: Response) => {
  try {
    const customerData = req.body;
    const tenant_id = req.headers['tenant-id'] as string;

    if (!tenant_id) {
      return res.status(400).json({
        success: false,
        message: 'Thiếu thông tin tenant_id trong header',
      });
    }

    if (!customerData.phone) {
      return res.status(400).json({
        success: false,
        message: 'Thiếu thông tin số điện thoại',
      });
    }

    if (!customerData.full_name) {
      return res.status(400).json({
        success: false,
        message: 'Thiếu thông tin họ tên',
      });
    }

    const result = await customerService.findOrCreateCustomer({
      customerData,
      tenant_id,
    });

    return res.status(result.success ? 200 : 400).json(result);
  } catch (error: any) {
    console.error('Lỗi khi tìm kiếm hoặc tạo khách hàng:', error);
    return res.status(500).json({
      success: false,
      message: `Lỗi khi tìm kiếm hoặc tạo khách hàng: ${error?.message || 'Lỗi không xác định'}`,
    });
  }
};

/**
 * Lấy danh sách địa chỉ của khách hàng
 * @param req Request
 * @param res Response
 */
export const getCustomerAddresses = async (req: Request, res: Response) => {
  try {
    const { customer_id } = req.params;
    const tenant_id = req.headers['tenant-id'] as string;

    if (!tenant_id) {
      return res.status(400).json({
        success: false,
        message: 'Thiếu thông tin tenant_id trong header',
      });
    }

    if (!customer_id) {
      return res.status(400).json({
        success: false,
        message: 'Thiếu thông tin customer_id',
      });
    }

    const result = await customerService.getCustomerAddresses({
      customer_id,
      tenant_id,
    });

    return res.status(result.success ? 200 : 400).json(result);
  } catch (error: any) {
    console.error('Lỗi khi lấy danh sách địa chỉ khách hàng:', error);
    return res.status(500).json({
      success: false,
      message: `Lỗi khi lấy danh sách địa chỉ khách hàng: ${error?.message || 'Lỗi không xác định'}`,
    });
  }
};

/**
 * Lấy thông tin chi tiết của một địa chỉ
 * @param req Request
 * @param res Response
 */
export const getAddressDetails = async (req: Request, res: Response) => {
  try {
    const { address_id } = req.params;
    const tenant_id = req.headers['tenant-id'] as string;

    if (!tenant_id) {
      return res.status(400).json({
        success: false,
        message: 'Thiếu thông tin tenant_id trong header',
      });
    }

    if (!address_id) {
      return res.status(400).json({
        success: false,
        message: 'Thiếu thông tin address_id',
      });
    }

    const result = await customerService.getAddressDetails({
      address_id,
      tenant_id,
    });

    return res.status(result.success ? 200 : 400).json(result);
  } catch (error: any) {
    console.error('Lỗi khi lấy thông tin chi tiết địa chỉ:', error);
    return res.status(500).json({
      success: false,
      message: `Lỗi khi lấy thông tin chi tiết địa chỉ: ${error?.message || 'Lỗi không xác định'}`,
    });
  }
};

/**
 * Tạo địa chỉ mới cho khách hàng
 * @param req Request
 * @param res Response
 */
export const createCustomerAddress = async (req: Request, res: Response) => {
  try {
    const addressData = req.body;
    const tenant_id = req.headers['tenant-id'] as string;

    if (!tenant_id) {
      return res.status(400).json({
        success: false,
        message: 'Thiếu thông tin tenant_id trong header',
      });
    }

    if (!addressData.customer_id) {
      return res.status(400).json({
        success: false,
        message: 'Thiếu thông tin customer_id',
      });
    }

    if (!addressData.address) {
      return res.status(400).json({
        success: false,
        message: 'Thiếu thông tin địa chỉ',
      });
    }

    const result = await customerService.createCustomerAddress({
      addressData,
      tenant_id,
    });

    return res.status(result.success ? 201 : 400).json(result);
  } catch (error: any) {
    console.error('Lỗi khi tạo địa chỉ mới:', error);
    return res.status(500).json({
      success: false,
      message: `Lỗi khi tạo địa chỉ mới: ${error?.message || 'Lỗi không xác định'}`,
    });
  }
};

/**
 * Cập nhật thông tin địa chỉ
 * @param req Request
 * @param res Response
 */
export const updateCustomerAddress = async (req: Request, res: Response) => {
  try {
    const { address_id } = req.params;
    const addressData = req.body;
    const tenant_id = req.headers['tenant-id'] as string;

    if (!tenant_id) {
      return res.status(400).json({
        success: false,
        message: 'Thiếu thông tin tenant_id trong header',
      });
    }

    if (!address_id) {
      return res.status(400).json({
        success: false,
        message: 'Thiếu thông tin address_id',
      });
    }

    const result = await customerService.updateCustomerAddress({
      address_id,
      addressData,
      tenant_id,
    });

    return res.status(result.success ? 200 : 400).json(result);
  } catch (error: any) {
    console.error('Lỗi khi cập nhật địa chỉ:', error);
    return res.status(500).json({
      success: false,
      message: `Lỗi khi cập nhật địa chỉ: ${error?.message || 'Lỗi không xác định'}`,
    });
  }
};

/**
 * Xóa địa chỉ
 * @param req Request
 * @param res Response
 */
export const deleteCustomerAddress = async (req: Request, res: Response) => {
  try {
    const { address_id } = req.params;
    const tenant_id = req.headers['tenant-id'] as string;

    if (!tenant_id) {
      return res.status(400).json({
        success: false,
        message: 'Thiếu thông tin tenant_id trong header',
      });
    }

    if (!address_id) {
      return res.status(400).json({
        success: false,
        message: 'Thiếu thông tin address_id',
      });
    }

    const result = await customerService.deleteCustomerAddress({
      address_id,
      tenant_id,
    });

    return res.status(result.success ? 200 : 400).json(result);
  } catch (error: any) {
    console.error('Lỗi khi xóa địa chỉ:', error);
    return res.status(500).json({
      success: false,
      message: `Lỗi khi xóa địa chỉ: ${error?.message || 'Lỗi không xác định'}`,
    });
  }
};
