import { Request, Response } from 'express';
import * as productService from '../services/supabase/product.service';

/**
 * L<PERSON>y thông tin chi tiết của một sản phẩm
 * @param req Request
 * @param res Response
 */
export const getProductDetails = async (req: Request, res: Response) => {
  try {
    const { product_id } = req.params;
    const tenant_id = req.headers['tenant-id'] as string;

    if (!tenant_id) {
      return res.status(400).json({
        success: false,
        message: 'Thiếu thông tin tenant_id trong header',
      });
    }

    if (!product_id) {
      return res.status(400).json({
        success: false,
        message: 'Thiếu thông tin product_id',
      });
    }

    const result = await productService.getProductDetails({
      product_id,
      sku: '',
      tenant_id,
    });

    return res.json(result);
  } catch (error: any) {
    console.error('Lỗi khi lấy thông tin chi tiết sản phẩm:', error);
    return res.status(500).json({
      success: false,
      message: `Lỗi khi lấy thông tin chi tiết sản phẩm: ${error?.message || 'Lỗi không xác định'}`,
    });
  }
};

/**
 * Lấy thông tin đơn hàng theo order_code
 * @param req Request
 * @param res Response
 */
export const getOrderByCode = async (req: Request, res: Response) => {
  try {
    const { order_code } = req.params;
    const tenant_id = req.headers['tenant-id'] as string;

    if (!tenant_id) {
      return res.status(400).json({
        success: false,
        message: 'Thiếu thông tin tenant_id trong header',
      });
    }

    if (!order_code) {
      return res.status(400).json({
        success: false,
        message: 'Thiếu thông tin order_code',
      });
    }

    const result = await productService.getOrderByCode({
      order_code,
      tenant_id,
    });

    return res.json(result);
  } catch (error: any) {
    console.error('Lỗi khi lấy thông tin đơn hàng:', error);
    return res.status(500).json({
      success: false,
      message: `Lỗi khi lấy thông tin đơn hàng: ${error?.message || 'Lỗi không xác định'}`,
    });
  }
};
