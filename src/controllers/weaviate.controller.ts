import { Request, Response, NextFunction } from "express";
import {
  initFaqsCollection,
  addFaq,
  searchFaqs,
  addFaqs,
  updateFaqBySupabaseId as updateFaqService,
  deleteFaqBySupabaseId as deleteFaqService,
  deleteFaqsBySupabaseIds as deleteFaqsService,
  initProductsCollection,
  addProduct,
  addProducts,
  searchProducts,
  updateProduct,
  deleteProduct,
  deleteProductsByFilter,
  deleteProductByProductId,
  deleteProductsByProductIds,
  updateProductByProductId,
  updateProductBotId,
} from "../services/weaviate";
import { sendError, sendSuccess } from "../utils/response.utils";
import { supabase } from "../config/supabase";

/**
 * Khởi tạo collection Faqs trong Weaviate
 */
export const initFaqs = async (
  _req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const result = await initFaqsCollection();
    return sendSuccess(res, result, result.message);
  } catch (error) {
    next(error);
  }
};

/**
 * Khởi tạo collection Products trong Weaviate
 */
export const initProducts = async (
  _req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const result = await initProductsCollection();
    return sendSuccess(res, result, result.message);
  } catch (error) {
    next(error);
  }
};

/**
 * Thêm nhiều FAQ mới vào collection
 */
export const createFaqs = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { faqs } = req.body;

    if (!faqs) {
      return sendError(res, "Thiếu thông tin faqs", 400);
    }

    const result = await addFaqs(faqs);
    return sendSuccess(res, result.data, "Đã thêm FAQ thành công");
  } catch (error) {
    next(error);
  }
};

/**
 * Tìm kiếm FAQ dựa trên nội dung
 */
export const searchFaq = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { query, tenant_id, limit } = req.query;

    if (!query) {
      return sendError(res, "Thiếu thông tin query để tìm kiếm", 400);
    }

    const result = await searchFaqs(
      query.toString(),
      tenant_id?.toString() || "",
      limit ? parseInt(limit.toString()) : 3
    );
    return sendSuccess(res, result.data, "Tìm kiếm FAQ thành công");
  } catch (error) {
    next(error);
  }
};

/**
 * Thêm một sản phẩm mới vào collection
 */
export const createProduct = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { name, price, stock, description, image_url, tenant_id, bot_id, product_id } =
      req.body;

    // Kiểm tra các trường bắt buộc
    if (!name || !tenant_id || !product_id) {
      return sendError(res, "Thiếu thông tin bắt buộc (name, tenant_id, product_id)", 400);
    }

    const result = await addProduct({
      name,
      price: price || 0,
      stock: stock || 0,
      description: description || "",
      image_url: image_url || "",
      tenant_id,
      bot_id: bot_id || "",
      product_id,
    });
    return sendSuccess(res, result.data, "Đã thêm sản phẩm thành công");
  } catch (error) {
    next(error);
  }
};

/**
 * Thêm nhiều sản phẩm cùng lúc vào collection
 */
export const createProducts = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { products } = req.body;

    if (!products || !Array.isArray(products) || products.length === 0) {
      return sendError(
        res,
        "Thiếu thông tin products hoặc không đúng định dạng",
        400
      );
    }
    // Kiểm tra xem tất cả các sản phẩm có cùng tenant_id không
    const firstTenantId = products[0].tenant_id;
    const allSameTenant = products.every(
      (product) => product.tenant_id === firstTenantId
    );

    if (!allSameTenant) {
      return sendError(
        res,
        "Tất cả sản phẩm phải thuộc cùng một tenant_id",
        400
      );
    }

    const result = await addProducts(products);
    return sendSuccess(
      res,
      result.data,
      `Đã thêm ${products.length} sản phẩm thành công`
    );
  } catch (error) {
    next(error);
  }
};

/**
 * Tìm kiếm sản phẩm dựa trên nội dung
 */
export const searchProduct = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { query, image_url, tenant_id, limit, bot_id } = req.query;

    if (!query) {
      return sendError(res, "Thiếu thông tin query để tìm kiếm", 400);
    }

    if (!tenant_id) {
      return sendError(res, "Thiếu thông tin tenant_id", 400);
    }

    const result = await searchProducts(
      {
        query: query.toString(),
        image_url: image_url?.toString() || "",
        tenant_id: tenant_id.toString(),
        bot_id: bot_id?.toString() || "",
      },
      limit ? parseInt(limit.toString()) : 5
    );
    return sendSuccess(res, result.data, "Tìm kiếm sản phẩm thành công");
  } catch (error) {
    next(error);
  }
};

/**
 * Cập nhật thông tin sản phẩm dựa trên image_url
 */
export const updateProductByImageUrl = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const {
      name,
      price,
      stock,
      description,
      image_url,
      tenant_id,
      bot_id,
      new_image_url,
      is_active,
    } = req.body;

    if (!image_url || !tenant_id) {
      return sendError(
        res,
        "Thiếu thông tin bắt buộc (image_url, tenant_id)",
        400
      );
    }

    // Tạo đối tượng chứa dữ liệu cập nhật
    const updateData: any = {};
    if (name !== undefined) updateData.name = name;
    if (price !== undefined) updateData.price = price;
    if (stock !== undefined) updateData.stock = stock;
    if (description !== undefined) updateData.description = description;
    if (new_image_url !== undefined) updateData.image_url = new_image_url;
    if (bot_id !== undefined) updateData.bot_id = bot_id;
    if (is_active !== undefined) updateData.is_active = is_active;

    // Kiểm tra xem có dữ liệu cập nhật không
    if (Object.keys(updateData).length === 0) {
      return sendError(res, "Không có dữ liệu cập nhật", 400);
    }

    const result = await updateProduct({
      image_url,
      tenant_id,
      updateData,
    });

    if (!result.success) {
      return sendError(res, "Không thể cập nhật sản phẩm", 404);
    }

    // Kiểm tra xem kết quả có chứa data không
    if ("data" in result) {
      return sendSuccess(res, result.data, "Đã cập nhật sản phẩm thành công");
    } else {
      return sendSuccess(res, {}, "Đã cập nhật sản phẩm thành công");
    }
  } catch (error) {
    next(error);
  }
};

/**
 * Xóa sản phẩm dựa trên image_url
 */
export const deleteProductByImageUrl = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { image_url, tenant_id } = req.body;

    if (!image_url || !tenant_id) {
      return sendError(
        res,
        "Thiếu thông tin bắt buộc (image_url, tenant_id)",
        400
      );
    }

    const result = await deleteProduct({
      image_url,
      tenant_id: tenant_id.toString(),
    });

    if (!result.success) {
      return sendError(res, "Không thể xóa sản phẩm", 404);
    }

    // Kiểm tra xem kết quả có chứa data không
    if ("data" in result) {
      return sendSuccess(res, result.data, "Đã xóa sản phẩm thành công");
    } else {
      return sendSuccess(res, {}, "Đã xóa sản phẩm thành công");
    }
  } catch (error) {
    next(error);
  }
};

/**
 * Xóa nhiều sản phẩm theo bộ lọc
 */
export const deleteProductsByFilterHandler = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { tenant_id, property, operator, value } = req.body;

    if (!tenant_id || !property || !operator || value === undefined) {
      return sendError(
        res,
        "Thiếu thông tin bộ lọc (tenant_id, property, operator, value)",
        400
      );
    }

    // Kiểm tra toán tử hợp lệ
    const validOperators = ["equal", "notEqual", "like"];
    if (!validOperators.includes(operator)) {
      return sendError(
        res,
        `Toán tử không hợp lệ. Các toán tử hợp lệ: ${validOperators.join(", ")}`,
        400
      );
    }

    const result = await deleteProductsByFilter({
      tenant_id,
      filter: {
        property,
        operator,
        value,
      },
    });

    return sendSuccess(
      res,
      result.data,
      "Đã xóa sản phẩm theo bộ lọc thành công"
    );
  } catch (error) {
    next(error);
  }
};

/**
 * Xóa sản phẩm dựa trên product_id
 */
export const deleteProductByProductIdHandler = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { product_id, tenant_id } = req.body;

    if (!product_id || !tenant_id) {
      return sendError(
        res,
        "Thiếu thông tin bắt buộc (product_id, tenant_id)",
        400
      );
    }

    const result = await deleteProductByProductId({
      product_id,
      tenant_id: tenant_id.toString(),
    });

    if (!result.success) {
      return sendError(res, result.message || "Không thể xóa sản phẩm", 404);
    }

    // Kiểm tra xem kết quả có chứa data không
    if ("data" in result) {
      return sendSuccess(res, result.data, "Đã xóa sản phẩm thành công");
    } else {
      return sendSuccess(res, {}, "Đã xóa sản phẩm thành công");
    }
  } catch (error) {
    next(error);
  }
};

/**
 * Xóa nhiều sản phẩm dựa trên danh sách product_id
 */
export const deleteProductsByProductIdsHandler = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { product_ids, tenant_id } = req.body;

    if (!product_ids || !Array.isArray(product_ids) || product_ids.length === 0) {
      return sendError(
        res,
        "Thiếu thông tin bắt buộc (product_ids phải là mảng không rỗng)",
        400
      );
    }

    if (!tenant_id) {
      return sendError(
        res,
        "Thiếu thông tin bắt buộc (tenant_id)",
        400
      );
    }

    // Kiểm tra giới hạn số lượng
    if (product_ids.length > 100) {
      return sendError(
        res,
        "Tối đa 100 sản phẩm mỗi lần xóa",
        400
      );
    }

    const result = await deleteProductsByProductIds({
      product_ids,
      tenant_id: tenant_id.toString(),
    });

    if (!result.success) {
      return sendError(res, result.message || "Không thể xóa sản phẩm", 404);
    }

    // Kiểm tra xem kết quả có chứa data không
    if ("data" in result) {
      return sendSuccess(
        res,
        result.data,
        result.message || "Đã xóa sản phẩm thành công"
      );
    } else {
      return sendSuccess(
        res,
        {},
        result.message || "Đã xóa sản phẩm thành công"
      );
    }
  } catch (error) {
    next(error);
  }
};

/**
 * Cập nhật thông tin sản phẩm dựa trên product_id
 */
export const updateProductByProductIdHandler = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const {
      product_id,
      tenant_id,
      name,
      price,
      stock,
      description,
      image_url,
      bot_id,
      is_active,
    } = req.body;

    if (!product_id || !tenant_id) {
      return sendError(
        res,
        "Thiếu thông tin bắt buộc (product_id, tenant_id)",
        400
      );
    }

    // Tạo đối tượng chứa dữ liệu cập nhật
    const updateData: any = {};
    if (name !== undefined) updateData.name = name;
    if (price !== undefined) updateData.price = price;
    if (stock !== undefined) updateData.stock = stock;
    if (description !== undefined) updateData.description = description;
    if (image_url !== undefined) updateData.image_url = image_url;
    if (bot_id !== undefined) updateData.bot_id = bot_id;
    if (is_active !== undefined) updateData.is_active = is_active;

    // Kiểm tra xem có dữ liệu cập nhật không
    if (Object.keys(updateData).length === 0) {
      return sendError(res, "Không có dữ liệu cập nhật", 400);
    }

    const result = await updateProductByProductId({
      product_id,
      tenant_id: tenant_id.toString(),
      updateData,
    });

    if (!result.success) {
      return sendError(
        res,
        result.message || "Không thể cập nhật thông tin sản phẩm",
        404
      );
    }

    // Kiểm tra xem kết quả có chứa data không
    if ("data" in result) {
      return sendSuccess(
        res,
        result.data,
        result.message || "Đã cập nhật thông tin sản phẩm thành công"
      );
    } else {
      return sendSuccess(
        res,
        {},
        result.message || "Đã cập nhật thông tin sản phẩm thành công"
      );
    }
  } catch (error) {
    next(error);
  }
};

/**
 * Cập nhật trường bot_id của sản phẩm (thêm hoặc xóa bot_id)
 */
export const updateProductBotIdHandler = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { product_id, tenant_id, bot_id, action } = req.body;

    // Kiểm tra các trường bắt buộc
    if (!product_id || !tenant_id || !bot_id) {
      return sendError(
        res,
        "Thiếu thông tin bắt buộc (product_id, tenant_id, bot_id)",
        400
      );
    }

    // Kiểm tra action hợp lệ
    if (action !== "add" && action !== "remove") {
      return sendError(
        res,
        "Action không hợp lệ. Các giá trị hợp lệ: 'add', 'remove'",
        400
      );
    }

    // Gọi service để cập nhật bot_id
    const result = await updateProductBotId({
      product_id,
      tenant_id,
      bot_id,
      action,
    });

    if (!result.success) {
      return sendError(
        res,
        result.message || "Không thể cập nhật bot_id cho sản phẩm",
        404
      );
    }

    // Kiểm tra xem kết quả có chứa data không
    if ("data" in result) {
      return sendSuccess(
        res,
        result.data,
        result.message ||
        `Đã ${action === "add" ? "thêm" : "xóa"} bot_id thành công`
      );
    } else {
      return sendSuccess(
        res,
        {},
        result.message ||
        `Đã ${action === "add" ? "thêm" : "xóa"} bot_id thành công`
      );
    }
  } catch (error) {
    next(error);
  }
};

/**
 * Cập nhật FAQ theo supabase_id
 */
export const updateFaqBySupabaseId = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { supabase_id, tenant_id, updateData } = req.body;

    if (!supabase_id || !tenant_id || !updateData) {
      return sendError(res, "Thiếu thông tin supabase_id, tenant_id hoặc updateData", 400);
    }

    const result = await updateFaqService({
      supabase_id,
      tenant_id,
      updateData,
    });

    if (result.success) {
      return sendSuccess(res, result.data, result.message);
    } else {
      return sendError(res, result.message, 404);
    }
  } catch (error) {
    next(error);
  }
};

/**
 * Xóa FAQ theo supabase_id
 */
export const deleteFaqBySupabaseId = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { supabase_id, tenant_id } = req.body;

    if (!supabase_id || !tenant_id) {
      return sendError(res, "Thiếu thông tin supabase_id hoặc tenant_id", 400);
    }

    const result = await deleteFaqService({
      supabase_id,
      tenant_id,
    });

    if (result.success) {
      return sendSuccess(res, result.data, "Đã xóa FAQ thành công");
    } else {
      return sendError(res, result.message || "Không thể xóa FAQ", 404);
    }
  } catch (error) {
    next(error);
  }
};

/**
 * Xóa nhiều FAQ theo danh sách supabase_id
 */
export const deleteFaqsBySupabaseIds = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { supabase_ids, tenant_id } = req.body;

    if (!supabase_ids || !Array.isArray(supabase_ids) || supabase_ids.length === 0 || !tenant_id) {
      return sendError(res, "Thiếu thông tin supabase_ids (mảng) hoặc tenant_id", 400);
    }

    const result = await deleteFaqsService({
      supabase_ids,
      tenant_id,
    });

    if (result.success) {
      return sendSuccess(res, result.data, result.message);
    } else {
      return sendError(res, result.message, 404);
    }
  } catch (error) {
    next(error);
  }
};
