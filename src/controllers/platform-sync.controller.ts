import { Request, Response, NextFunction } from 'express';
import {
  syncAllProductsFromHaravan,
  syncAllProductsFromSapo,
  syncAllProductsFromPlatform,
  checkHaravanProductsExistence,
  checkSapoProductsExistence,
  checkProductsExistence
} from '../services/sync/platform-sync.service';
import { sendError, sendSuccess } from '../utils/response.utils';
import { addHaravanSyncJob, addSapoSyncJob, productSyncQueue } from '../services/queue/queues';
import { haravanProductService, sapoProductService } from '../services/platforms';
import dotenv from 'dotenv';

// Load biến môi trường
dotenv.config();

// Kiểm tra xem có tắt BullMQ không
const isBullMQDisabled = process.env.DISABLE_BULLMQ === 'true'; // Sử dụng giá trị từ biến môi trường

/**
 * <PERSON>ồng bộ sản phẩm từ Haravan
 * C<PERSON>u hình mới để đồng bộ toàn bộ sản phẩm từ Haravan vào Supabase
 */
export const syncHaravanProducts = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    // Lấy thông tin từ request
    const {
      token,
      limit = 50,
      updated_at_min,
      full_sync = false, // Mặc định không đồng bộ đầy đủ
      force_update = false // Mặc định không cập nhật các sản phẩm đã tồn tại
    } = req.body;

    const tenant_id = req.body.tenant_id || req.headers['tenant-id'] || req.headers['tenant_id'];
    const bot_id = req.body.bot_id || req.headers['bot-id'] || req.headers['bot_id'];

    // Kiểm tra các trường bắt buộc
    if (!token) {
      return sendError(res, 'Thiếu token xác thực Haravan', 400);
    }

    if (!tenant_id) {
      return sendError(res, 'Thiếu thông tin tenant_id', 400);
    }

    const willForceUpdate = force_update === true || full_sync === true;
    console.info(`🚀 [HARAVAN] Tenant: ${tenant_id} | Mode: ${willForceUpdate ? 'FULL' : 'INCREMENTAL'} | Limit: ${limit}`);

    try {
      const countResult = await haravanProductService.getProductCount(token, {
        updated_at_min: updated_at_min
      });

      if (!countResult.success) {
        return sendError(res, `API key Haravan không hợp lệ hoặc đã hết hạn: ${countResult.message}`, 400);
      }

      const totalProducts = countResult.count;
      console.info(`📊 [HARAVAN] Found ${totalProducts} products`);

      // Nếu không có sản phẩm nào, trả về thông báo
      if (totalProducts === 0) {
        return sendSuccess(res, {
          count: 0,
          message: 'Không có sản phẩm nào để đồng bộ',
          sync_result: {
            total: 0,
            products: [],
            variants: []
          }
        }, 'Không có sản phẩm nào để đồng bộ');
      }

      // Bước 2: Đồng bộ sản phẩm từ Haravan

      // Tính toán số trang cần đồng bộ
      const pageSize = limit;
      const totalPages = Math.ceil(totalProducts / pageSize);

      // Tạo thông báo cho người dùng
      const responseMessage = `Đã bắt đầu đồng bộ ${totalProducts} sản phẩm từ Haravan`;

      // Kiểm tra xem có tắt BullMQ không
      if (isBullMQDisabled) {

        // Đồng bộ tất cả sản phẩm từ Haravan với phân trang và đợi kết quả
        const syncResult = await syncAllProductsFromHaravan(token, tenant_id, bot_id, {
          limit: pageSize,
          updated_at_min,
          full_sync,
          force_update
        });

        if (!syncResult.success) {
          return sendError(res, `Lỗi khi đồng bộ sản phẩm từ Haravan: ${syncResult.message}`, 500);
        }


        // Trả về kết quả đồng bộ chi tiết
        return sendSuccess(res, {
          count: totalProducts,
          message: syncResult.message,
          sync_result: syncResult.data
        }, syncResult.message);
      } else {
        // Thêm job đồng bộ sản phẩm vào queue (background)
        try {
          const job = await addHaravanSyncJob(token, tenant_id, bot_id, {
            limit: pageSize,
            updated_at_min,
            full_sync,
            force_update
          });


          // Trả về thông báo thành công ngay lập tức
          return sendSuccess(res, {
            count: totalProducts,
            message: responseMessage,
            job_id: job.id
          }, responseMessage);
        } catch (error: any) {
          console.error('Lỗi khi thêm job đồng bộ Haravan vào queue:', error);

          // Nếu không thể kết nối đến Redis, xử lý đồng bộ trực tiếp
          if (error.message && error.message.includes('ECONNREFUSED')) {

            try {
              // Đồng bộ tất cả sản phẩm từ Haravan với phân trang và đợi kết quả
              const syncResult = await syncAllProductsFromHaravan(token, tenant_id, bot_id, {
                limit: pageSize,
                updated_at_min,
                full_sync,
                force_update
              });


              // Trả về kết quả đồng bộ chi tiết
              return sendSuccess(res, {
                count: totalProducts,
                message: syncResult.message,
                sync_result: syncResult.data
              }, syncResult.message);
            } catch (syncError: any) {
              console.error('Lỗi khi đồng bộ trực tiếp:', syncError);
              return sendError(res, `Lỗi khi đồng bộ sản phẩm từ Haravan: ${syncError.message}`, 500);
            }
          }

          return sendError(res, `Lỗi khi thêm job đồng bộ vào queue: ${error.message}`, 500);
        }
      }
    } catch (error: any) {
      console.error('Lỗi khi đồng bộ sản phẩm từ Haravan:', error);
      return sendError(res, `Lỗi khi đồng bộ sản phẩm từ Haravan: ${error.message}`, 500);
    }
  } catch (error: any) {
    console.error('Lỗi khi xử lý request đồng bộ sản phẩm từ Haravan:', error);
    next(error);
  }
};

/**
 * Đồng bộ sản phẩm từ Sapo
 */
export const syncSapoProducts = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const {
      sapo_url,
      limit = 50,
      page = 1,
      updated_at_min,
      full_sync = false, // Mặc định không đồng bộ đầy đủ
      force_update = false // Mặc định không cập nhật các sản phẩm đã tồn tại
    } = req.body;

    const tenant_id = req.body.tenant_id || req.headers['tenant-id'] || req.headers['tenant_id'];
    const bot_id = req.body.bot_id || req.headers['bot-id'] || req.headers['bot_id'];

    // Kiểm tra các trường bắt buộc
    if (!sapo_url) {
      return sendError(res, 'Thiếu URL Sapo đầy đủ', 400);
    }

    if (!tenant_id) {
      return sendError(res, 'Thiếu thông tin tenant_id', 400);
    }

    const willForceUpdate = force_update === true || full_sync === true;

    // Kiểm tra API key và lấy số lượng sản phẩm
    try {
      // Kiểm tra API key bằng cách gọi API lấy số lượng sản phẩm
      const countResult = await sapoProductService.getProductCount(sapo_url, {
        updated_at_min: updated_at_min
      });

      if (!countResult.success) {
        return sendError(res, `API key Sapo không hợp lệ hoặc đã hết hạn: ${countResult.message}`, 400);
      }

      // Trả về thông báo đã bắt đầu đồng bộ và số lượng sản phẩm
      const responseMessage = `Đã bắt đầu đồng bộ ${countResult.count} sản phẩm từ Sapo`;

      // Kiểm tra xem có tắt BullMQ không
      if (isBullMQDisabled) {

        try {
          // Đồng bộ tất cả sản phẩm từ Sapo với phân trang và đợi kết quả
          const syncResult = await syncAllProductsFromSapo(sapo_url, tenant_id, bot_id, {
            limit,
            page,
            updated_at_min,
            full_sync,
            force_update
          });


          // Trả về kết quả đồng bộ chi tiết
          return sendSuccess(res, {
            count: countResult.count,
            message: syncResult.message,
            sync_result: syncResult.data
          }, syncResult.message);
        } catch (syncError: any) {
          console.error('Lỗi khi đồng bộ trực tiếp:', syncError);
          return sendError(res, `Lỗi khi đồng bộ sản phẩm từ Sapo: ${syncError.message}`, 500);
        }
      } else {
        // Thêm job đồng bộ sản phẩm vào queue (background)
        try {
          const job = await addSapoSyncJob(sapo_url, tenant_id, bot_id, {
            limit,
            page,
            updated_at_min,
            full_sync,
            force_update
          });


          // Trả về thông báo thành công ngay lập tức
          return sendSuccess(res, {
            count: countResult.count,
            message: responseMessage,
            job_id: job.id
          }, responseMessage);
        } catch (error: any) {
          console.error('Lỗi khi thêm job đồng bộ Sapo vào queue:', error);

          // Nếu không thể kết nối đến Redis, xử lý đồng bộ trực tiếp
          if (error.message && error.message.includes('ECONNREFUSED')) {

            try {
              // Đồng bộ tất cả sản phẩm từ Sapo với phân trang và đợi kết quả
              const syncResult = await syncAllProductsFromSapo(sapo_url, tenant_id, bot_id, {
                limit,
                page,
                updated_at_min,
                full_sync,
                force_update
              });


              // Trả về kết quả đồng bộ chi tiết
              return sendSuccess(res, {
                count: countResult.count,
                message: syncResult.message,
                sync_result: syncResult.data
              }, syncResult.message);
            } catch (syncError: any) {
              console.error('Lỗi khi đồng bộ trực tiếp:', syncError);
              return sendError(res, `Lỗi khi đồng bộ sản phẩm từ Sapo: ${syncError.message}`, 500);
            }
          }

          return sendError(res, `Lỗi khi thêm job đồng bộ vào queue: ${error.message}`, 500);
        }
      }
    } catch (error: any) {
      console.error('Lỗi khi kiểm tra API key Sapo:', error);
      return sendError(res, `Lỗi khi kiểm tra API key Sapo: ${error.message}`, 500);
    }
  } catch (error: any) {
    console.error('Lỗi khi đồng bộ sản phẩm từ Sapo:', error);
    next(error);
  }
};

/**
 * Kiểm tra trạng thái đồng bộ
 */
export const getSyncStatus = async (
  _req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    // Kiểm tra xem có tắt BullMQ không
    if (isBullMQDisabled) {
      return sendSuccess(
        res,
        {
          message: 'BullMQ đã bị tắt, đang sử dụng chế độ đồng bộ trực tiếp',
          status: 'direct_sync',
          counts: {
            waiting: 0,
            active: 0,
            completed: 0,
            failed: 0,
            delayed: 0
          },
          waiting: [],
          active: [],
          completed: [],
          failed: []
        },
        'Trạng thái đồng bộ hiện tại (chế độ đồng bộ trực tiếp)'
      );
    }

    // Lấy thông tin về các job trong queue
    const jobCounts = await productSyncQueue.getJobCounts();

    // Lấy danh sách các job đang chờ xử lý
    const waitingJobs = await productSyncQueue.getJobs(['waiting'], 0, 10);

    // Lấy danh sách các job đang xử lý
    const activeJobs = await productSyncQueue.getJobs(['active'], 0, 10);

    // Lấy danh sách các job đã hoàn thành gần đây
    const completedJobs = await productSyncQueue.getJobs(['completed'], 0, 10);

    // Lấy danh sách các job thất bại gần đây
    const failedJobs = await productSyncQueue.getJobs(['failed'], 0, 10);

    return sendSuccess(
      res,
      {
        counts: jobCounts,
        waiting: waitingJobs.map(job => ({
          id: job.id,
          name: job.name,
          timestamp: job.timestamp,
          data: job.data
        })),
        active: activeJobs.map(job => ({
          id: job.id,
          name: job.name,
          timestamp: job.timestamp,
          progress: job.progress,
          data: job.data
        })),
        completed: completedJobs.map(job => ({
          id: job.id,
          name: job.name,
          timestamp: job.timestamp,
          finishedOn: job.finishedOn,
          returnvalue: job.returnvalue
        })),
        failed: failedJobs.map(job => ({
          id: job.id,
          name: job.name,
          timestamp: job.timestamp,
          failedReason: job.failedReason
        }))
      },
      'Trạng thái đồng bộ hiện tại'
    );
  } catch (error: any) {
    console.error('Lỗi khi kiểm tra trạng thái đồng bộ:', error);
    next(error);
  }
};

/**
 * Đồng bộ sản phẩm từ nền tảng (API chung)
 */
export const syncProducts = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const {
      platform,
      auth_token,
      limit = 50,
      page = 1,
      updated_at_min,
      full_sync = false, // Mặc định không đồng bộ đầy đủ
      force_update = false // Mặc định không cập nhật các sản phẩm đã tồn tại
    } = req.body;
    const tenant_id = req.body.tenant_id || req.headers['tenant_id'];
    const bot_id = req.body.bot_id || req.headers['bot_id'];

    // Kiểm tra các trường bắt buộc
    if (!auth_token) {
      return sendError(res, 'Thiếu thông tin xác thực', 400);
    }

    if (!tenant_id) {
      return sendError(res, 'Thiếu thông tin tenant_id', 400);
    }

    if (!platform) {
      return sendError(res, 'Thiếu thông tin nền tảng', 400);
    }

    // Kiểm tra nền tảng hợp lệ
    if (platform !== 'haravan' && platform !== 'sapo') {
      return sendError(res, 'Nền tảng không được hỗ trợ. Chỉ hỗ trợ: haravan, sapo', 400);
    }

    // Kiểm tra API key và lấy số lượng sản phẩm
    try {
      let countResult;

      // Lấy số lượng sản phẩm từ nền tảng tương ứng
      if (platform === 'haravan') {
        countResult = await haravanProductService.getProductCount(auth_token, {
          updated_at_min: updated_at_min
        });
      } else if (platform === 'sapo') {
        countResult = await sapoProductService.getProductCount(auth_token, {
          updated_at_min: updated_at_min
        });
      } else {
        return sendError(res, `Nền tảng ${platform} không được hỗ trợ`, 400);
      }

      if (!countResult.success) {
        return sendError(res, `API key ${platform} không hợp lệ hoặc đã hết hạn: ${countResult.message}`, 400);
      }

      // Trả về thông báo đã bắt đầu đồng bộ và số lượng sản phẩm
      const responseMessage = `Đã bắt đầu đồng bộ ${countResult.count} sản phẩm từ ${platform}`;

      // Kiểm tra xem có tắt BullMQ không
      if (isBullMQDisabled) {

        try {
          // Đồng bộ tất cả sản phẩm từ nền tảng với phân trang và đợi kết quả
          const syncResult = await syncAllProductsFromPlatform(
            platform,
            auth_token,
            tenant_id,
            bot_id,
            {
              limit,
              page,
              updated_at_min,
              full_sync,
              force_update
            }
          );


          // Trả về kết quả đồng bộ chi tiết
          return sendSuccess(res, {
            count: countResult.count,
            message: syncResult.message,
            sync_result: syncResult.data
          }, syncResult.message);
        } catch (syncError: any) {
          console.error('Lỗi khi đồng bộ trực tiếp:', syncError);
          return sendError(res, `Lỗi khi đồng bộ sản phẩm từ ${platform}: ${syncError.message}`, 500);
        }
      } else {
        // Thêm job đồng bộ sản phẩm vào queue (background)
        try {
          let job;

          if (platform === 'haravan') {
            job = await addHaravanSyncJob(auth_token, tenant_id, bot_id, {
              limit,
              page,
              updated_at_min,
              full_sync,
              force_update
            });
          } else if (platform === 'sapo') {
            job = await addSapoSyncJob(auth_token, tenant_id, bot_id, {
              limit,
              page,
              updated_at_min,
              full_sync,
              force_update
            });
          }


          // Trả về thông báo thành công ngay lập tức
          return sendSuccess(res, {
            count: countResult.count,
            message: responseMessage,
            job_id: job?.id
          }, responseMessage);
        } catch (error: any) {
          console.error(`Lỗi khi thêm job đồng bộ ${platform} vào queue:`, error);

          // Nếu không thể kết nối đến Redis, xử lý đồng bộ trực tiếp
          if (error.message && error.message.includes('ECONNREFUSED')) {

            try {
              // Đồng bộ tất cả sản phẩm từ nền tảng với phân trang và đợi kết quả
              const syncResult = await syncAllProductsFromPlatform(
                platform,
                auth_token,
                tenant_id,
                bot_id,
                {
                  limit,
                  page,
                  updated_at_min,
                  full_sync,
                  force_update
                }
              );


              // Trả về kết quả đồng bộ chi tiết
              return sendSuccess(res, {
                count: countResult.count,
                message: syncResult.message,
                sync_result: syncResult.data
              }, syncResult.message);
            } catch (syncError: any) {
              console.error('Lỗi khi đồng bộ trực tiếp:', syncError);
              return sendError(res, `Lỗi khi đồng bộ sản phẩm từ ${platform}: ${syncError.message}`, 500);
            }
          }

          return sendError(res, `Lỗi khi thêm job đồng bộ vào queue: ${error.message}`, 500);
        }
      }
    } catch (error: any) {
      console.error(`Lỗi khi kiểm tra API key ${platform}:`, error);
      return sendError(res, `Lỗi khi kiểm tra API key ${platform}: ${error.message}`, 500);
    }
  } catch (error: any) {
    console.error('Lỗi khi đồng bộ sản phẩm:', error);
    next(error);
  }
};

/**
 * Kiểm tra sản phẩm từ Haravan đã tồn tại
 * API này chỉ kiểm tra và trả về kết quả ngay lập tức, không thực hiện đồng bộ
 */
export const checkHaravanProducts = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    // Lấy thông tin từ request
    const {
      token,
      limit = 50,
      updated_at_min
    } = req.body;

    const tenant_id = req.body.tenant_id || req.headers['tenant-id'] || req.headers['tenant_id'];

    // Kiểm tra các trường bắt buộc
    if (!token) {
      return sendError(res, 'Thiếu token xác thực Haravan', 400);
    }

    if (!tenant_id) {
      return sendError(res, 'Thiếu thông tin tenant_id', 400);
    }


    try {
      // Kiểm tra API key bằng cách gọi API lấy số lượng sản phẩm
      const countResult = await haravanProductService.getProductCount(token, {
        updated_at_min: updated_at_min
      });

      if (!countResult.success) {
        return sendError(res, `API key Haravan không hợp lệ hoặc đã hết hạn: ${countResult.message}`, 400);
      }

      const totalProducts = countResult.count;

      // Nếu không có sản phẩm nào, trả về thông báo
      if (totalProducts === 0) {
        return sendSuccess(res, {
          count: 0,
          message: 'Không có sản phẩm nào để đồng bộ',
          existing: 0,
          new: 0,
          products: []
        }, 'Không có sản phẩm nào để đồng bộ');
      }

      // Kiểm tra sản phẩm đã tồn tại
      const checkResult = await checkHaravanProductsExistence(token, tenant_id, {
        limit,
        updated_at_min
      });

      if (!checkResult.success) {
        return sendError(res, `Lỗi khi kiểm tra sản phẩm từ Haravan: ${checkResult.message}`, 500);
      }

      // Trả về kết quả kiểm tra
      return sendSuccess(res, {
        count: totalProducts,
        message: checkResult.message,
        existing: checkResult.data?.existing || 0,
        new: checkResult.data?.new || 0,
        products: checkResult.data?.products || []
      }, checkResult.message);
    } catch (error: any) {
      console.error('Lỗi khi kiểm tra sản phẩm từ Haravan:', error);
      return sendError(res, `Lỗi khi kiểm tra sản phẩm từ Haravan: ${error.message}`, 500);
    }
  } catch (error: any) {
    console.error('Lỗi khi xử lý request kiểm tra sản phẩm từ Haravan:', error);
    next(error);
  }
};

/**
 * Kiểm tra sản phẩm từ Sapo đã tồn tại
 * API này chỉ kiểm tra và trả về kết quả ngay lập tức, không thực hiện đồng bộ
 */
export const checkSapoProducts = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const {
      sapo_url,
      limit = 50,
      updated_at_min
    } = req.body;

    const tenant_id = req.body.tenant_id || req.headers['tenant-id'] || req.headers['tenant_id'];

    // Kiểm tra các trường bắt buộc
    if (!sapo_url) {
      return sendError(res, 'Thiếu URL Sapo đầy đủ', 400);
    }

    if (!tenant_id) {
      return sendError(res, 'Thiếu thông tin tenant_id', 400);
    }


    try {
      // Kiểm tra API key bằng cách gọi API lấy số lượng sản phẩm
      const countResult = await sapoProductService.getProductCount(sapo_url, {
        updated_at_min: updated_at_min
      });

      if (!countResult.success) {
        return sendError(res, `API key Sapo không hợp lệ hoặc đã hết hạn: ${countResult.message}`, 400);
      }

      const totalProducts = countResult.count;

      // Nếu không có sản phẩm nào, trả về thông báo
      if (totalProducts === 0) {
        return sendSuccess(res, {
          count: 0,
          message: 'Không có sản phẩm nào để đồng bộ',
          existing: 0,
          new: 0,
          products: []
        }, 'Không có sản phẩm nào để đồng bộ');
      }

      // Kiểm tra sản phẩm đã tồn tại
      const checkResult = await checkSapoProductsExistence(sapo_url, tenant_id, {
        limit,
        updated_at_min
      });

      if (!checkResult.success) {
        return sendError(res, `Lỗi khi kiểm tra sản phẩm từ Sapo: ${checkResult.message}`, 500);
      }

      // Trả về kết quả kiểm tra
      return sendSuccess(res, {
        count: totalProducts,
        message: checkResult.message,
        existing: checkResult.data?.existing || 0,
        new: checkResult.data?.new || 0,
        products: checkResult.data?.products || []
      }, checkResult.message);
    } catch (error: any) {
      console.error('Lỗi khi kiểm tra sản phẩm từ Sapo:', error);
      return sendError(res, `Lỗi khi kiểm tra sản phẩm từ Sapo: ${error.message}`, 500);
    }
  } catch (error: any) {
    console.error('Lỗi khi xử lý request kiểm tra sản phẩm từ Sapo:', error);
    next(error);
  }
};

/**
 * Kiểm tra sản phẩm từ nền tảng (API chung)
 * API này chỉ kiểm tra và trả về kết quả ngay lập tức, không thực hiện đồng bộ
 */
export const checkProducts = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const {
      platform,
      auth_token,
      limit = 50,
      updated_at_min
    } = req.body;
    const tenant_id = req.body.tenant_id || req.headers['tenant_id'];

    // Kiểm tra các trường bắt buộc
    if (!auth_token) {
      return sendError(res, 'Thiếu thông tin xác thực', 400);
    }

    if (!tenant_id) {
      return sendError(res, 'Thiếu thông tin tenant_id', 400);
    }

    if (!platform) {
      return sendError(res, 'Thiếu thông tin nền tảng', 400);
    }

    // Kiểm tra nền tảng hợp lệ
    if (platform !== 'haravan' && platform !== 'sapo') {
      return sendError(res, 'Nền tảng không được hỗ trợ. Chỉ hỗ trợ: haravan, sapo', 400);
    }

    try {
      let countResult;

      // Lấy số lượng sản phẩm từ nền tảng tương ứng
      if (platform === 'haravan') {
        countResult = await haravanProductService.getProductCount(auth_token, {
          updated_at_min: updated_at_min
        });
      } else if (platform === 'sapo') {
        countResult = await sapoProductService.getProductCount(auth_token, {
          updated_at_min: updated_at_min
        });
      } else {
        return sendError(res, `Nền tảng ${platform} không được hỗ trợ`, 400);
      }

      if (!countResult.success) {
        return sendError(res, `API key ${platform} không hợp lệ hoặc đã hết hạn: ${countResult.message}`, 400);
      }

      const totalProducts = countResult.count;

      // Nếu không có sản phẩm nào, trả về thông báo
      if (totalProducts === 0) {
        return sendSuccess(res, {
          count: 0,
          message: 'Không có sản phẩm nào để đồng bộ',
          existing: 0,
          new: 0,
          products: []
        }, 'Không có sản phẩm nào để đồng bộ');
      }

      // Kiểm tra sản phẩm đã tồn tại
      const checkResult = await checkProductsExistence(platform, auth_token, tenant_id, {
        limit,
        updated_at_min
      });

      if (!checkResult.success) {
        return sendError(res, `Lỗi khi kiểm tra sản phẩm từ ${platform}: ${checkResult.message}`, 500);
      }

      // Trả về kết quả kiểm tra
      return sendSuccess(res, {
        count: totalProducts,
        message: checkResult.message,
        existing: checkResult.data?.existing || 0,
        new: checkResult.data?.new || 0,
        products: checkResult.data?.products || []
      }, checkResult.message);
    } catch (error: any) {
      console.error(`Lỗi khi kiểm tra sản phẩm từ ${platform}:`, error);
      return sendError(res, `Lỗi khi kiểm tra sản phẩm từ ${platform}: ${error.message}`, 500);
    }
  } catch (error: any) {
    console.error('Lỗi khi kiểm tra sản phẩm:', error);
    next(error);
  }
};

export default {
  syncProducts,
  syncHaravanProducts,
  syncSapoProducts,
  getSyncStatus,
  checkProducts,
  checkHaravanProducts,
  checkSapoProducts
};
