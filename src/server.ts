import express, { Request, Response } from "express";
import cors from "cors";
import bodyParser from "body-parser";
import dotenv from "dotenv";
import routes from "./routes/index";
import { errorMiddleware } from "./middlewares/index";
import { getWeaviateClientInstance } from "./config/weaviate";
import { initializeBullMQSystem, shutdownBullMQSystem, createHealthCheckEndpoint, createMetricsEndpoint, productSyncQueue } from "./services/queue";
import { getBullBoardRouter } from "./services/queue/bull-board.service";
import { bullBoardDevMiddleware } from "./middlewares/bull-board-auth.middleware";
import { mastra } from "./mastra";
import { z } from "zod";

// Khởi tạo biến môi trường
dotenv.config();

const PORT = process.env.PORT || 3003;
const NODE_ENV = process.env.NODE_ENV || 'development';
const IS_PRODUCTION = NODE_ENV === 'production';

/**
 * Tạo Express App với tất cả middleware và routes
 */
const createApp = () => {
  const app = express();

  // Middleware cơ bản
  app.use(cors());
  app.use(bodyParser.json());

  // Route mặc định
  app.get("/", (_req: Request, res: Response) => {
    res.json({
      message: "Mooly Chatbot AI Backend",
      version: "1.0.0",
      status: "running",
      timestamp: new Date().toISOString()
    });
  });

  app.post('/test/agent', async (req, res) => {
    const ragAgent = mastra.getAgent("ragAgent")

    const intentAgent = mastra.getAgent("intelligentCustomerServiceAgent")

    const agentMemory = ragAgent.getMemory();

    const memoryRecent = await agentMemory?.query({
      threadId: "inbox_253_conv_2",
      resourceId: "5724526654280000",
      selectBy: {
        last: 3,
      }
    })
    const analysis = await intentAgent.generate([
      {
        role: "user",
        content: `Danh sách 3 tin nhắn gần nhất:
        ${memoryRecent}
        ---
        Tin nhắn mới nhất: có được thiết kế theo yêu cầu ko
        `
      }
    ], {
      output: z.array(z.string()).describe('Danh sách các topic cần tìm kiếm trong Faqs để có đầy đủ thông tin chính xác trả lời câu hỏi của khách hàng'),
      temperature: 0.1,
      // instructions: `Phân tích ý định và cảm xúc trong tin nhắn: "${req.body.message}"`
    });

    res.json({
      message: "Test route is working",
      timestamp: new Date().toISOString(),
      // thread: await agentMemory?.query({
      //   threadId: "inbox_253_conv_2",
      //   resourceId: "5724526654280000",
      //   selectBy: {
      //     last: 5,
      //   }
      // }),
      analysis: analysis.object
    });
  })

  // Health check endpoint - Cập nhật để bao gồm BullMQ
  app.get("/health", createHealthCheckEndpoint(productSyncQueue));
  
  // Metrics endpoint cho monitoring
  app.get("/metrics", createMetricsEndpoint());

  // Bull Board Dashboard - Đặt trước routes chính để tránh conflict
  try {
    app.use('/admin/queues', bullBoardDevMiddleware, getBullBoardRouter());
  } catch (error) {
    console.error('❌ Lỗi khi khởi tạo Bull Board:', error);
  }

  // Tích hợp Mastra (sẽ được cấu hình sau nếu cần)
  // app.use("/api/mastra", mastra);

  // Routes chính
  app.use("/api", routes);

  // Error handling middleware (luôn đặt ở cuối)
  app.use(errorMiddleware);

  return app;
};

/**
 * Kiểm tra kết nối với các services
 */
const checkConnections = async () => {
  const checks = [];

  // Kiểm tra Weaviate
  try {
    await getWeaviateClientInstance();
    checks.push({ service: "Weaviate", status: "✅ Connected" });
  } catch (error) {
    checks.push({ service: "Weaviate", status: "⚠️ Disconnected", error: error instanceof Error ? error.message : "Unknown error" });
  }

  // Log kết quả
  console.log("\n🔍 Service Connection Status:");
  checks.forEach(check => {
    if (check.error) {
    }
  });

  return checks;
};

/**
 * Khởi động server
 */
const startServer = async () => {
  try {

    // Kiểm tra kết nối
    await checkConnections();

    // Kiểm tra chế độ chạy BullMQ
    const isBullMQDisabled = process.env.DISABLE_BULLMQ === 'true';
    const isWorkerMode = process.env.WORKER_MODE === 'true';
    const isIntegratedMode = process.env.INTEGRATED_MODE === 'true';

    if (isBullMQDisabled) {
    } else if (isWorkerMode) {
      return; // Không khởi động Express server
    } else if (isIntegratedMode) {
    } else {
      // Khởi động BullMQ system
      try {
        await initializeBullMQSystem();
      } catch (error: any) {
        console.error('❌ Lỗi khi khởi động BullMQ workers:', error);

        // Kiểm tra xem có phải lỗi kết nối Redis/Dragonfly không
        const isRedisConnectionError =
          error.message && (
            error.message.includes('ECONNREFUSED') ||
            error.message.includes('connection is closed') ||
            error.message.includes('Redis connection lost') ||
            error.message.includes('Dragonfly connection lost') ||
            error.message.includes('Timeout') ||
            error.message.includes('timed out')
          );

        if (isRedisConnectionError) {
        }

        // Tiếp tục khởi động server ngay cả khi worker không khởi động được
      }
    }

    // Tạo Express app
    const app = createApp();

    // Khởi động server
    const server = app.listen(PORT, () => {
             console.log(`\n🎉 Server is running successfully!`);
       console.log(`\n⚡ Ready to serve requests!\n`);
    });

    // Xử lý tắt server một cách an toàn
    const gracefulShutdown = async () => {
      console.log("\n🛑 Received shutdown signal, closing server gracefully...");
      
      // Tắt BullMQ system trước
      try {
        await shutdownBullMQSystem();
      } catch (error) {
        console.error("❌ Lỗi khi tắt BullMQ system:", error);
      }
      
      server.close(() => {
        console.log("✅ Server closed successfully");
        process.exit(0);
      });

      // Force close after 10 seconds
      setTimeout(() => {
        process.exit(1);
      }, 10000);
    };

    process.on("SIGTERM", gracefulShutdown);
    process.on("SIGINT", gracefulShutdown);

    return server;

  } catch (error) {
    console.error("❌ Failed to start server:", error);
    process.exit(1);
  }
};

// Khởi động server
startServer().catch((error) => {
  console.error("❌ Unhandled error during startup:", error);
  process.exit(1);
});