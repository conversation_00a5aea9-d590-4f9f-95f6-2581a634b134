import axios from 'axios';

// Base64 của một hình ảnh placeholder đơn giản (1x1 pixel transparent PNG)
export const PLACEHOLDER_IMAGE_BASE64 = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=';

/**
 * Chuyển đổi URL hình ảnh thành chuỗi base64
 * @param imageUrl URL của hình ảnh cần chuyển đổi
 * @param defaultBase64 Chuỗi base64 mặc định nếu không thể chuyển đổi
 * @returns Promise<string> Chuỗi base64 của hình ảnh hoặc chuỗi mặc định
 */
export const imageUrlToBase64 = async (imageUrl: string, defaultBase64?: string): Promise<string> => {
  try {
    // Kiểm tra nếu URL không hợp lệ
    if (!imageUrl || !imageUrl.trim()) {
      console.warn('URL hình ảnh không hợp lệ, sử dụng base64 mặc định');
      if (defaultBase64) return defaultBase64;
      throw new Error('URL hình ảnh không hợp lệ');
    }

    // Thiết lập timeout để tránh chờ quá lâu
    const response = await axios.get(imageUrl, {
      responseType: 'arraybuffer',
      timeout: 10000, // 10 giây timeout
      maxContentLength: 10 * 1024 * 1024, // Giới hạn kích thước 10MB
      headers: {
        'Accept': 'image/*'
      }
    });

    // Chuyển đổi dữ liệu nhận được thành base64
    const base64 = Buffer.from(response.data, 'binary').toString('base64');

    // Trả về chuỗi base64
    return base64;
  } catch (error: any) {
    console.warn(`Lỗi khi chuyển đổi hình ảnh sang base64 (${imageUrl}):`, error.message);

    // Nếu có base64 mặc định, trả về nó thay vì ném lỗi
    if (defaultBase64) return defaultBase64;

    // Nếu không có base64 mặc định, ném lỗi
    throw new Error(`Không thể chuyển đổi hình ảnh sang base64: ${error.message || 'Lỗi không xác định'}`);
  }
};

/**
 * Kiểm tra URL hình ảnh có hợp lệ không
 * @param imageUrl URL của hình ảnh cần kiểm tra
 * @returns Promise<boolean> true nếu URL hợp lệ, false nếu không
 */
export const isValidImageUrl = async (imageUrl: string): Promise<boolean> => {
  try {
    if (!imageUrl || !imageUrl.trim()) return false;

    // Chỉ gửi request HEAD để kiểm tra URL mà không tải toàn bộ hình ảnh
    const response = await axios.head(imageUrl, {
      timeout: 5000, // 5 giây timeout
      headers: {
        'Accept': 'image/*'
      }
    });

    // Kiểm tra status code và content-type
    const contentType = response.headers['content-type'] || '';
    return response.status === 200 && contentType.startsWith('image/');
  } catch (error) {
    return false;
  }
};
