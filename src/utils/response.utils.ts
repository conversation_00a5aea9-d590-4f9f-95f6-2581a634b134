import { Response } from 'express';

/**
 * Tr<PERSON> về response thành công
 */
export const sendSuccess = (res: Response, data: any, message = 'Thành công', statusCode = 200) => {
  return res.status(statusCode).json({
    success: true,
    message,
    data
  });
};

/**
 * Trả về response lỗi
 */
export const sendError = (res: Response, message: string, statusCode = 500, error?: any) => {
  return res.status(statusCode).json({
    success: false,
    message,
    error: process.env.NODE_ENV === 'production' ? undefined : error
  });
};

/**
 * Trả về response lỗi 400 Bad Request
 */
export const sendBadRequest = (res: Response, message = 'Yêu cầu không hợp lệ', error?: any) => {
  return sendError(res, message, 400, error);
};

/**
 * Trả về response lỗi 401 Unauthorized
 */
export const sendUnauthorized = (res: Response, message = 'Không được phép truy cập', error?: any) => {
  return sendError(res, message, 401, error);
};

/**
 * Tr<PERSON> về response lỗi 404 Not Found
 */
export const sendNotFound = (res: Response, message = 'Không tìm thấy tài nguyên', error?: any) => {
  return sendError(res, message, 404, error);
};

/**
 * Extract nội dung từ thẻ <final_answer> trong response của AI
 * Bỏ qua phần <thinking> và các nội dung khác để chỉ lấy final answer gửi cho khách hàng
 * Luôn luôn xóa tag <thinking> để đảm bảo response thân thiện với user
 */
export const extractFinalAnswer = (aiResponse: string): string => {
  if (!aiResponse || typeof aiResponse !== 'string') {
    return aiResponse || '';
  }

  // Tìm thẻ <final_answer>
  const finalAnswerMatch = aiResponse.match(/<final_answer>([\s\S]*?)<\/final_answer>/i);

  if (finalAnswerMatch && finalAnswerMatch[1]) {
    // Trả về nội dung trong thẻ final_answer, loại bỏ khoảng trắng thừa
    return finalAnswerMatch[1].trim();
  }

  // Nếu không tìm thấy thẻ final_answer, xóa tag <thinking> và trả về phần còn lại
  let cleanedResponse = aiResponse;

  // Xóa toàn bộ nội dung trong thẻ <thinking>
  cleanedResponse = cleanedResponse.replace(/<thinking>[\s\S]*?<\/thinking>/gi, '');

  // Xóa các tag thinking rỗng hoặc không đóng đúng cách
  cleanedResponse = cleanedResponse.replace(/<\/?thinking[^>]*>/gi, '');

  // Loại bỏ khoảng trắng thừa và trả về
  return cleanedResponse.trim();
};
