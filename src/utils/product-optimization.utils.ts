/**
 * Utility functions để tối ưu hóa dữ liệu sản phẩm cho LLM
 * Tiết kiệm token và cung cấp thông tin chính xác cho tư vấn khách hàng
 */

export interface PriceInfo {
  price: number;
  original_price?: number;
  discount_percent?: number;
  is_on_sale?: boolean;
}

export interface OptimizedVariant {
  id: string;
  name: string;
  price: number;
  original_price?: number;
  discount_percent?: number;
  is_on_sale?: boolean;
  stock_quantity: number;
  sku?: string;
  attributes?: {
    color?: string;
    size?: string;
    style?: string;
  };
}

export interface OptimizedProduct {
  id: string;
  name: string;
  price: number;
  original_price?: number;
  discount_percent?: number;
  is_on_sale?: boolean;
  stock_quantity: number;
  short_description: string;
  avatar?: string;
  images?: string[];
  type?: string;
  sku?: string;
  is_active: boolean;
  variants?: OptimizedVariant[];
}

/**
 * Tính toán thông tin giá cả tối ưu cho LLM
 * @param price Giá hiện tại
 * @param comparePrice Giá so sánh (gi<PERSON> gốc)
 * @returns Thông tin giá đã được tối ưu
 */
export const calculatePriceInfo = (price: number, comparePrice?: number): PriceInfo => {
  const finalPrice = Number(price) || 0;
  const originalPrice = comparePrice ? Number(comparePrice) : undefined;
  
  // Nếu không có giá so sánh hoặc giá so sánh <= giá hiện tại
  if (!originalPrice || originalPrice <= finalPrice) {
    return { price: finalPrice };
  }
  
  // Tính % giảm giá
  const discountPercent = Math.round(((originalPrice - finalPrice) / originalPrice) * 100);
  
  return {
    price: finalPrice,
    original_price: originalPrice,
    discount_percent: discountPercent,
    is_on_sale: true
  };
};

/**
 * Tối ưu hóa thông tin variants - chỉ giữ variants active và có stock
 * @param variants Danh sách variants gốc
 * @returns Danh sách variants đã được tối ưu
 */
export const optimizeVariants = (variants: any[]): OptimizedVariant[] | undefined => {
  if (!Array.isArray(variants)) return undefined;
  
  const activeVariants = variants
    .filter(variant => 
      variant && 
      variant.id && 
      variant.is_active !== false && 
      Number(variant.stock_quantity) > 0
    )
    .map(variant => {
      const priceInfo = calculatePriceInfo(variant.price, variant.compare_at_price);
      return {
        id: String(variant.id),
        name: String(variant.name || "Variant"),
        ...priceInfo,
        stock_quantity: Number(variant.stock_quantity) || 0,
        sku: variant.sku ? String(variant.sku) : undefined,
        // Chỉ giữ attributes cơ bản (color, size, style)
        attributes: variant.attributes ? {
          color: variant.attributes.color,
          size: variant.attributes.size,
          style: variant.attributes.style
        } : undefined
      };
    });
    
  return activeVariants.length > 0 ? activeVariants : undefined;
};

/**
 * Tối ưu hóa mô tả sản phẩm
 * @param shortDescription Mô tả ngắn
 * @param description Mô tả đầy đủ
 * @param maxLength Độ dài tối đa
 * @returns Mô tả đã được tối ưu
 */
export const optimizeDescription = (
  shortDescription?: string, 
  description?: string, 
  maxLength: number = 150
): string => {
  if (shortDescription && shortDescription.trim()) {
    return shortDescription.length > maxLength 
      ? shortDescription.substring(0, maxLength).trim() + "..."
      : shortDescription.trim();
  }
  
  if (description && description.trim()) {
    return description.length > maxLength 
      ? description.substring(0, maxLength).trim() + "..."
      : description.trim();
  }
  
  return "Không có mô tả";
};

/**
 * Tối ưu hóa danh sách hình ảnh
 * @param images Danh sách hình ảnh gốc
 * @param maxImages Số lượng hình ảnh tối đa
 * @returns Danh sách hình ảnh đã được tối ưu
 */
export const optimizeImages = (images: any, maxImages: number = 2): string[] | undefined => {
  if (!images) return undefined;
  
  let imageList: string[] = [];
  
  if (Array.isArray(images)) {
    imageList = images.filter(img => img && typeof img === 'string' && img.trim());
  } else if (typeof images === 'string' && images.trim()) {
    imageList = [images];
  }
  
  return imageList.length > 0 ? imageList.slice(0, maxImages) : undefined;
};

/**
 * Tối ưu hóa dữ liệu sản phẩm đầy đủ
 * @param products Danh sách sản phẩm gốc
 * @returns Danh sách sản phẩm đã được tối ưu
 */
export const optimizeProductData = (products: any[]): OptimizedProduct[] => {
  if (!Array.isArray(products)) {
    return [];
  }

  return products
    .filter(product => product && product.id && product.is_active !== false)
    .map(product => {
      const priceInfo = calculatePriceInfo(product.price, product.compare_at_price);
      const optimizedVariants = optimizeVariants(product.variants);
      
      return {
        id: String(product.id),
        name: String(product.name || "Không có tên"),
        ...priceInfo,
        stock_quantity: Number(product.stock_quantity) || 0,
        short_description: optimizeDescription(
          product.short_description, 
          product.description || product.description_preview
        ),
        avatar: product.avatar ? String(product.avatar) : undefined,
        images: optimizeImages(product.images),
        type: product.type ? String(product.type) : undefined,
        sku: product.sku ? String(product.sku) : undefined,
        is_active: true, // Đã filter ở trên
        variants: optimizedVariants
      };
    });
};

/**
 * Format thông tin giá cả cho hiển thị
 * @param priceInfo Thông tin giá
 * @returns Chuỗi mô tả giá đã format
 */
export const formatPriceDisplay = (priceInfo: PriceInfo): string => {
  const { price, original_price, discount_percent, is_on_sale } = priceInfo;
  
  if (is_on_sale && original_price && discount_percent) {
    return `${price.toLocaleString('vi-VN')}đ (Giảm ${discount_percent}% từ ${original_price.toLocaleString('vi-VN')}đ)`;
  }
  
  return `${price.toLocaleString('vi-VN')}đ`;
};

/**
 * Kiểm tra sản phẩm có sẵn để bán không
 * @param product Thông tin sản phẩm
 * @returns true nếu có thể bán
 */
export const isProductAvailable = (product: OptimizedProduct): boolean => {
  if (!product.is_active) return false;
  
  // Nếu là sản phẩm đơn giản
  if (product.type !== 'variable') {
    return product.stock_quantity > 0;
  }
  
  // Nếu là sản phẩm có variants
  if (product.variants && product.variants.length > 0) {
    return product.variants.some(variant => variant.stock_quantity > 0);
  }
  
  return false;
};

/**
 * Lấy thông tin tồn kho tổng hợp
 * @param product Thông tin sản phẩm
 * @returns Thông tin tồn kho
 */
export const getStockInfo = (product: OptimizedProduct): {
  total_stock: number;
  available_variants: number;
  is_in_stock: boolean;
} => {
  if (product.type !== 'variable' || !product.variants) {
    return {
      total_stock: product.stock_quantity,
      available_variants: 0,
      is_in_stock: product.stock_quantity > 0
    };
  }
  
  const totalStock = product.variants.reduce((sum, variant) => sum + variant.stock_quantity, 0);
  const availableVariants = product.variants.filter(variant => variant.stock_quantity > 0).length;
  
  return {
    total_stock: totalStock,
    available_variants: availableVariants,
    is_in_stock: totalStock > 0
  };
};
