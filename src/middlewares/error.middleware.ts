import { Request, Response, NextFunction } from 'express';

/**
 * Middleware xử lý lỗi chung cho toàn bộ ứng dụng
 */
export const errorMiddleware = (
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  console.error('Lỗi:', error);

  // Kiểm tra loại lỗi và trả về response phù hợp
  if (error.name === 'ValidationError') {
    return res.status(400).json({
      success: false,
      message: 'Lỗi xác thực dữ liệu',
      error: error.message
    });
  }

  // Lỗi mặc định
  return res.status(500).json({
    success: false,
    message: 'Lỗi máy chủ nội bộ',
    error: process.env.NODE_ENV === 'production' ? 'Đã xảy ra lỗi' : error.message
  });
};
