import { Request, Response, NextFunction } from 'express';
import { AnyZodObject } from 'zod';

/**
 * Middleware xác thực dữ liệu sử dụng Zod
 * @param schema Schema Zod để xác thực
 */
export const validate = (schema: AnyZodObject) => 
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      // Xác thực dữ liệu từ request
      await schema.parseAsync({
        body: req.body,
        query: req.query,
        params: req.params
      });
      
      return next();
    } catch (error) {
      // Trả về lỗi xác thực
      return res.status(400).json({
        success: false,
        message: 'Lỗi xác thực dữ liệu',
        error
      });
    }
  };
