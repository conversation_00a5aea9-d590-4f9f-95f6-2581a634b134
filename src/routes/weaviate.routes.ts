import { Router } from "express";
import { weaviateController } from "../controllers";
import { validate } from "../middlewares";
import { weaviateValidator } from "../validators";

const router = Router();

/**
 * @route POST /api/weaviate/faqs/init
 * @desc Khởi tạo collection Faqs trong Weaviate
 */
router.post("/faqs/init", weaviateController.initFaqs);

/**
 * @route POST /api/weaviate/faqs
 * @desc Thêm nhiều FAQ mới vào collection
 */
router.post(
  "/faqs",
  // validate(weaviateValidator.createFaqSchema),
  weaviateController.createFaqs
);

/**
 * @route GET /api/weaviate/faqs/search
 * @desc Tìm kiếm FAQ dựa trên nội dung
 */
router.get(
  "/faqs/search",
  validate(weaviateValidator.searchFaqSchema),
  weaviateController.searchFaq
);

/**
 * @route PUT /api/weaviate/faqs/update
 * @desc Cập nhật FAQ theo supabase_id
 */
router.put(
  "/faqs/update",
  validate(weaviateValidator.updateFaqBySupabaseIdSchema),
  weaviateController.updateFaqBySupabaseId
);

/**
 * @route DELETE /api/weaviate/faqs/delete
 * @desc Xóa FAQ theo supabase_id
 */
router.delete(
  "/faqs/delete",
  validate(weaviateValidator.deleteFaqBySupabaseIdSchema),
  weaviateController.deleteFaqBySupabaseId
);

/**
 * @route DELETE /api/weaviate/faqs/delete-many
 * @desc Xóa nhiều FAQ theo danh sách supabase_id
 */
router.delete(
  "/faqs/delete-many",
  validate(weaviateValidator.deleteFaqsBySupabaseIdsSchema),
  weaviateController.deleteFaqsBySupabaseIds
);

/**
 * @route POST /api/weaviate/products/init
 * @desc Khởi tạo collection Products trong Weaviate
 */
router.post("/products/init", weaviateController.initProducts);

/**
 * @route POST /api/weaviate/products
 * @desc Thêm một sản phẩm mới vào collection
 */
router.post(
  "/products",
  validate(weaviateValidator.createProductSchema),
  weaviateController.createProduct
);

/**
 * @route POST /api/weaviate/products/batch
 * @desc Thêm nhiều sản phẩm cùng lúc vào collection
 */
router.post(
  "/products/batch",
  // validate(weaviateValidator.createProductsSchema),
  weaviateController.createProducts
);

/**
 * @route GET /api/weaviate/products/search
 * @desc Tìm kiếm sản phẩm dựa trên nội dung
 */
router.get(
  "/products/search",
  validate(weaviateValidator.searchProductSchema),
  weaviateController.searchProduct
);

/**
 * @route DELETE /api/weaviate/products/filter
 * @desc Xóa nhiều sản phẩm theo bộ lọc
 */
router.delete(
  "/products/filter",
  weaviateController.deleteProductsByFilterHandler
);

/**
 * @route PUT /api/weaviate/products/update
 * @desc Cập nhật thông tin sản phẩm dựa trên image_url
 */
router.put(
  "/products/update",
  // validate(weaviateValidator.updateProductSchema),
  weaviateController.updateProductByImageUrl
);

/**
 * @route DELETE /api/weaviate/products/delete
 * @desc Xóa sản phẩm dựa trên image_url
 */
router.delete(
  "/products/delete",
  weaviateController.deleteProductByImageUrl
);

/**
 * @route DELETE /api/weaviate/products/delete-by-id
 * @desc Xóa sản phẩm dựa trên product_id
 */
router.delete(
  "/products/delete-by-id",
  weaviateController.deleteProductByProductIdHandler
);

/**
 * @route DELETE /api/weaviate/products/delete-by-ids
 * @desc Xóa nhiều sản phẩm dựa trên danh sách product_id
 */
router.delete(
  "/products/delete-by-ids",
  validate(weaviateValidator.deleteProductsByIdsSchema),
  weaviateController.deleteProductsByProductIdsHandler
);

/**
 * @route PUT /api/weaviate/products/update-by-id
 * @desc Cập nhật thông tin sản phẩm dựa trên product_id
 */
router.put(
  "/products/update-by-id",
  weaviateController.updateProductByProductIdHandler
);

/**
 * @route PUT /api/weaviate/products/update-bot-id
 * @desc Cập nhật trường bot_id của sản phẩm (thêm hoặc xóa bot_id)
 */
router.put(
  "/products/update-bot-id",
  validate(weaviateValidator.updateProductBotIdSchema),
  weaviateController.updateProductBotIdHandler
);

export default router;
