import { Router } from 'express';
import { customerController } from '../controllers';
import { validate } from '../middlewares';
import { customerValidator } from '../validators';

const router = Router();

/**
 * @route GET /api/customers/:customer_id/addresses
 * @desc Lấy danh sách địa chỉ của khách hàng
 */
router.get(
  '/:customer_id/addresses',
  customerController.getCustomerAddresses
);

/**
 * @route GET /api/customers/addresses/:address_id
 * @desc Lấy thông tin chi tiết của một địa chỉ
 */
router.get(
  '/addresses/:address_id',
  customerController.getAddressDetails
);

/**
 * @route POST /api/customers/addresses
 * @desc Tạo địa chỉ mới cho khách hàng
 */
router.post(
  '/addresses',
  validate(customerValidator.createAddressSchema),
  customerController.createCustomerAddress
);

/**
 * @route PUT /api/customers/addresses/:address_id
 * @desc Cập nhật thông tin địa chỉ
 */
router.put(
  '/addresses/:address_id',
  validate(customerValidator.updateAddressSchema),
  customerController.updateCustomerAddress
);

/**
 * @route DELETE /api/customers/addresses/:address_id
 * @desc Xóa địa chỉ
 */
router.delete(
  '/addresses/:address_id',
  customerController.deleteCustomerAddress
);

export default router;
