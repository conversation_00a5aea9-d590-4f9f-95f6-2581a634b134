import { Router } from 'express';
import * as platformSyncController from '../controllers/platform-sync.controller';
import { validate } from '../middlewares';
import { platformSyncValidator } from '../validators';

const router = Router();

/**
 * @route POST /api/sync/products
 * @desc Đồng bộ sản phẩm từ nền tảng (API chung)
 */
router.post(
  '/products',
  validate(platformSyncValidator.syncProductsSchema),
  platformSyncController.syncProducts
);

/**
 * @route POST /api/sync/haravan/products
 * @desc Đồng bộ sản phẩm từ <PERSON> (giữ lại để tương thích ngược)
 */
router.post(
  '/haravan/products',
  validate(platformSyncValidator.syncHaravanProductsSchema),
  platformSyncController.syncHaravanProducts
);

/**
 * @route POST /api/sync/sapo/products
 * @desc Đồng bộ sản phẩm từ <PERSON>po (giữ lại để tương thích ngược)
 */
router.post(
  '/sapo/products',
  validate(platformSyncValidator.syncSapoProductsSchema),
  platformSyncController.syncSapoProducts
);

/**
 * @route GET /api/sync/status
 * @desc Kiểm tra trạng thái đồng bộ
 */
router.get('/status', platformSyncController.getSyncStatus);

/**
 * @route POST /api/sync/check/products
 * @desc Kiểm tra sản phẩm từ nền tảng đã tồn tại (API chung)
 */
router.post(
  '/check/products',
  validate(platformSyncValidator.syncProductsSchema),
  platformSyncController.checkProducts
);

/**
 * @route POST /api/sync/check/haravan/products
 * @desc Kiểm tra sản phẩm từ Haravan đã tồn tại
 */
router.post(
  '/check/haravan/products',
  validate(platformSyncValidator.syncHaravanProductsSchema),
  platformSyncController.checkHaravanProducts
);

/**
 * @route POST /api/sync/check/sapo/products
 * @desc Kiểm tra sản phẩm từ Sapo đã tồn tại
 */
router.post(
  '/check/sapo/products',
  validate(platformSyncValidator.syncSapoProductsSchema),
  platformSyncController.checkSapoProducts
);

export default router;
