import { Router } from 'express';
import { agent<PERSON>ontroller } from '../controllers';
import { validate } from '../middlewares';
import { agentValidator } from '../validators';

const router = Router();

/**
 * @route POST /api/agent/generate
 * @desc T<PERSON><PERSON> phản hồi từ agent
 */
router.post(
  '/generate',
  // validate(agentValidator.messageSchema),
  agentController.generateResponse
);


export default router;
