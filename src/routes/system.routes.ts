import { Router } from 'express';
import { systemController } from '../controllers';
import {
  getMessageBatchingStats,
  getMessageBufferConnectionStatus,
  cleanupExpiredBatches,
  // updateBotBatchingConfig,
  getChatbotConfig,
  getAllQueuesStatsAPI,
  cleanupAllQueuesAPI,
  pauseAllQueuesAPI,
  resumeAllQueuesAPI,
  getBullBoardHealthAPI,
} from '../controllers/system.controller';

const router = Router();

/**
 * @route GET /api/system/status
 * @desc Kiểm tra trạng thái hệ thống
 */
router.get('/status', systemController.getSystemStatus);

/**
 * @route GET /api/system/supabase
 * @desc Kiểm tra kết nối Supabase
 */
router.get('/supabase', systemController.checkSupabase);

/**
 * @route GET /api/system/supabase-admin
 * @desc Kiểm tra kết nối Supabase Admin
 */
router.get('/supabase-admin', systemController.checkSupabaseAdmin);

// Message Batching Monitoring Routes
router.get('/message-batching/stats', getMessageBatchingStats);
router.get('/message-batching/connection', getMessageBufferConnectionStatus);
router.post('/message-batching/cleanup', cleanupExpiredBatches);
// router.put('/message-batching/config', updateBotBatchingConfig);

// Chatbot Configuration Routes
router.get('/chatbot/config/:inbox_id', getChatbotConfig);

// Bull Board Management Routes
router.get('/queues/stats', getAllQueuesStatsAPI);
router.post('/queues/cleanup', cleanupAllQueuesAPI);
router.post('/queues/pause', pauseAllQueuesAPI);
router.post('/queues/resume', resumeAllQueuesAPI);
router.get('/queues/health', getBullBoardHealthAPI);

export default router;
