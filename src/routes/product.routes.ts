import { Router } from 'express';
import { productController } from '../controllers';

const router = Router();

/**
 * @route GET /api/products/:product_id
 * @desc Lấy thông tin chi tiết của một sản phẩm
 */
router.get(
  '/:product_id',
  productController.getProductDetails
);

/**
 * @route GET /api/products/orders/:order_code
 * @desc Lấy thông tin đơn hàng theo order_code
 */
router.get(
  '/orders/:order_code',
  productController.getOrderByCode
);

export default router;
