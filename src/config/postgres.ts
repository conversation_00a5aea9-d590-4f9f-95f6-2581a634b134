import postgres from 'postgres';
import dotenv from 'dotenv';
import { Logger } from './logging.config';

// Đảm bảo biến môi trường được load
dotenv.config();

// C<PERSON>u hình kết nối PostgreSQL từ DATABASE_URL hoặc các biến riêng lẻ
const connectionString = process.env.DATABASE_URL || 'postgresql://postgres:<EMAIL>:5432/postgres';

// Tạo và export PostgreSQL client
export const sql = postgres(connectionString, {
  ssl: { rejectUnauthorized: false }, // Bật SSL nhưng bỏ qua xác thực chứng chỉ
  max: 10, // Số lượng kết nối tối đa trong pool
  idle_timeout: 30, // Thời gian timeout cho kết nối không hoạt động (giây)
  transform: {
    undefined: null, // Chuyển đổi undefined thành null
  },
});

// Kiểm tra kết nối PostgreSQL
export const checkPostgresConnection = async () => {
  try {
    // Thực hiện truy vấn đơn giản để kiểm tra kết nối
    const result = await sql`SELECT 1 as connection_test`;

    if (result && result.length > 0) {
      Logger.info('Kết nối PostgreSQL thành công');
      return {
        success: true,
        message: 'Kết nối PostgreSQL thành công',
      };
    } else {
      Logger.error('Lỗi kết nối PostgreSQL: Không nhận được kết quả');
      return {
        success: false,
        message: 'Lỗi kết nối PostgreSQL: Không nhận được kết quả',
      };
    }
  } catch (error: any) {
    Logger.error('Lỗi kết nối PostgreSQL:', error);
    return {
      success: false,
      message: `Lỗi kết nối PostgreSQL: ${error?.message || 'Lỗi không xác định'}`,
    };
  }
};
