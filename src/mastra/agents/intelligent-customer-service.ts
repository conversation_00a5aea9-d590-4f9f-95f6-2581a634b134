import { openai } from "@ai-sdk/openai";
import { Agent } from "@mastra/core/agent";
import { Memory } from "@mastra/memory";
import { google } from "@ai-sdk/google";
import { PgVector } from "@mastra/pg";
import postgresStore from "../utils/postgres-store";
import dotenv from "dotenv";

// Import tất cả tools từ ecommerceAgent để tận dụng tối đa
import {
  searchProductsTool,
  getProductDetailsTool,
  trackOrderTool,
  getReturnPolicyTool,
  registerComplaintTool,
  humanHandoffTool,
  collectCustomerInfoTool,
  getFaqsTool,
  detectSpamTool,
  getOrderByCodeTool,
  updateOrderStatusTool,
  cancelOrderTool,
  getPromotionsTool,
  createOrderTool,
} from "../tools";

// Import system prompts chuyên biệt
import {
  intelligentCustomerServicePrompt,
  intelligentWorkingMemoryTemplate,
} from "../prompts/intelligent-customer-service";

dotenv.config();

// Khởi tạo PgVector client cho semantic search
const pgVectorClient = new PgVector({
  connectionString: process.env.PG_CONNECTION_STRING ||
    "postgresql://postgres:postgres@localhost:5432/postgres"
});

// Cấu hình Memory system tối ưu cho customer service
const intelligentMemory = new Memory({
  storage: postgresStore,
  
  // Sử dụng PgVector cho semantic search thông minh
  vector: pgVectorClient,
  embedder: openai.embedding("text-embedding-3-small"),
  
  options: {
    // Tăng số lượng tin nhắn gần đây để hiểu ngữ cảnh tốt hơn
    lastMessages: 15,
    
    // Cấu hình semantic recall mạnh mẽ hơn
    semanticRecall: {
      topK: 5,
      messageRange: 3,
    },
    
    // Bật tính năng tạo tiêu đề thread tự động
    threads: {
      generateTitle: true
    },
    
    // Working memory với template chuyên biệt
    workingMemory: {
      enabled: true,
      template: intelligentWorkingMemoryTemplate,
    },
  },
});

/**
 * INTELLIGENT CUSTOMER SERVICE AGENT
 * 
 * Agent chuyên biệt cho chăm sóc khách hàng thông minh:
 * - Tận dụng tất cả tools từ ecommerceAgent
 * - Memory system mạnh mẽ với semantic search
 * - System prompt được tối ưu cho customer service
 * - Khả năng phân tích cảm xúc và ý định khách hàng
 * - Workflow thông minh với human handoff
 */
export const intelligentCustomerServiceAgent = new Agent({
  name: "Intelligent Customer Service Agent",
  instructions: intelligentCustomerServicePrompt,
  model: google(process.env.LLM_MODEL || "gemini-2.0-flash-exp"),
  
  // Sử dụng tất cả tools từ ecommerceAgent để đảm bảo tính nhất quán
  tools: {
    // 🔍 PRODUCT TOOLS - Tìm kiếm và thông tin sản phẩm
    searchProductsTool,
    getProductDetailsTool,
    
    // 📦 ORDER TOOLS - Quản lý đơn hàng
    createOrderTool,
    trackOrderTool,
    getOrderByCodeTool,
    updateOrderStatusTool,
    cancelOrderTool,
    
    // 🎁 PROMOTION TOOLS - Khuyến mãi
    getPromotionsTool,
    
    // 🛡️ SUPPORT TOOLS - Hỗ trợ khách hàng
    getReturnPolicyTool,
    registerComplaintTool,
    humanHandoffTool,
    collectCustomerInfoTool,
    getFaqsTool,
    detectSpamTool,
  },
  
  // Memory system thông minh
  memory: intelligentMemory,
});

/**
 * Helper function để tạo context cho intelligent agent
 */
export const createIntelligentCustomerServiceContext = (params: {
  tenantId: string;
  botId?: string;
  customerId?: string;
  customerEmotion?: string;
  urgencyLevel?: string;
  customerIntent?: string;
  conversationStage?: string;
  language?: "vi" | "en";
}) => {
  return {
    "tenant-id": params.tenantId,
    "bot-id": params.botId || "",
    "customer-id": params.customerId || "",
    "customer-emotion": params.customerEmotion || "neutral",
    "urgency-level": params.urgencyLevel || "medium",
    "customer-intent": params.customerIntent || "inquiry",
    "conversation-stage": params.conversationStage || "initial",
    "language": params.language || "vi",
  };
};

/**
 * Helper function để phân tích cảm xúc và ý định khách hàng
 */
export const analyzeCustomerIntent = (message: string): {
  emotion: string;
  intent: string;
  urgency: string;
  complexity: string;
} => {
  const lowerMessage = message.toLowerCase();
  
  // Phân tích cảm xúc
  let emotion = "neutral";
  if (lowerMessage.includes("cảm ơn") || lowerMessage.includes("tuyệt vời") || lowerMessage.includes("hài lòng")) {
    emotion = "happy";
  } else if (lowerMessage.includes("tức giận") || lowerMessage.includes("khó chịu") || lowerMessage.includes("thất vọng")) {
    emotion = "angry";
  } else if (lowerMessage.includes("lo lắng") || lowerMessage.includes("không hiểu") || lowerMessage.includes("bối rối")) {
    emotion = "confused";
  }
  
  // Phân tích ý định
  let intent = "inquiry";
  if (lowerMessage.includes("mua") || lowerMessage.includes("đặt hàng") || lowerMessage.includes("order")) {
    intent = "purchase";
  } else if (lowerMessage.includes("theo dõi") || lowerMessage.includes("kiểm tra đơn") || lowerMessage.includes("trạng thái")) {
    intent = "order_tracking";
  } else if (lowerMessage.includes("khiếu nại") || lowerMessage.includes("phàn nàn") || lowerMessage.includes("vấn đề")) {
    intent = "complaint";
  } else if (lowerMessage.includes("đổi trả") || lowerMessage.includes("hoàn tiền") || lowerMessage.includes("return")) {
    intent = "return";
  } else if (lowerMessage.includes("tư vấn") || lowerMessage.includes("gợi ý") || lowerMessage.includes("recommend")) {
    intent = "consultation";
  }
  
  // Phân tích mức độ khẩn cấp
  let urgency = "medium";
  if (lowerMessage.includes("khẩn cấp") || lowerMessage.includes("gấp") || lowerMessage.includes("urgent")) {
    urgency = "high";
  } else if (lowerMessage.includes("không gấp") || lowerMessage.includes("từ từ")) {
    urgency = "low";
  }
  
  // Phân tích độ phức tạp
  let complexity = "simple";
  if (intent === "complaint" || intent === "return" || emotion === "angry") {
    complexity = "complex";
  } else if (intent === "purchase" || intent === "consultation") {
    complexity = "medium";
  }
  
  return { emotion, intent, urgency, complexity };
};

/**
 * Helper function để tạo response context cho agent
 */
export const createResponseContext = (
  analysis: ReturnType<typeof analyzeCustomerIntent>,
  tenantId: string,
  customerId?: string
) => {
  return createIntelligentCustomerServiceContext({
    tenantId,
    customerId,
    customerEmotion: analysis.emotion,
    urgencyLevel: analysis.urgency,
    customerIntent: analysis.intent,
    conversationStage: analysis.complexity === "complex" ? "escalation" : "active",
  });
};
