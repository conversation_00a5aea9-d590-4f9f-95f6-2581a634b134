# Loma Bag Workflow

Workflow tư vấn thông minh cho Loma Bag với tích hợp tìm kiếm FAQ để cung cấp context chính xác cho agent.

## Tổng quan

Workflow này được thiết kế để:

1. **Phân tích ngữ cảnh hội thoại**: Sử dụng AI để phân tích 5 tin nhắn gần nhất + tin nhắn mới để hiểu ý định khách hàng
2. **Tìm kiếm FAQ thông minh**: Tự động tạo keywords và tìm kiếm trong Weaviate FAQ database
3. **Tư vấn với context**: Cung cấp context từ FAQ cho Loma Bag agent để trả lời chính xác hơn

## Kiến trúc Workflow

```
Input: Recent Messages + New Message + Tenant/Bot ID
    ↓
Step 1: Keyword Analysis Agent
    - Phân tích 5 tin nhắn gần nhất + tin nhắn mới
    - Tạo 3-5 keywords phù hợp
    - <PERSON><PERSON><PERSON> đ<PERSON>nh chủ đề chính và ý định khách hàng
    ↓
Step 2: FAQ Search
    - Tìm kiếm FAQ trong Weaviate dựa trên keywords
    - Lấy tối đa 3 FAQ có score cao nhất
    - Loại bỏ duplicate và sắp xếp theo relevance
    ↓
Step 3: Response Generation
    - Tạo context từ FAQ results
    - Gọi Loma Bag Agent với context
    - Trả về response cuối cùng
    ↓
Output: Response + Keywords + FAQ Context + Analysis
```

## Input Schema

```typescript
{
  recentMessages: Array<{
    role: "user" | "assistant" | "system",
    content: string,
    timestamp?: string
  }>,
  newMessage: string,
  tenant_id: string,
  bot_id?: string
}
```

## Output Schema

```typescript
{
  response: string,                    // Câu trả lời từ Loma Bag agent
  keywords_used: string[],             // Keywords đã sử dụng để tìm kiếm
  faq_context: Array<{                 // FAQ context được tìm thấy
    topic: string,
    content: string,
    score?: number
  }>,
  main_topic: string,                  // Chủ đề chính của cuộc hội thoại
  customer_intent: string              // Ý định của khách hàng
}
```

## Cách sử dụng

### 1. Sử dụng trong code

```typescript
import { mastra } from "../mastra";

const workflowRun = mastra.getWorkflow("lomaBagWorkflow").createRun();

const result = await workflowRun.start({
  inputData: {
    recentMessages: [
      { role: "user", content: "Chào shop" },
      { role: "assistant", content: "Chào chị! Em là Diệu từ Loma Bag..." }
    ],
    newMessage: "Giá túi canvas size S bao nhiêu?",
    tenant_id: "your-tenant-id",
    bot_id: "your-bot-id"
  }
});

console.log(result.output.response);
```

### 2. Sử dụng qua API

```bash
POST /api/workflows/lomaBagWorkflow/run
Content-Type: application/json

{
  "inputData": {
    "recentMessages": [...],
    "newMessage": "Giá túi canvas size S bao nhiêu?",
    "tenant_id": "your-tenant-id",
    "bot_id": "your-bot-id"
  }
}
```

### 3. Streaming workflow

```typescript
const result = await workflowRun.stream({
  inputData: { ... }
});

for await (const chunk of result.stream) {
  console.log(chunk);
}
```

## Tính năng nổi bật

### 1. Phân tích ngữ cảnh thông minh
- Sử dụng Gemini 2.0 Flash để phân tích conversation history
- Tự động tạo keywords phù hợp với ý định khách hàng
- Hiểu được context của cuộc hội thoại, không chỉ tin nhắn đơn lẻ

### 2. Tìm kiếm FAQ tối ưu
- Hybrid search trong Weaviate với multiple keywords
- Loại bỏ duplicate và ranking theo relevance score
- Hỗ trợ multi-tenant và bot-specific filtering

### 3. Error handling mạnh mẽ
- Fallback mechanisms ở mọi step
- Graceful degradation khi FAQ service không khả dụng
- Retry logic cho các API calls

### 4. Performance optimization
- Parallel search cho multiple keywords
- Limit kết quả để tránh overload
- Efficient memory usage với streaming

## Monitoring và Debug

### 1. Workflow status
```typescript
console.log(result.status); // "success" | "failed" | "suspended"
```

### 2. Step-by-step monitoring
```typescript
workflowRun.watch((event) => {
  console.log(`Step ${event.payload.id}: ${event.type}`);
});
```

### 3. Error tracking
```typescript
if (result.status === "failed") {
  console.error("Workflow failed:", result.error);
}
```

## Cấu hình

### Environment Variables
```env
# Gemini API
GOOGLE_GENERATIVE_AI_API_KEY=your-api-key

# Weaviate
WEAVIATE_URL=your-weaviate-url
WEAVIATE_API_KEY=your-weaviate-key

# PostgreSQL (cho memory)
PG_CONNECTION_STRING=postgresql://...
```

### Tuning Parameters

Trong `loma-bag-workflow.ts`, bạn có thể điều chỉnh:

- `keywords.slice(0, 3)`: Số lượng keywords tối đa
- `searchFaqs(keyword, tenant_id, 2, bot_id)`: Limit FAQ per keyword
- `uniqueFaqs.slice(0, 3)`: Số lượng FAQ context tối đa
- `recentMessages.slice(-5)`: Số tin nhắn history để phân tích

## Troubleshooting

### 1. Workflow không tìm thấy FAQ
- Kiểm tra tenant_id và bot_id có đúng không
- Verify FAQ data đã được sync vào Weaviate
- Check keywords được tạo có phù hợp không

### 2. Agent response không sử dụng FAQ context
- Kiểm tra FAQ content có đủ chi tiết không
- Verify prompt template trong response generation step
- Check agent instructions có hướng dẫn sử dụng context không

### 3. Performance issues
- Reduce số lượng keywords hoặc FAQ limit
- Enable caching cho FAQ search results
- Monitor Weaviate response time

## Examples

Xem file `src/examples/loma-bag-workflow-example.ts` để có examples chi tiết về cách sử dụng workflow trong các scenarios khác nhau.
