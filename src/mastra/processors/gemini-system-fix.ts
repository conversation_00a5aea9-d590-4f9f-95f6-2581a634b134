import { CoreMessage, MemoryProcessor, MemoryProcessorOpts } from "@mastra/core";

/**
 * Custom Memory Processor để xử lý các vấn đề định dạng message với Gemini
 *
 * Các vấn đề được xử lý:
 * 1. "contents.parts must not be empty" - Khi conversation bắt đầu bằng tool call sau system message
 * 2. "function call turn comes immediately after a user turn or after a function response turn" - Thứ tự tool calls không đúng
 *
 * Giải pháp:
 * - Chèn text message mặc định khi cần thiết
 * - <PERSON><PERSON><PERSON> thứ tự tool calls để tuân thủ quy tắc của Gemini
 *
 * <PERSON><PERSON><PERSON> sử dụng:
 * ```typescript
 * import { GeminiSystemFixProcessor } from "./processors/gemini-system-fix";
 *
 * const memory = new Memory({
 *   processors: [
 *     new GeminiSystemFixProcessor(),
 *     // other processors...
 *   ],
 * });
 * ```
 */
export class GeminiSystemFixProcessor extends MemoryProcessor {
  constructor() {
    super({ name: "GeminiSystemFixProcessor" });
  }

  process(
    messages: CoreMessage[],
    _opts: MemoryProcessorOpts = {}
  ): CoreMessage[] {
    if (!messages || messages.length === 0) {
      return messages;
    }

    // Avoid mutating input array
    let result: CoreMessage[] = [...messages];

    // Bước 1: Xử lý vấn đề system message + tool call
    result = this.fixSystemMessageIssue(result);

    // Bước 2: Xử lý vấn đề thứ tự tool calls
    result = this.fixToolCallSequence(result);

    return result;
  }

  /**
   * Xử lý vấn đề system message theo sau bởi tool call
   */
  private fixSystemMessageIssue(messages: CoreMessage[]): CoreMessage[] {
    const result = [...messages];

    // Tìm vị trí system message đầu tiên
    const systemIndex = result.findIndex(msg => msg.role === 'system');

    if (systemIndex === -1) {
      return result;
    }

    // Kiểm tra message ngay sau system message
    const nextIndex = systemIndex + 1;

    if (nextIndex < result.length) {
      const nextMsg = result[nextIndex];

      // Kiểm tra nếu message tiếp theo là assistant với tool calls hoặc không có text content
      const needsTextMessage = this.shouldInsertTextMessage(nextMsg);

      if (needsTextMessage) {
        // Chèn một assistant text message đơn giản sau system message
        const defaultTextMessage: CoreMessage = {
          role: 'assistant',
          content: 'Xin chào! Tôi sẽ hỗ trợ bạn.'
        };

        result.splice(nextIndex, 0, defaultTextMessage);
      }
    } else {
      // Nếu chỉ có system message, thêm một user message và assistant message mặc định
      result.push(
        {
          role: 'user',
          content: 'Xin chào'
        },
        {
          role: 'assistant',
          content: 'Xin chào! Tôi là trợ lý AI của cửa hàng. Tôi có thể giúp gì cho bạn hôm nay?'
        }
      );
    }

    return result;
  }

  /**
   * Xử lý vấn đề thứ tự tool calls để tránh lỗi "function call turn comes immediately after a user turn"
   */
  private fixToolCallSequence(messages: CoreMessage[]): CoreMessage[] {
    const result: CoreMessage[] = [];

    for (let i = 0; i < messages.length; i++) {
      const currentMsg = messages[i];
      const prevMsg = i > 0 ? messages[i - 1] : null;

      // Thêm message hiện tại
      result.push(currentMsg);

      // Kiểm tra nếu message hiện tại là assistant với tool calls
      if (this.isAssistantWithToolCalls(currentMsg)) {
        // Kiểm tra xem message trước đó có phải là user không
        if (prevMsg && prevMsg.role === 'user') {
          // OK - tool call sau user message là hợp lệ
          continue;
        }

        // Kiểm tra xem message trước đó có phải là tool result không
        if (prevMsg && prevMsg.role === 'tool') {
          // OK - tool call sau tool result cũng hợp lệ
          continue;
        }

        // Nếu không hợp lệ, chèn một user message giả
        if (prevMsg && !['user', 'tool'].includes(prevMsg.role)) {
          const fakeUserMessage: CoreMessage = {
            role: 'user',
            content: 'Tiếp tục hỗ trợ tôi.'
          };

          // Chèn user message trước tool call
          result.splice(result.length - 1, 0, fakeUserMessage);
        }
      }
    }

    return result;
  }

  /**
   * Kiểm tra xem có cần chèn text message hay không
   */
  private shouldInsertTextMessage(message: CoreMessage): boolean {
    // Nếu không phải assistant message thì không cần xử lý
    if (message.role !== 'assistant') {
      return false;
    }

    // Kiểm tra content của message
    if (typeof message.content === 'string') {
      // Nếu là string và có nội dung thì OK
      return !message.content.trim();
    }

    if (Array.isArray(message.content)) {
      // Kiểm tra xem có text content không
      const hasText = message.content.some(part => 
        part.type === 'text' && part.text && part.text.trim()
      );
      
      // Kiểm tra xem có chỉ toàn tool calls không
      const hasOnlyToolCalls = message.content.length > 0 && 
        message.content.every(part => part.type === 'tool-call');
      
      // Cần chèn text message nếu không có text hoặc chỉ có tool calls
      return !hasText || hasOnlyToolCalls;
    }

    // Trường hợp khác (content undefined, null, etc.)
    return true;
  }

  /**
   * Kiểm tra xem message có phải là assistant với tool calls không
   */
  private isAssistantWithToolCalls(message: CoreMessage): boolean {
    if (message.role !== 'assistant') {
      return false;
    }

    if (Array.isArray(message.content)) {
      return message.content.some(part => part.type === 'tool-call');
    }

    return false;
  }

  /**
   * Validate conversation flow để đảm bảo tuân thủ quy tắc Gemini
   */
  private validateConversationFlow(messages: CoreMessage[]): boolean {
    for (let i = 0; i < messages.length; i++) {
      const msg = messages[i];
      const prevMsg = i > 0 ? messages[i - 1] : null;

      // Kiểm tra tool call sequence
      if (this.isAssistantWithToolCalls(msg)) {
        if (prevMsg && !['user', 'tool'].includes(prevMsg.role)) {
          return false;
        }
      }

      // Kiểm tra empty content
      if (msg.role === 'assistant' && this.shouldInsertTextMessage(msg)) {
        return false;
      }
    }

    return true;
  }
}

/**
 * Factory function để tạo processor với custom message
 */
export function createGeminiSystemFixProcessor(defaultMessage?: string) {
  return new (class extends GeminiSystemFixProcessor {
    process(messages: CoreMessage[], opts: MemoryProcessorOpts = {}): CoreMessage[] {
      // Override default message nếu được cung cấp
      const originalProcess = super.process(messages, opts);
      
      if (defaultMessage) {
        // Tìm và thay thế default message
        return originalProcess.map(msg => {
          if (msg.role === 'assistant' && msg.content === 'Xin chào! Tôi sẽ hỗ trợ bạn.') {
            return { ...msg, content: defaultMessage };
          }
          return msg;
        });
      }
      
      return originalProcess;
    }
  })();
}
