import { <PERSON><PERSON> } from "@mastra/core";
import {
  ecommerceAgent,
} from "./agents";
import dotenv from "dotenv";
import { initWeaviateForMastra } from "./utils/weaviate-init";
import postgresStore from "./utils/postgres-store";
import { ragAgent } from "./agents/loma-customer-service";
import { lomaBagWorkflow } from "./workflows";
import { LangfuseExporter } from "langfuse-vercel";
import { intelligentCustomerServiceAgent } from "./agents/intelligent-customer-service";

dotenv.config();

// Khởi tạo Weaviate client (không chờ kết quả để tránh blocking)
initWeaviateForMastra().catch((error) => {
  console.warn("⚠️ Lỗi khi khởi tạo Weaviate client:", error);
});

export const mastra = new Mastra({
  agents: {
    ecommerceAgent,
    ragAgent,
    intelligentCustomerServiceAgent,
  },
  workflows: {
    lomaBagWorkflow,
  },
  storage: postgresStore,
  server: {
    middleware: [
      async (c, next) => {
        // Lấy RuntimeContext từ context
        const runtimeContext = c.get("runtimeContext");

        // Set default values for RuntimeContext
        runtimeContext.set("bot_id", "3a38a49b-6ea7-4942-95bf-fc8e612c195a");
        runtimeContext.set("tenant_id", "4ebbbb49-db73-4420-acac-96aaf1670aef");

        // If headers are provided, use them instead of defaults
        const botId = c.req.header("x-bot-id");
        const tenantId = c.req.header("x-tenant-id");
        const threadId = c.req.header("x-thread-id");
        const resourceId = c.req.header("x-resource-id");
        const accountId = c.req.header("x-account-id");
        const conversationId = c.req.header("x-conversation-id");

        if (botId) {
          runtimeContext.set("bot_id", botId);
        }

        if (tenantId) {
          runtimeContext.set("tenant_id", tenantId);
        }

        if (threadId) {
          runtimeContext.set("thread_id", threadId);
        }

        if (resourceId) {
          runtimeContext.set("resource_id", resourceId);
        }

        // Thêm account_id và conversation_id vào runtime context
        if (accountId) {
          runtimeContext.set("account_id", accountId);
        }

        if (conversationId) {
          runtimeContext.set("conversation_id", conversationId);
        }

        await next();
      },
    ],
  },
  telemetry: {
    serviceName: 'ai',
    enabled: true,
    export: {
      type: "custom",
      exporter: new LangfuseExporter({
        publicKey: process.env.LANGFUSE_PUBLIC_KEY,
        secretKey: process.env.LANGFUSE_SECRET_KEY,
        baseUrl: process.env.LANGFUSE_BASEURL,
      }),
    },
  },
});
