import { z } from 'zod';

// Schema định nghĩa cấu trúc của một tin nhắn
// export const messageSchema = z.object({
//   content: z.string().describe('Nội dung của tin nhắn'),
//   delay: z.number().optional().describe('Thời gian trễ trước khi gửi tin nhắn này (ms)')
// });

// Schema định nghĩa danh sách tin nhắn
// export const messagesListSchema = z.array(messageSchema).describe('đoạn tin nhắn được tách ra hợp lý để gửi khách hàng dễ đọc hơn');

// Schema định nghĩa kết quả trả về từ agent
export const agentResponseSchema = z.object({
  messages: z.string().describe('Nội dung tin nhắn sẽ được gửi cho người dùng'),
  images: z.array(z.string()).optional().describe('Dan<PERSON> sách URL của các ảnh sẽ được gửi cho người dùng nếu có'),
  thinking: z.string().optional().describe('Nội dung suy nghĩ của agent'),
});
