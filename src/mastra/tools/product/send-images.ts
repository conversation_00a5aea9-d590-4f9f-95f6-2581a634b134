import { createTool } from "@mastra/core/tools";
import { z } from "zod";

/**
 * Công cụ gửi hình ảnh sản phẩm cho khách hàng
 * Nhận product_id và tự động lấy đầy đủ hình ảnh từ database để gửi (tối đa 10 hình)
 */
export const sendImagesTool = createTool({
  id: "send_images",
  description: "Gửi hình ảnh sản phẩm cho khách hàng - Nhận product_id và tự động lấy đầy đủ hình ảnh từ database",
  inputSchema: z.object({
    product_id: z.string().describe("ID của sản phẩm cần gửi hình ảnh"),
  }),
  execute: async ({ context, runtimeContext }) => {
    try {
      // Lấy tenant_id từ runtime context
      const tenant_id = runtimeContext.get("tenant_id");

      if (!tenant_id) {
        return {
          success: false,
          error: "Thiếu thông tin tenant_id trong runtime context",
        };
      }

      // Validate product_id
      if (!context.product_id || context.product_id.trim().length === 0) {
        return {
          success: false,
          error: "Product ID không được để trống",
        };
      }

      // Tool này sẽ được xử lý bởi agent.service.ts để lấy hình ảnh và gửi
      // Ở đây chỉ trả về thông tin để agent.service.ts xử lý
      return {
        success: true,
        product_id: context.product_id.trim(),
        message: `Đã chuẩn bị gửi hình ảnh sản phẩm với ID: ${context.product_id}`,
      };
    } catch (error: any) {
      console.error("Lỗi khi xử lý gửi hình ảnh sản phẩm:", error);
      return {
        success: false,
        error: `Lỗi hệ thống: ${error?.message || "Lỗi không xác định"}`,
      };
    }
  },
});
