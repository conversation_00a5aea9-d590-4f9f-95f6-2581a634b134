import { createTool } from "@mastra/core/tools";
import { z } from "zod";
import { getProductBySku } from "../../../services/postgres/product.service";
import { supabaseAdmin } from "../../../config/supabase";
import { optimizeProductData, formatPriceDisplay, isProductAvailable, getStockInfo } from "../../../utils/product-optimization.utils";

/**
 * TOOL LẤY THÔNG TIN SẢN PHẨM CHI TIẾT - PRODUCTION
 *
 * Tool duy nhất tối ưu hóa để thay thế getProductPricingTool và getProductBySkuTool:
 * - Nhận vào product_id hoặc sku (hoặc cả hai)
 * - Tự động xử lý logic tìm kiếm trong products và product_variants
 * - Hiển thị thông tin giá chi tiết (giá gốc, giá sale, % giảm giá)
 * - Kiểm tra tồn kho và trạng thái sản phẩm
 * - <PERSON><PERSON> lý cả sản phẩm đơn giản và sản phẩm có biến thể
 * - Tối ưu hóa để tiết kiệm token và cung cấp thông tin chính xác
 */
export const getProductDetailsTool = createTool({
  id: "get_product_details",
  description: "Lấy thông tin chi tiết sản phẩm - Tool duy nhất: tìm kiếm theo product_id hoặc SKU, tự động xử lý logic giá, kiểm tra tồn kho, hiển thị biến thể",
  inputSchema: z.object({
    product_id: z
      .string()
      .nullable()
      .optional()
      .describe("Mã sản phẩm (tùy chọn - nếu không có sẽ tìm theo SKU)"),
    sku: z
      .string()
      .nullable()
      .optional()
      .describe("Mã SKU của sản phẩm hoặc biến thể (tùy chọn - nếu không có sẽ tìm theo product_id)"),
    variant_id: z
      .string()
      .nullable()
      .optional()
      .describe("Mã biến thể cụ thể (tùy chọn - chỉ dùng khi có product_id và muốn lấy biến thể cụ thể)"),
  }),
  execute: async ({ context, runtimeContext }) => {
    try {
      // Lấy tenant_id từ runtime context
      const tenant_id = runtimeContext.get("tenant_id");
      if (!tenant_id) {
        return {
          success: false,
          error: "Thiếu thông tin tenant_id trong runtime context",
        };
      }

      // Kiểm tra input - phải có ít nhất product_id hoặc sku
      if (!context.product_id && !context.sku) {
        return {
          success: false,
          error: "Vui lòng cung cấp product_id hoặc sku để tìm kiếm sản phẩm",
        };
      }

      let productData: any = null;
      let searchMethod = "";

      // TRƯỜNG HỢP 1: Có product_id và variant_id - lấy biến thể cụ thể
      if (context.product_id && context.variant_id) {
        searchMethod = "variant_specific";

        const { data: variantResult, error: variantError } = await supabaseAdmin
          .from('product_variants')
          .select(`
            id, name, sku, price, compare_at_price, stock_quantity, is_active, attributes,
            products!inner(id, name, description, short_description, type, avatar, images, is_active, category_id)
          `)
          .eq('id', context.variant_id)
          .eq('product_id', context.product_id)
          .eq('tenant_id', tenant_id)
          .eq('is_active', true)
          .eq('products.is_active', true)
          .single();

        if (variantError || !variantResult) {
          return {
            success: false,
            error: `❌ Không tìm thấy biến thể sản phẩm với ID: ${context.variant_id}`,
          };
        }

        const variant = variantResult;
        const product = (variant.products as any);
        productData = {
          id: product.id,
          name: product.name,
          description: product.description,
          short_description: product.short_description,
          type: product.type,
          avatar: product.avatar,
          images: product.images,
          category_id: product.category_id,
          variants: [{
            id: variant.id,
            name: variant.name,
            variant_name: variant.name,
            sku: variant.sku,
            price: variant.price,
            compare_at_price: variant.compare_at_price,
            stock_quantity: variant.stock_quantity,
            attributes: variant.attributes,
            is_active: variant.is_active,
          }],
        };
      }
      // TRƯỜNG HỢP 2: Có SKU - tìm kiếm theo SKU
      else if (context.sku) {
        searchMethod = "sku_search";

        const searchResult = await getProductBySku({
          sku: context.sku,
          tenant_id: tenant_id.toString(),
        });

        if (!searchResult.success || !searchResult.data || searchResult.data.length === 0) {
          return {
            success: false,
            error: `❌ Không tìm thấy sản phẩm nào với SKU: ${context.sku}`,
          };
        }

        productData = searchResult.data[0];
      }
      // TRƯỜNG HỢP 3: Có product_id - lấy thông tin sản phẩm
      else if (context.product_id) {
        searchMethod = "product_id";

        const { data: productResult, error: productError } = await supabaseAdmin
          .from('products')
          .select('id, name, description, short_description, sku, price, compare_at_price, stock_quantity, type, avatar, images, is_active, category_id')
          .eq('id', context.product_id)
          .eq('tenant_id', tenant_id)
          .eq('is_active', true)
          .single();

        if (productError || !productResult) {
          return {
            success: false,
            error: `❌ Không tìm thấy sản phẩm với ID: ${context.product_id}`,
          };
        }

        const product = productResult as any;

        // Nếu sản phẩm có biến thể, lấy tất cả biến thể
        if (product.type === 'variable') {
          const { data: allVariants, error: variantsError } = await supabaseAdmin
            .from('product_variants')
            .select('id, name, sku, price, compare_at_price, stock_quantity, attributes, is_active')
            .eq('product_id', context.product_id)
            .eq('tenant_id', tenant_id)
            .eq('is_active', true)
            .order('name');

          if (variantsError) {
            console.error("❌ Lỗi khi lấy biến thể sản phẩm:", variantsError);
            return {
              success: false,
              error: `Lỗi khi lấy biến thể sản phẩm: ${variantsError.message}`,
            };
          }

          product.variants = allVariants || [];
        }

        productData = product;
      }

      if (!productData) {
        return {
          success: false,
          error: "❌ Không tìm thấy sản phẩm phù hợp",
        };
      }

      // XỬ LÝ VÀ ĐỊNH DẠNG DỮ LIỆU TRẢ VỀ
      return formatProductResponse(productData, searchMethod, context);

    } catch (error: any) {
      console.error("❌ Lỗi khi lấy thông tin sản phẩm:", error);
      return {
        success: false,
        error: `Lỗi khi lấy thông tin sản phẩm: ${error?.message || "Lỗi không xác định"}`,
      };
    }
  },
});

/**
 * Hàm helper để định dạng response sản phẩm
 */
function formatProductResponse(productData: any, searchMethod: string, context: any) {
  // Kiểm tra xem có phải là kết quả từ variant cụ thể không
  const isVariantSpecific = searchMethod === "variant_specific" || 
    (productData.variants && productData.variants.length === 1 && 
     productData.variants[0].sku === context.sku);

  if (isVariantSpecific) {
    // TRƯỜNG HỢP: Biến thể cụ thể - sử dụng utility functions
    const optimizedProducts = optimizeProductData([productData]);
    if (optimizedProducts.length === 0 || !optimizedProducts[0].variants) {
      return {
        success: false,
        error: "Không tìm thấy biến thể hoặc biến thể không khả dụng",
      };
    }

    const optimizedProduct = optimizedProducts[0];
    const variant = optimizedProduct.variants![0]; // Safe vì đã check ở trên

    return {
      success: true,
      product_info: {
        product_id: optimizedProduct.id,
        variant_id: variant.id,
        product_name: optimizedProduct.name,
        variant_name: variant.name,
        short_description: optimizedProduct.short_description,
        sku: variant.sku,
        type: optimizedProduct.type,
        avatar: optimizedProduct.avatar,
        images: optimizedProduct.images,

        // Thông tin giá tối ưu
        price: variant.price,
        original_price: variant.original_price,
        discount_percent: variant.discount_percent,
        is_on_sale: variant.is_on_sale,
        price_display: formatPriceDisplay(variant),

        // Thông tin tồn kho
        stock_quantity: variant.stock_quantity,
        in_stock: variant.stock_quantity > 0,
        stock_status: variant.stock_quantity > 10 ? 'Còn hàng' :
                     variant.stock_quantity > 0 ? `Chỉ còn ${variant.stock_quantity} sản phẩm` : 'Hết hàng',

        attributes: variant.attributes,
        has_variants: true,
        is_available: isProductAvailable(optimizedProduct),
      },
      message: `✅ Biến thể "${variant.name}" - ${formatPriceDisplay(variant)}`,
    };
  } else if (productData.type === 'variable' && productData.variants && productData.variants.length > 0) {
    // TRƯỜNG HỢP: Sản phẩm có nhiều biến thể - sử dụng utility functions
    const optimizedProducts = optimizeProductData([productData]);
    if (optimizedProducts.length === 0) {
      return {
        success: false,
        error: "Không tìm thấy sản phẩm hoặc sản phẩm không khả dụng",
      };
    }

    const optimizedProduct = optimizedProducts[0];
    const stockInfo = getStockInfo(optimizedProduct);

    // Tính giá range từ variants
    const prices = optimizedProduct.variants?.map(v => v.price) || [optimizedProduct.price];
    const minPrice = Math.min(...prices);
    const maxPrice = Math.max(...prices);

    return {
      success: true,
      product_info: {
        product_id: optimizedProduct.id,
        product_name: optimizedProduct.name,
        short_description: optimizedProduct.short_description,
        sku: optimizedProduct.sku,
        type: optimizedProduct.type,
        avatar: optimizedProduct.avatar,
        images: optimizedProduct.images,

        // Thông tin giá range
        price_range: {
          min_price: minPrice,
          max_price: maxPrice,
          price_display: minPrice === maxPrice ?
            `${minPrice.toLocaleString('vi-VN')}đ` :
            `${minPrice.toLocaleString('vi-VN')}đ - ${maxPrice.toLocaleString('vi-VN')}đ`,
        },

        // Thông tin tồn kho tối ưu
        total_stock: stockInfo.total_stock,
        available_variants: stockInfo.available_variants,
        is_in_stock: stockInfo.is_in_stock,

        variants: optimizedProduct.variants,
        has_variants: true,
        is_available: isProductAvailable(optimizedProduct),
      },
      message: `✅ Sản phẩm "${optimizedProduct.name}" có ${optimizedProduct.variants?.length || 0} biến thể khả dụng. Giá từ ${minPrice.toLocaleString('vi-VN')}đ${maxPrice !== minPrice ? ` - ${maxPrice.toLocaleString('vi-VN')}đ` : ''}`,
    };
  } else {
    // TRƯỜNG HỢP: Sản phẩm đơn giản - sử dụng utility functions
    const optimizedProducts = optimizeProductData([productData]);
    if (optimizedProducts.length === 0) {
      return {
        success: false,
        error: "Không tìm thấy sản phẩm hoặc sản phẩm không khả dụng",
      };
    }

    const optimizedProduct = optimizedProducts[0];

    return {
      success: true,
      product_info: {
        product_id: optimizedProduct.id,
        product_name: optimizedProduct.name,
        short_description: optimizedProduct.short_description,
        sku: optimizedProduct.sku,
        type: optimizedProduct.type,
        avatar: optimizedProduct.avatar,
        images: optimizedProduct.images,

        // Thông tin giá tối ưu
        price: optimizedProduct.price,
        original_price: optimizedProduct.original_price,
        discount_percent: optimizedProduct.discount_percent,
        is_on_sale: optimizedProduct.is_on_sale,
        price_display: formatPriceDisplay(optimizedProduct),

        // Thông tin tồn kho
        stock_quantity: optimizedProduct.stock_quantity,
        in_stock: optimizedProduct.stock_quantity > 0,
        stock_status: optimizedProduct.stock_quantity > 10 ? 'Còn hàng' :
                     optimizedProduct.stock_quantity > 0 ? `Chỉ còn ${optimizedProduct.stock_quantity} sản phẩm` : 'Hết hàng',

        has_variants: false,
        is_available: isProductAvailable(optimizedProduct),
      },
      message: `✅ Sản phẩm "${optimizedProduct.name}" - ${formatPriceDisplay(optimizedProduct)}`,
    };
  }
}
