import { createTool } from "@mastra/core/tools";
import { z } from "zod";
import { getOrderByCode } from "../../../services/supabase/product.service";

/**
 * Công cụ lấy thông tin đơn hàng theo mã đơn hàng
 * Sử dụng Supabase để lấy thông tin đơn hàng
 */
export const getOrderByCodeTool = createTool({
  id: "get_order_by_code",
  description: "Lấy thông tin chi tiết về một đơn hàng theo mã đơn hàng",
  inputSchema: z.object({
    order_code: z.string().describe("Mã đơn hàng cần tra cứu"),
  }),
  execute: async ({ context, runtimeContext }) => {
    try {

      // Lấy tenant_id từ runtime context
      const tenant_id = runtimeContext.get("tenant_id");

      if (!tenant_id) {
        return {
          order_details: null,
          error: "Thiếu thông tin tenant_id trong runtime context",
        };
      }

      // Lấy thông tin đơn hàng từ Supabase
      const result = await getOrderByCode({
        order_code: context.order_code,
        tenant_id: tenant_id.toString(),
      });

      if (!result.success) {
        console.error(`Không tìm thấy đơn hàng với mã ${context.order_code}`);
        return {
          order_details: null,
          error: result.message,
        };
      }

      return {
        order_details: result.data,
      };
    } catch (error: any) {
      console.error("Lỗi khi lấy thông tin đơn hàng:", error);
      return {
        order_details: null,
        error: `Lỗi khi lấy thông tin đơn hàng: ${error?.message || "Lỗi không xác định"}`,
      };
    }
  },
});
