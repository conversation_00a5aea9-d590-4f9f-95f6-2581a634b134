import { createTool } from "@mastra/core/tools";
import { z } from "zod";
import { getOrderByCode, updateOrderStatus } from "../../../services/supabase/product.service";

/**
 * <PERSON><PERSON><PERSON> cụ cho phép khách hàng hủy đơn hàng
 * Chỉ cho phép hủy đơn hàng khi đơn hàng đang ở trạng thái chưa giao cho shipper
 * (pending, processing)
 */
export const cancelOrderTool = createTool({
  id: "cancel_order",
  description: "Cho phép khách hàng hủy đơn hàng nếu đơn hàng chưa được giao cho shipper",
  inputSchema: z.object({
    order_code: z.string().describe("Mã đơn hàng cần hủy"),
    reason: z.string().describe("Lý do hủy đơn hàng"),
  }),
  execute: async ({ context, runtimeContext }) => {
    try {

      // Lấy tenant_id từ runtime context
      const tenant_id = runtimeContext.get("tenant_id");

      if (!tenant_id) {
        return {
          success: false,
          error: "Thiếu thông tin tenant_id trong runtime context",
        };
      }

      // Lấy thông tin đơn hàng hiện tại để kiểm tra trạng thái
      const orderResult = await getOrderByCode({
        order_code: context.order_code,
        tenant_id: tenant_id.toString(),
      });

      if (!orderResult.success || !orderResult.data) {
        return {
          success: false,
          error: orderResult.message || "Không tìm thấy đơn hàng",
        };
      }

      // Lấy thông tin đơn hàng từ kết quả trả về
      const order = orderResult.data as any; // Ép kiểu để tránh lỗi TypeScript
      const currentStatus = order.status;


      // Kiểm tra xem đơn hàng có thể hủy không
      const canCancel = ['pending', 'processing'].includes(currentStatus);

      if (!canCancel) {
        return {
          success: false,
          error: "Đơn hàng không thể hủy vì đã được giao cho đơn vị vận chuyển. Vui lòng liên hệ với nhân viên để được hỗ trợ.",
          need_agent: true, // Đánh dấu cần chuyển cho nhân viên
        };
      }

      // Cập nhật trạng thái đơn hàng thành cancelled
      const result = await updateOrderStatus({
        order_id: order.id,
        status: "cancelled",
        comment: `Khách hàng hủy đơn hàng. Lý do: ${context.reason}`,
        tenant_id: tenant_id.toString(),
      });

      if (!result.success) {
        console.error(`Không thể hủy đơn hàng: ${result.message}`);
        return {
          success: false,
          error: result.message,
        };
      }

      return {
        success: true,
        data: {
          order_id: order.id,
          order_code: context.order_code,
          status: "cancelled",
          cancelled_at: new Date().toISOString(),
        },
        message: "Đơn hàng đã được hủy thành công",
      };
    } catch (error: any) {
      console.error("Lỗi khi hủy đơn hàng:", error);
      return {
        success: false,
        error: `Lỗi khi hủy đơn hàng: ${error?.message || "Lỗi không xác định"}`,
      };
    }
  },
});
