/**
 * File này chứa các giá trị mặc định cho các trường hợp không thể lấy dữ liệu từ database
 * Chỉ sử dụng trong trường hợp khẩn cấp khi không thể kết nối đến database
 */

// Phương thức vận chuyển mặc định
export const DEFAULT_SHIPPING_METHOD = {
  id: "default-shipping-method",
  code: "STANDARD",
  name: "Giao hàng tiêu chuẩn",
  fee: 30000,
  estimated_delivery: "3-5 ngày",
  description: "Giao hàng tiêu chuẩn đến tất cả các tỉnh thành",
  shipping_type: "fixed", // Luôn sử dụng loại "fixed"
  free_shipping_threshold: 500000, // Miễn phí vận chuyển cho đơn hàng từ 500,000 VND
  minimum_order_amount: 0 // Không có giá trị đơn hàng tối thiểu
};

// Thời gian giao hàng mặc định
export const DEFAULT_DELIVERY_TIME = "3-5 ngày làm việc";

// Đơn vị vận chuyển mặc định
export const DEFAULT_SHIPPING_CARRIER = "GiaoHangNhanh";

// Tạo mã theo dõi vận chuyển ngẫu nhiên
export const generateRandomTrackingNumber = () => {
  return `GHN${Math.floor(Math.random() * 1000000000)}`;
};

// Tạo mã đơn hàng ngẫu nhiên
export const generateRandomOrderNumber = () => {
  const timestamp = Date.now();
  const randomChars = Math.random().toString(36).substring(2, 6).toUpperCase();
  return `ORD-${timestamp}-${randomChars}`;
};

// Tạo mã khiếu nại ngẫu nhiên
export const generateRandomComplaintId = () => {
  const timestamp = Date.now();
  const randomChars = Math.random().toString(36).substring(2, 6).toUpperCase();
  return `KN-${timestamp}-${randomChars}`;
};
