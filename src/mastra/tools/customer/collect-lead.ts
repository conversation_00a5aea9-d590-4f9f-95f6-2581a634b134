import { createTool } from "@mastra/core/tools";
import { z } from "zod";

/**
 * <PERSON><PERSON>ng cụ thu thập leads cho RAG chatbot
 * Chỉ cần số điện thoại để nhân viên tư vấn liên hệ sau
 */
export const collectLeadTool = createTool({
  id: "collect_lead",
  description: "Thu thập số điện thoại khách hàng để nhân viên tư vấn liên hệ hỗ trợ. Sử dụng khi khách hàng muốn được tư vấn trực tiếp, cần hỗ trợ chuyên sâu, hoặc quan tâm đến sản phẩm/dịch vụ.",
  inputSchema: z.object({
    phone: z.string().describe("Số điện thoại liên hệ của khách hàng (b<PERSON><PERSON> buộ<PERSON>, định dạng: 0xxxxxxxxx)"),
  }),
  execute: async ({ context, runtimeContext }) => {
    try {
      // Lấy tenant_id từ runtime context
      const tenant_id = runtimeContext.get("tenant_id");

      if (!tenant_id) {
        return {
          success: false,
          error: "Thiếu thông tin tenant_id trong runtime context",
        };
      }

      // Validate số điện thoại
      const phoneRegex = /^(0[3|5|7|8|9])+([0-9]{8})$/;
      if (!phoneRegex.test(context.phone)) {
        return {
          success: false,
          error: "Số điện thoại không hợp lệ. Vui lòng nhập số điện thoại Việt Nam (ví dụ: 0987654321)",
        };
      }

      // Thu thập thông tin lead

      return {
        success: true,
        message: "Cảm ơn bạn đã cung cấp thông tin liên hệ! Nhân viên tư vấn sẽ liên hệ với bạn trong thời gian sớm nhất để hỗ trợ tốt nhất.",
      };
    } catch (error: any) {
      console.error("Lỗi khi thu thập lead:", error);
      return {
        success: false,
        error: `Lỗi khi thu thập thông tin liên hệ: ${error?.message || "Lỗi không xác định"}`,
      };
    }
  },
});
