import { createTool } from '@mastra/core/tools';
import { z } from 'zod';

// Export getFaqsTool từ file faqs.ts
export { getFaqsTool } from './faqs';

// Export collectLeadTool từ file collect-lead.ts
export { collectLeadTool } from './collect-lead';

// 1. Công cụ thu thập thông tin khách hàng
export const collectCustomerInfoTool = createTool({
  id: 'collect_customer_info',
  description: 'Thu thập thông tin khách hàng',
  inputSchema: z.object({
    info_type: z.string().describe('Loại thông tin cần thu thập (contact, shipping, complaint_reason, etc.)')
  }),
  execute: async ({ context }) => {

    // Gi<PERSON> lập thu thập thông tin khách hàng
    let collectedInfo = {};

    switch (context.info_type) {
      case 'contact':
        collectedInfo = {
          name: "Nguyễ<PERSON>",
          phone: "0987654321",
          email: "nguy<PERSON><EMAIL>"
        };
        break;
      case 'shipping':
        collectedInfo = {
          address: "123 Đường ABC, Phường XYZ",
          city: "Hà Nội",
          district: "Cầu Giấy",
          notes: "Giao hàng ngoài giờ hành chính"
        };
        break;
      case 'complaint_reason':
        collectedInfo = {
          complaint_reason: "Giao hàng chậm, không thể đợi thêm"
        };
        break;
      case 'product_preference':
        collectedInfo = {
          preferred_size: "L",
          preferred_colors: ["Đen", "Xanh Navy"],
          preferred_style: "Casual",
          budget_range: "200000-500000"
        };
        break;
      default:
        collectedInfo = {
          message: "Loại thông tin không hợp lệ"
        };
    }

    return {
      customer_info: collectedInfo,
      info_type: context.info_type
    };
  }
});


