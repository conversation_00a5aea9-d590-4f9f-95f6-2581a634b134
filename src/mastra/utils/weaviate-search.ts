import { getWeaviateClientInstance } from "../../config/weaviate";

/**
 * Tìm kiếm sản phẩm trong Weaviate sử dụng semantic search
 */
export async function searchProductsInWeaviate(query: string, limit: number = 5) {
  try {
    const client = await getWeaviateClientInstance();
    
    // Thực hiện tìm kiếm semantic
    const result = await client.graphql
      .get()
      .withClassName('Product')
      .withFields('id name description price category images')
      .withNearText({
        concepts: [query],
        distance: 0.7 // Điều chỉnh độ tương tự
      })
      .withLimit(limit)
      .do();

    const products = result?.data?.Get?.Product || [];
    
    // Format kết quả
    return products.map((product: any, index: number) => ({
      id: product.id || `product-${index}`,
      name: product.name || 'Sản phẩm không tên',
      description: product.description || '',
      price: product.price || 0,
      category: product.category || '',
      images: product.images || [],
      score: 0.8 - (index * 0.1) // Giả lập relevance score
    }));

  } catch (error) {
    console.error("Lỗi khi tìm kiếm trong Weaviate:", error);
    return [];
  }
}

/**
 * Tìm kiếm FAQ trong Weaviate
 */
export async function searchFAQsInWeaviate(query: string, limit: number = 5) {
  try {
    const client = await getWeaviateClientInstance();
    
    const result = await client.graphql
      .get()
      .withClassName('FAQ')
      .withFields('id question answer category')
      .withNearText({
        concepts: [query],
        distance: 0.7
      })
      .withLimit(limit)
      .do();

    const faqs = result?.data?.Get?.FAQ || [];
    
    return faqs.map((faq: any, index: number) => ({
      id: faq.id || `faq-${index}`,
      question: faq.question || '',
      answer: faq.answer || '',
      category: faq.category || '',
      score: 0.8 - (index * 0.1)
    }));

  } catch (error) {
    console.error("Lỗi khi tìm kiếm FAQ trong Weaviate:", error);
    return [];
  }
}

/**
 * Tìm kiếm tổng quát trong Weaviate
 */
export async function searchGeneralInWeaviate(query: string, limit: number = 5) {
  try {
    // Tìm kiếm trong cả Product và FAQ
    const [products, faqs] = await Promise.all([
      searchProductsInWeaviate(query, Math.ceil(limit / 2)),
      searchFAQsInWeaviate(query, Math.ceil(limit / 2))
    ]);

    // Kết hợp và sắp xếp kết quả
    const combined = [
      ...products.map((p: any) => ({ ...p, type: 'product' })),
      ...faqs.map((f: any) => ({ ...f, type: 'faq' }))
    ].sort((a, b) => b.score - a.score);

    return combined.slice(0, limit);

  } catch (error) {
    console.error("Lỗi khi tìm kiếm tổng quát trong Weaviate:", error);
    return [];
  }
}
