/**
 * T<PERSON><PERSON> một Weaviate client gi<PERSON> lập để tránh crash ứng dụng khi không thể kết nối đến Weaviate server
 */
export function createMockWeaviateClient() {
  console.warn("<PERSON><PERSON> sử dụng Weaviate client gi<PERSON> lập. <PERSON><PERSON><PERSON> chức năng liên quan đến Weaviate sẽ không hoạt động.");
  
  // Tạo một đối tượng giả lập với các phương thức cần thiết
  return {
    collections: {
      get: () => ({
        exists: async () => false,
        config: {
          get: async () => ({}),
        },
        delete: async () => ({}),
        create: async () => ({}),
        tenants: {
          getByName: async () => null,
          create: async () => ({}),
        },
        withTenant: () => ({
          data: {
            insert: async () => ({}),
            insertMany: async () => ({}),
          },
          query: {
            nearText: async () => ({ objects: [] }),
            nearImage: async () => ({ objects: [] }),
          },
          filter: {
            byProperty: () => ({
              equal: () => ({}),
            }),
          },
        }),
      }),
    },
  };
}

export default createMockWeaviateClient;
