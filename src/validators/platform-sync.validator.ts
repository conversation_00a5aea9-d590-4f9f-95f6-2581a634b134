import { z } from 'zod';

/**
 * <PERSON><PERSON><PERSON> x<PERSON><PERSON> thực chung cho API đồng bộ sản phẩm
 */
export const syncProductsSchema = z.object({
  body: z.object({
    platform: z.enum(['haravan', 'sapo'], {
      required_error: 'Nền tảng là bắt buộc (haravan hoặc sapo)'
    }),
    // Thông tin xác thực - có thể là token Haravan hoặc URL Sapo
    auth_token: z.string({
      required_error: 'Thông tin xác thực là bắt buộc'
    }),
    limit: z.number().optional().default(50),
    page: z.number().optional(),
    updated_at_min: z.string().optional(),
    full_sync: z.boolean().optional().default(false), // Mặc định không đồng bộ đầy đủ
    force_update: z.boolean().optional().default(false), // Mặc định không cập nhật các sản phẩm đã tồn tại
    bot_id: z.string().or(z.array(z.string())).optional(),
    tenant_id: z.string().optional()
  }).refine(
    (data) => {
      // Kiểm tra định dạng URL Sapo nếu nền tảng là Sapo
      if (data.platform === 'sapo') {
        return data.auth_token.includes('@') && data.auth_token.includes('/admin');
      }
      return true;
    },
    {
      message: 'URL Sapo không đúng định dạng. Yêu cầu: https://[api_key]:[password]@[domain]/admin/...',
      path: ['auth_token']
    }
  ),
  headers: z.object({
    'tenant-id': z.string({
      required_error: 'tenant-id trong header là bắt buộc'
    })
  }).optional()
});

/**
 * Schema xác thực cho API đồng bộ sản phẩm từ Haravan
 */
export const syncHaravanProductsSchema = z.object({
  body: z.object({
    token: z.string({
      required_error: 'Token xác thực Haravan là bắt buộc'
    }),
    limit: z.number().optional().default(50),
    page: z.number().optional(),
    updated_at_min: z.string().optional(),
    full_sync: z.boolean().optional().default(false), // Mặc định không đồng bộ đầy đủ
    force_update: z.boolean().optional().default(false), // Mặc định không cập nhật các sản phẩm đã tồn tại
    bot_id: z.string().or(z.array(z.string())).optional(),
    tenant_id: z.string().optional()
  }),
  headers: z.object({
    'tenant-id': z.string({
      required_error: 'tenant-id trong header là bắt buộc'
    })
  }).optional()
});

/**
 * Schema xác thực cho API đồng bộ sản phẩm từ Sapo (giữ lại để tương thích ngược)
 */
export const syncSapoProductsSchema = z.object({
  body: z.object({
    sapo_url: z.string({
      required_error: 'URL Sapo đầy đủ là bắt buộc (format: https://[api_key]:[password]@[domain]/admin/...)'
    }).refine(
      (url) => url.includes('@') && url.includes('/admin'),
      {
        message: 'URL Sapo không đúng định dạng. Yêu cầu: https://[api_key]:[password]@[domain]/admin/...'
      }
    ),
    limit: z.number().optional().default(50),
    page: z.number().optional(),
    updated_at_min: z.string().optional(),
    full_sync: z.boolean().optional().default(false), // Mặc định không đồng bộ đầy đủ
    force_update: z.boolean().optional().default(false), // Mặc định không cập nhật các sản phẩm đã tồn tại
    bot_id: z.string().or(z.array(z.string())).optional(),
    tenant_id: z.string().optional()
  }),
  headers: z.object({
    'tenant-id': z.string({
      required_error: 'tenant-id trong header là bắt buộc'
    })
  }).optional()
});

export default {
  syncProductsSchema,
  syncHaravanProductsSchema,
  syncSapoProductsSchema
};
