import { z } from 'zod';

/**
 * Validator cho các yêu cầu liên quan đến Weaviate
 */
export const weaviateValidator = {
  /**
   * Schema xác thực cho việc tạo FAQ mới
   */
  createFaqSchema: z.object({
    body: z.object({
      topic: z.string({
        required_error: 'Topic là bắt buộc',
        invalid_type_error: 'Topic phải là chuỗi'
      }).min(1, 'Topic không được để trống'),
      content: z.string({
        required_error: 'Content là bắt buộc',
        invalid_type_error: 'Content phải là chuỗi'
      }).min(1, 'Content không được để trống')
    })
  }),

  /**
   * Schema xác thực cho việc tìm kiếm FAQ
   */
  searchFaqSchema: z.object({
    query: z.object({
      query: z.string({
        required_error: 'Query tìm kiếm là bắt buộc',
        invalid_type_error: 'Query tìm kiếm phải là chuỗi'
      }).min(1, 'Query tìm kiếm không được để trống'),
      limit: z.string().optional().transform(val => val ? parseInt(val) : 5)
    })
  }),

  /**
   * Schema xác thực cho việc cập nhật FAQ theo supabase_id
   */
  updateFaqBySupabaseIdSchema: z.object({
    body: z.object({
      supabase_id: z.string({
        required_error: 'supabase_id là bắt buộc',
        invalid_type_error: 'supabase_id phải là chuỗi'
      }).min(1, 'supabase_id không được để trống'),
      tenant_id: z.string({
        required_error: 'tenant_id là bắt buộc',
        invalid_type_error: 'tenant_id phải là chuỗi'
      }).min(1, 'tenant_id không được để trống'),
      updateData: z.object({
        topic: z.string().optional(),
        content: z.string().optional(),
        bot_id: z.string().optional()
      }).refine(data => 
        Object.keys(data).length > 0,
        "Phải có ít nhất một trường để cập nhật"
      )
    })
  }),

  /**
   * Schema xác thực cho việc xóa FAQ theo supabase_id
   */
  deleteFaqBySupabaseIdSchema: z.object({
    body: z.object({
      supabase_id: z.string({
        required_error: 'supabase_id là bắt buộc',
        invalid_type_error: 'supabase_id phải là chuỗi'
      }).min(1, 'supabase_id không được để trống'),
      tenant_id: z.string({
        required_error: 'tenant_id là bắt buộc',
        invalid_type_error: 'tenant_id phải là chuỗi'
      }).min(1, 'tenant_id không được để trống')
    })
  }),

  /**
   * Schema xác thực cho việc xóa nhiều FAQ theo supabase_ids
   */
  deleteFaqsBySupabaseIdsSchema: z.object({
    body: z.object({
      supabase_ids: z.array(z.string({
        required_error: 'Mỗi supabase_id phải là chuỗi',
        invalid_type_error: 'Mỗi supabase_id phải là chuỗi'
      })).min(1, 'Danh sách supabase_ids không được để trống'),
      tenant_id: z.string({
        required_error: 'tenant_id là bắt buộc',
        invalid_type_error: 'tenant_id phải là chuỗi'
      }).min(1, 'tenant_id không được để trống')
    })
  }),

  /**
   * Schema xác thực cho việc tạo sản phẩm mới
   */
  createProductSchema: z.object({
    body: z.object({
      name: z.string({
        required_error: 'Tên sản phẩm là bắt buộc',
        invalid_type_error: 'Tên sản phẩm phải là chuỗi'
      }).min(1, 'Tên sản phẩm không được để trống'),
      price: z.number({
        invalid_type_error: 'Giá sản phẩm phải là số'
      }).optional().default(0),
      stock: z.number({
        invalid_type_error: 'Số lượng tồn kho phải là số'
      }).optional().default(0),
      description: z.string({
        invalid_type_error: 'Mô tả sản phẩm phải là chuỗi'
      }).optional().default(''),
      image_url: z.string({
        invalid_type_error: 'URL hình ảnh phải là chuỗi'
      }).optional().default(''),
      tenant_id: z.string({
        required_error: 'Tenant ID là bắt buộc',
        invalid_type_error: 'Tenant ID phải là chuỗi'
      }).min(1, 'Tenant ID không được để trống'),
      product_id: z.string({
        required_error: 'Product ID là bắt buộc',
        invalid_type_error: 'Product ID phải là chuỗi'
      }).min(1, 'Product ID không được để trống'),
      bot_id: z.union([
        z.string(),
        z.array(z.string())
      ]).optional().default('')
    })
  }),

  /**
   * Schema xác thực cho việc tạo nhiều sản phẩm
   */
  createProductsSchema: z.object({
    body: z.object({
      products: z.array(
        z.object({
          name: z.string({
            required_error: 'Tên sản phẩm là bắt buộc',
            invalid_type_error: 'Tên sản phẩm phải là chuỗi'
          }).min(1, 'Tên sản phẩm không được để trống'),
          price: z.number({
            invalid_type_error: 'Giá sản phẩm phải là số'
          }).optional().default(0),
          stock: z.number({
            invalid_type_error: 'Số lượng tồn kho phải là số'
          }).optional().default(0),
          description: z.string({
            invalid_type_error: 'Mô tả sản phẩm phải là chuỗi'
          }).optional().default(''),
          image_url: z.string({
            invalid_type_error: 'URL hình ảnh phải là chuỗi'
          }).optional().default(''),
          tenant_id: z.string({
            required_error: 'Tenant ID là bắt buộc',
            invalid_type_error: 'Tenant ID phải là chuỗi'
          }).min(1, 'Tenant ID không được để trống'),
          bot_id: z.union([
            z.string(),
            z.array(z.string())
          ]).optional().default('')
        })
      ).min(1, 'Cần ít nhất một sản phẩm')
    })
  }),

  /**
   * Schema xác thực cho việc tìm kiếm sản phẩm
   */
  searchProductSchema: z.object({
    query: z.object({
      query: z.string({
        required_error: 'Query tìm kiếm là bắt buộc',
        invalid_type_error: 'Query tìm kiếm phải là chuỗi'
      }).min(1, 'Query tìm kiếm không được để trống'),
      tenant_id: z.string({
        required_error: 'Tenant ID là bắt buộc',
        invalid_type_error: 'Tenant ID phải là chuỗi'
      }).min(1, 'Tenant ID không được để trống'),
      limit: z.string().optional().transform(val => val ? parseInt(val) : 5)
    })
  }),

  /**
   * Schema xác thực cho việc cập nhật bot_id của sản phẩm
   */
  updateProductBotIdSchema: z.object({
    body: z.object({
      product_id: z.string({
        required_error: 'Product ID là bắt buộc',
        invalid_type_error: 'Product ID phải là chuỗi'
      }).min(1, 'Product ID không được để trống'),
      tenant_id: z.string({
        required_error: 'Tenant ID là bắt buộc',
        invalid_type_error: 'Tenant ID phải là chuỗi'
      }).min(1, 'Tenant ID không được để trống'),
      bot_id: z.string({
        required_error: 'Bot ID là bắt buộc',
        invalid_type_error: 'Bot ID phải là chuỗi'
      }).min(1, 'Bot ID không được để trống'),
      action: z.enum(['add', 'remove'], {
        required_error: 'Action là bắt buộc',
        invalid_type_error: 'Action phải là "add" hoặc "remove"'
      })
    })
  }),

  /**
   * Schema xác thực cho việc xóa nhiều sản phẩm theo danh sách product_id
   */
  deleteProductsByIdsSchema: z.object({
    body: z.object({
      product_ids: z.array(z.string({
        required_error: 'Product ID là bắt buộc',
        invalid_type_error: 'Product ID phải là chuỗi'
      }).min(1, 'Product ID không được để trống'), {
        required_error: 'Danh sách product_ids là bắt buộc',
        invalid_type_error: 'product_ids phải là mảng'
      }).min(1, 'Danh sách product_ids không được để trống').max(100, 'Tối đa 100 sản phẩm mỗi lần xóa'),
      tenant_id: z.string({
        required_error: 'Tenant ID là bắt buộc',
        invalid_type_error: 'Tenant ID phải là chuỗi'
      }).min(1, 'Tenant ID không được để trống')
    })
  })
};
