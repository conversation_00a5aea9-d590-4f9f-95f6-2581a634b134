import { z } from 'zod';

/**
 * Validator cho các yêu cầu liên quan đến khách hàng
 */
export const customerValidator = {
  /**
   * Schema xác thực cho việc tạo địa chỉ mới
   */
  createAddressSchema: z.object({
    body: z.object({
      customer_id: z.string({
        required_error: 'customer_id là bắt buộc',
        invalid_type_error: 'customer_id phải là chuỗi'
      }),
      full_name: z.string({
        required_error: 'full_name là bắt buộc',
        invalid_type_error: 'full_name phải là chuỗi'
      }),
      phone: z.string({
        required_error: 'phone là bắt buộc',
        invalid_type_error: 'phone phải là chuỗi'
      }),
      address: z.string({
        required_error: 'address là bắt buộc',
        invalid_type_error: 'address phải là chuỗi'
      }),
      province: z.string().nullable().optional(),
      district: z.string().nullable().optional(),
      ward: z.string().nullable().optional(),
      country: z.string().optional().default('Vietnam'),
      address_type: z.enum(['shipping', 'billing', 'both']).optional().default('shipping'),
      is_default_shipping: z.boolean().optional().default(false),
      is_default_billing: z.boolean().optional().default(false),
      notes: z.string().optional()
    })
  }),

  /**
   * Schema xác thực cho việc cập nhật địa chỉ
   */
  updateAddressSchema: z.object({
    body: z.object({
      full_name: z.string().optional(),
      phone: z.string().optional(),
      address: z.string().optional(),
      province: z.string().nullable().optional(),
      district: z.string().nullable().optional(),
      ward: z.string().nullable().optional(),
      country: z.string().optional(),
      address_type: z.enum(['shipping', 'billing', 'both']).optional(),
      is_default_shipping: z.boolean().optional(),
      is_default_billing: z.boolean().optional(),
      notes: z.string().optional()
    }).refine(data => Object.keys(data).length > 0, {
      message: 'Phải cung cấp ít nhất một trường để cập nhật'
    })
  })
};
