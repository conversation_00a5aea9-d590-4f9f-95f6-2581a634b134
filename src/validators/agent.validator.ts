import { z } from 'zod';

/**
 * Validator cho các yêu cầu liên quan đến agent
 */
export const agentValidator = {
  /**
   * Schema xác thực cho tin nhắn
   */
  messageSchema: z.object({
    body: z.object({
      message: z.string({
        required_error: 'Tin nhắn là bắt buộc',
        invalid_type_error: 'Tin nhắn phải là chuỗi'
      }).min(1, 'Tin nhắn không được để trống'),
      threadId: z.string().optional(),
      resourceId: z.string().optional()
    })
  })
};
