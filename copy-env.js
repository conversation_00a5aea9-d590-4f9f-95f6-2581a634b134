const fs = require('fs');
const path = require('path');

// Đường dẫn đến file .env
const envFilePath = path.join(__dirname, '.env');
// Đường dẫn đến thư mục dist
const distFolderPath = path.join(__dirname, 'dist');
// Đường dẫn đến file .env trong thư mục dist
const distEnvFilePath = path.join(distFolderPath, '.env');

// Kiểm tra xem file .env có tồn tại không
if (fs.existsSync(envFilePath)) {
  // Kiểm tra xem thư mục dist có tồn tại không
  if (!fs.existsSync(distFolderPath)) {
    fs.mkdirSync(distFolderPath);
  }
  
  // Sao chép file .env vào thư mục dist
  fs.copyFileSync(envFilePath, distEnvFilePath);
  console.log('File .env đã được sao chép vào thư mục dist');
} else {
  console.log('Không tìm thấy file .env');
}
