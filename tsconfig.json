{"compilerOptions": {"module": "Node16", "moduleResolution": "Node16", "target": "esnext", "outDir": "./dist", "rootDir": "./src", "lib": ["esnext"], "types": ["node"], "strict": true, "esModuleInterop": true, "allowJs": true, "noEmit": false, "allowImportingTsExtensions": false, "skipLibCheck": true, "isolatedModules": true, "resolveJsonModule": true, "incremental": true, "forceConsistentCasingInFileNames": true}, "include": ["src/**/*.ts"], "exclude": ["node_modules", "dist", "trigger.config.ts"]}