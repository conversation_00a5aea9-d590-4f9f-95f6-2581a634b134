-- Migration: Setup Storage RLS Policies for Tenant-based Access Control
-- T<PERSON><PERSON> các RLS policies cho Supabase Storage để user có thể quản lý hình ảnh của họ

-- 1. Enable RLS cho storage.objects nếu chưa có
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

-- 2. Tạo bucket 'products' nếu chưa có (với public access)
INSERT INTO storage.buckets (id, name, public)
VALUES ('products', 'products', true)
ON CONFLICT (id) DO NOTHING;

-- 3. Policy cho phép đọc hình ảnh sản phẩm (public read)
CREATE POLICY "Public can view product images"
ON storage.objects
FOR SELECT
TO public
USING (bucket_id = 'products');

-- 4. Policy cho phép authenticated users upload hình ảnh vào thư mục tenant của họ
-- Cấu trúc thư mục: products/{tenant_id}/{filename}
CREATE POLICY "Authenticated users can upload to their tenant folder"
ON storage.objects
FOR INSERT
TO authenticated
WITH CHECK (
  bucket_id = 'products' AND
  (storage.foldername(name))[1] = (
    SELECT tenant_id::text 
    FROM auth.users 
    WHERE id = auth.uid()
  )
);

-- 5. Policy cho phép authenticated users xóa hình ảnh trong thư mục tenant của họ
CREATE POLICY "Users can delete images in their tenant folder"
ON storage.objects
FOR DELETE
TO authenticated
USING (
  bucket_id = 'products' AND
  (storage.foldername(name))[1] = (
    SELECT tenant_id::text 
    FROM auth.users 
    WHERE id = auth.uid()
  )
);

-- 6. Policy cho phép authenticated users cập nhật metadata hình ảnh trong thư mục tenant của họ
CREATE POLICY "Users can update images in their tenant folder"
ON storage.objects
FOR UPDATE
TO authenticated
USING (
  bucket_id = 'products' AND
  (storage.foldername(name))[1] = (
    SELECT tenant_id::text 
    FROM auth.users 
    WHERE id = auth.uid()
  )
)
WITH CHECK (
  bucket_id = 'products' AND
  (storage.foldername(name))[1] = (
    SELECT tenant_id::text 
    FROM auth.users 
    WHERE id = auth.uid()
  )
);

-- 7. Tạo function helper để kiểm tra quyền truy cập tenant
CREATE OR REPLACE FUNCTION check_tenant_access(file_path text, user_tenant_id text)
RETURNS boolean AS $$
BEGIN
  -- Kiểm tra xem đường dẫn file có thuộc về tenant của user không
  RETURN (storage.foldername(file_path))[1] = user_tenant_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 8. Policy đặc biệt cho service role (để đồng bộ sản phẩm)
-- Chỉ cho phép service role upload vào thư mục có tenant_id hợp lệ
CREATE POLICY "Service role can manage product images"
ON storage.objects
FOR ALL
TO service_role
USING (bucket_id = 'products')
WITH CHECK (bucket_id = 'products');

-- 9. Tạo view để user có thể xem hình ảnh sản phẩm của tenant họ
CREATE OR REPLACE VIEW user_product_images AS
SELECT 
  so.id,
  so.name,
  so.bucket_id,
  so.owner,
  so.created_at,
  so.updated_at,
  so.last_accessed_at,
  so.metadata,
  (storage.foldername(so.name))[1] as tenant_folder
FROM storage.objects so
WHERE so.bucket_id = 'products'
  AND (storage.foldername(so.name))[1] = (
    SELECT tenant_id::text 
    FROM auth.users 
    WHERE id = auth.uid()
  );

-- 10. Grant permissions cho view
GRANT SELECT ON user_product_images TO authenticated;

-- Comments để giải thích
COMMENT ON POLICY "Public can view product images" ON storage.objects IS 
'Cho phép tất cả user xem hình ảnh sản phẩm (cần thiết cho website public)';

COMMENT ON POLICY "Authenticated users can upload to their tenant folder" ON storage.objects IS 
'User chỉ có thể upload hình ảnh vào thư mục tenant của họ: products/{tenant_id}/';

COMMENT ON POLICY "Users can delete images in their tenant folder" ON storage.objects IS 
'User chỉ có thể xóa hình ảnh trong thư mục tenant của họ';

COMMENT ON POLICY "Service role can manage product images" ON storage.objects IS 
'Service role có thể quản lý tất cả hình ảnh sản phẩm (dùng cho đồng bộ)';

COMMENT ON FUNCTION check_tenant_access(text, text) IS 
'Helper function kiểm tra quyền truy cập tenant cho file path';

COMMENT ON VIEW user_product_images IS 
'View cho phép user xem danh sách hình ảnh sản phẩm của tenant họ';
