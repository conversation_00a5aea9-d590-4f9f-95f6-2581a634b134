-- Migration: Cậ<PERSON> nhật cấu hình mặc định delay_time cho tin nhắn hình ảnh
-- Đ<PERSON><PERSON> bảo delay_time mặc định là 10 giây để chờ user nhập thêm câu hỏi tư vấn

-- 1. <PERSON><PERSON><PERSON> nhật default value cho delay_time trong bảng chatbot_configurations
ALTER TABLE chatbot_configurations 
ALTER COLUMN delay_time SET DEFAULT 10;

-- 2. C<PERSON><PERSON> nhật các record hiện tại có delay_time = 0 thành 10 giây
UPDATE chatbot_configurations 
SET delay_time = 10, updated_at = NOW()
WHERE delay_time = 0 OR delay_time IS NULL;

-- 3. Thêm comment để giải thích
COMMENT ON COLUMN chatbot_configurations.delay_time IS 
'Thời gian delay (giây) trước khi xử lý tin nhắn. Mặc định 10s cho tin nhắn hình ảnh để chờ user nhập thêm câu hỏi tư vấn. Đặt 0 để xử lý ngay lập tức.';

-- 4. Tạo function để tự động set delay_time cho chatbot mới
CREATE OR REPLACE FUNCTION set_default_chatbot_config()
RETURNS TRIGGER AS $$
BEGIN
  -- Đảm bảo delay_time có giá trị mặc định hợp lệ
  IF NEW.delay_time IS NULL OR NEW.delay_time < 0 THEN
    NEW.delay_time := 10;
  END IF;
  
  -- Đảm bảo delay_time không vượt quá 30 giây
  IF NEW.delay_time > 30 THEN
    NEW.delay_time := 30;
  END IF;
  
  -- Set updated_at
  NEW.updated_at := NOW();
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 5. Tạo trigger để tự động áp dụng cấu hình mặc định
DROP TRIGGER IF EXISTS trigger_set_default_chatbot_config ON chatbot_configurations;
CREATE TRIGGER trigger_set_default_chatbot_config
  BEFORE INSERT OR UPDATE ON chatbot_configurations
  FOR EACH ROW
  EXECUTE FUNCTION set_default_chatbot_config();

-- 6. Thêm constraint để đảm bảo delay_time trong khoảng hợp lệ
ALTER TABLE chatbot_configurations 
ADD CONSTRAINT check_delay_time_range 
CHECK (delay_time >= 0 AND delay_time <= 30);

-- 7. Tạo index để tối ưu hóa query theo delay_time
CREATE INDEX IF NOT EXISTS idx_chatbot_configurations_delay_time 
ON chatbot_configurations(delay_time);

-- Comments để giải thích migration
COMMENT ON CONSTRAINT check_delay_time_range ON chatbot_configurations IS 
'Đảm bảo delay_time trong khoảng 0-30 giây. 0 = xử lý ngay lập tức, >0 = delay để chờ user nhập thêm câu hỏi';

COMMENT ON FUNCTION set_default_chatbot_config() IS 
'Tự động set cấu hình mặc định cho chatbot: delay_time=10s, updated_at=NOW()';
