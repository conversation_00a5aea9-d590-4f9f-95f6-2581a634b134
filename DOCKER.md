# Hướng dẫn sử dụng Docker vớ<PERSON> Chatbot AI Backend

## Giới thiệu

Tài liệu này hướng dẫn cách sử dụng Docker để chạy Chatbot AI Backend cho Thọ Trần Shop. Chúng tôi sử dụng Docker để đảm bảo môi trường chạy ứng dụng nhất quán và dễ dàng triển khai.

## Y<PERSON>u cầu

- Docker và Docker Compose đã được cài đặt
- Các biến môi trường cần thiết (xem phần Cấu hình)

## C<PERSON>u hình

Trước khi chạy ứng dụng, bạn cần cấu hình các biến môi trường. Có hai cách:

### Cách 1: Sử dụng file .env

Sao chép file `.env.example` thành `.env` và điền các giá trị cần thiết:

```bash
cp .env.example .env
```

### Cách 2: Cung cấp biến môi trường trực tiếp

Bạn có thể cung cấp biến môi trường trực tiếp khi chạy docker-compose:

```bash
OPENAI_API_KEY=your_key GOOGLE_API_KEY=your_key docker-compose up -d
```

## Chạy ứng dụng

### Khởi động tất cả dịch vụ

```bash
docker-compose up -d
```

Lệnh này sẽ khởi động:
- Ứng dụng Express (cổng 3000)
- Dragonfly (Redis alternative, cổng 6379)

### Chỉ khởi động ứng dụng Express

```bash
docker-compose up -d app
```

### Xem logs

```bash
# Xem logs của tất cả dịch vụ
docker-compose logs -f

# Chỉ xem logs của ứng dụng Express
docker-compose logs -f app
```

## Build lại image

Nếu bạn thay đổi mã nguồn và muốn build lại image:

```bash
docker-compose build app
```

## Dừng ứng dụng

```bash
# Dừng tất cả dịch vụ
docker-compose down

# Dừng và xóa volumes (dữ liệu sẽ bị mất)
docker-compose down -v
```

## Cấu trúc Docker

### Dockerfile

Chúng tôi sử dụng multi-stage build để tối ưu hóa kích thước image:

1. **Stage 1 (builder)**: Build ứng dụng từ mã nguồn
2. **Stage 2 (production)**: Tạo image production chỉ với các file cần thiết

### docker-compose.yml

File này định nghĩa các dịch vụ cần thiết để chạy ứng dụng:

- **app**: Ứng dụng Express
- **dragonfly**: Thay thế cho Redis, sử dụng cho BullMQ

## Khắc phục sự cố

### Không thể kết nối đến Dragonfly

Kiểm tra logs của Dragonfly:

```bash
docker-compose logs dragonfly
```

### Ứng dụng không khởi động

Kiểm tra logs của ứng dụng:

```bash
docker-compose logs app
```

### Xóa và tạo lại tất cả

Nếu bạn gặp vấn đề không thể giải quyết, hãy thử xóa tất cả và bắt đầu lại:

```bash
docker-compose down -v
docker-compose build --no-cache
docker-compose up -d
```

## Triển khai lên môi trường production

Khi triển khai lên môi trường production, bạn nên:

1. Sử dụng Docker Swarm hoặc Kubernetes để quản lý container
2. Cấu hình các biến môi trường thông qua secrets hoặc config maps
3. Sử dụng persistent volumes cho dữ liệu quan trọng
4. Cấu hình reverse proxy (như Nginx) phía trước ứng dụng
5. Thiết lập monitoring và logging
