# Loma Bag Workflow - Tóm tắt triển khai

## 🎯 Mục tiêu đã hoàn thành

Đã xây dựng thành công **Loma Bag Workflow** với logic như yêu cầu:

1. ✅ **Agent phân tích keywords**: Tạo agent để phân tích 5 tin nhắn gần nhất + tin nhắn mới để tạo keywords tìm kiếm FAQ
2. ✅ **Tìm kiếm FAQ thông minh**: Tích hợp với Weaviate FAQ service để lấy tối đa 3 FAQ phù hợp nhất
3. ✅ **Context-aware response**: Sử dụng context từ FAQ để Loma Bag agent tạo response chính xác hơn

## 📁 Cấu trúc files đã tạo

```
src/
├── mastra/
│   ├── agents/
│   │   └── loma-customer-service.ts    # ✨ Thêm keywordAnalysisAgent
│   ├── workflows/
│   │   ├── index.ts                    # 🆕 Export workflows
│   │   ├── loma-bag-workflow.ts        # 🆕 Main workflow logic
│   │   └── README.md                   # 🆕 Documentation
│   └── index.ts                        # ✨ Đăng ký workflow
├── examples/
│   └── loma-bag-workflow-example.ts    # 🆕 Usage examples
└── test-workflow.ts                    # 🆕 Test script
```

## 🔧 Workflow Architecture

### Step 1: Keyword Analysis
- **Agent**: `keywordAnalysisAgent` (Gemini 2.0 Flash)
- **Input**: 5 tin nhắn gần nhất + tin nhắn mới
- **Output**: 3-5 keywords + main_topic + customer_intent
- **Fallback**: Tạo keywords từ tin nhắn mới nếu agent fail

### Step 2: FAQ Search
- **Service**: `searchFaqs` từ Weaviate
- **Logic**: Tìm kiếm với từng keyword, limit 2 FAQ/keyword
- **Output**: Tối đa 3 FAQ có score cao nhất (loại bỏ duplicate)
- **Filtering**: Theo tenant_id và bot_id

### Step 3: Response Generation
- **Agent**: `lomaBagAgent` (Gemini 2.5 Flash Lite)
- **Context**: FAQ results được format thành context
- **Fallback**: Chạy agent không context nếu có lỗi

## 🚀 Cách sử dụng

### 1. Qua code
```typescript
import { mastra } from "./mastra";

const workflowRun = mastra.getWorkflow("lomaBagWorkflow").createRun();
const result = await workflowRun.start({
  inputData: {
    recentMessages: [...],
    newMessage: "Giá túi canvas size S bao nhiêu?",
    tenant_id: "your-tenant-id",
    bot_id: "your-bot-id"
  }
});
```

### 2. Qua API
```bash
POST /api/workflows/lomaBagWorkflow/run
```

### 3. Test
```bash
npx tsx src/test-workflow.ts
```

## 📊 Input/Output Schema

### Input
```typescript
{
  recentMessages: Array<{role, content, timestamp}>,
  newMessage: string,
  tenant_id: string,
  bot_id?: string
}
```

### Output
```typescript
{
  response: string,              // Câu trả lời cuối cùng
  keywords_used: string[],       // Keywords đã dùng
  faq_context: Array<{           // FAQ context
    topic: string,
    content: string,
    score?: number
  }>,
  main_topic: string,            // Chủ đề chính
  customer_intent: string        // Ý định khách hàng
}
```

## 🛡️ Error Handling

### Robust Fallbacks
1. **Keyword Analysis fail** → Tạo keywords từ tin nhắn mới
2. **FAQ Search fail** → Tiếp tục với empty context
3. **Agent Response fail** → Fallback response hoặc error message

### Graceful Degradation
- Workflow vẫn hoạt động ngay cả khi một số components fail
- Luôn trả về response, không bao giờ crash

## 🔧 Configuration

### Tuning Parameters
- **Keywords limit**: 3-5 keywords (có thể điều chỉnh)
- **FAQ per keyword**: 2 (có thể tăng)
- **Max FAQ context**: 3 (có thể điều chỉnh)
- **Recent messages**: 5 tin nhắn (có thể thay đổi)

### Environment Variables
```env
GOOGLE_GENERATIVE_AI_API_KEY=your-key
WEAVIATE_URL=your-weaviate-url
WEAVIATE_API_KEY=your-key
PG_CONNECTION_STRING=postgresql://...
```

## 🎯 Lợi ích đạt được

### 1. Context-Aware Responses
- Agent hiểu được ngữ cảnh cuộc hội thoại
- Sử dụng thông tin FAQ để trả lời chính xác hơn
- Tránh việc mất bối cảnh trong conversation

### 2. Intelligent FAQ Search
- Tự động tạo keywords phù hợp
- Tìm kiếm thông minh với multiple keywords
- Ranking và filtering theo relevance

### 3. Scalable Architecture
- Workflow có thể mở rộng thêm steps
- Support multi-tenant và bot-specific
- Easy to monitor và debug

### 4. Production Ready
- Comprehensive error handling
- Performance optimized
- Detailed logging và monitoring

## 📈 Next Steps

### Có thể mở rộng thêm:
1. **Caching layer** cho FAQ search results
2. **A/B testing** cho different keyword strategies
3. **Analytics** để track workflow performance
4. **Custom processors** cho specific use cases
5. **Integration** với các external services khác

## 🧪 Testing

Đã tạo sẵn:
- `src/test-workflow.ts`: Basic test script
- `src/examples/loma-bag-workflow-example.ts`: Detailed examples
- Error scenarios testing trong workflow

## 📚 Documentation

- **README.md**: Chi tiết trong `src/mastra/workflows/README.md`
- **Examples**: Trong `src/examples/`
- **API docs**: Auto-generated từ Mastra

---

**Workflow đã sẵn sàng sử dụng!** 🎉

Bạn có thể test ngay bằng cách chạy `npx tsx src/test-workflow.ts` hoặc tích hợp vào chatbot system hiện tại.
