# Mooly Chatbot AI Backend - Setup Guide

## 🚀 Cài đặt nhanh

### 1. Cài đặt dependencies
```bash
yarn install
```

### 2. <PERSON><PERSON>u hình môi trường
Tạo file `.env` với các biến môi trường cần thiết:

```bash
# Server Configuration
PORT=3003
NODE_ENV=development

# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/mooly_db
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Weaviate Configuration
WEAVIATE_URL=http://localhost:8080

# AI Configuration
OPENAI_API_KEY=your-openai-api-key
```

### 3. Chạy dự án

#### Development
```bash
yarn dev
```

#### Production
```bash
yarn build
yarn start
```

## 📁 Cấu trúc dự án đã được đơn giản hóa

- **`yarn dev`**: Chạy toàn bộ server trong development mode
- **`yarn start`**: Chạy server trong production mode
- **`yarn build`**: Build dự án cho production
- **`yarn test`**: Chạy tests

## 🔗 API Endpoints

- **Health Check**: `GET /health`
- **Main API**: `/api/*`
- **Homepage**: `GET /`

## 🎯 Các thay đổi chính

1. **Đơn giản hóa scripts**: Chỉ còn `dev` và `start`
2. **Tích hợp tất cả services**: Express + Mastra trong một server duy nhất
3. **Loại bỏ BullMQ complexity**: Không còn workers riêng biệt
4. **Cấu trúc rõ ràng**: Một file server.ts duy nhất để quản lý toàn bộ

## 🛠️ Troubleshooting

### Lỗi kết nối database
- Kiểm tra `DATABASE_URL` và `SUPABASE_*` trong file `.env`

### Lỗi kết nối Weaviate
- Đảm bảo Weaviate server đang chạy
- Kiểm tra `WEAVIATE_URL` trong file `.env`

### Port đã được sử dụng
- Thay đổi `PORT` trong file `.env` hoặc kill process đang sử dụng port 3003 