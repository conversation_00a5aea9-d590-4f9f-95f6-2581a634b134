const axios = require('axios');

const token = '9F7DC2D4C0992505DA146E421A730C12AB4B1BDD7DB406A556C7D07484F17CF2';

async function test() {
  try {
    const response = await axios.get('https://apis.haravan.com/com/products.json', {
      params: { limit: 1 },
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bear<PERSON> ${token}`
      }
    });

    const product = response.data.products[0];
    console.log('Product ID:', product.id);
    console.log('Product Title:', product.title);
    console.log('Product Price:', product.price);
    console.log('Product Compare At Price:', product.compare_at_price);
    console.log('Variants Count:', product.variants?.length || 0);
    
    if (product.variants && product.variants.length > 0) {
      console.log('\nAll Variants:');
      product.variants.forEach((variant, index) => {
        console.log(`Variant ${index + 1}:`);
        console.log('- Variant ID:', variant.id);
        console.log('- Variant Title:', variant.title);
        console.log('- Variant Price:', variant.price);
        console.log('- Variant Compare At Price:', variant.compare_at_price);
        console.log('- Option1:', variant.option1);
        console.log('- Option2:', variant.option2);
        console.log('- Option3:', variant.option3);
        console.log('---');
      });
    }
    
  } catch (error) {
    console.error('Error:', error.message);
  }
}

test();
